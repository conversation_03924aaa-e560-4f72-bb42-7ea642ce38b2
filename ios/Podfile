ENV['RCT_NEW_ARCH_ENABLED'] = '1'

 def node_require(script)
   require Pod::Executable.execute_command('node', ['-p',
     "require.resolve(
       '#{script}',
       {paths: [process.argv[1]]},
     )", __dir__]).strip
end

node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')


platform :ios, '16.0'
project 'scorescast.xcodeproj'
prepare_react_native_project!

setup_permissions([
  'AppTrackingTransparency',
])

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end


target 'scorescast' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    :fabric_enabled => true, # Ensure this is set to true
    :hermes_enabled => true  # Ensure Hermes is enabled
  )


  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  end
end

target 'WonderPushNotificationServiceExtension' do
  platform :ios, '16.0'
  pod 'WonderPushExtension', '~> 4.0'
end
