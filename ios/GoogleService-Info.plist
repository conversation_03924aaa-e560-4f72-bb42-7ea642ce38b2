<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>990092887088-8kub9qgcmk9r5nrilumb63nt820qjba1.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.990092887088-8kub9qgcmk9r5nrilumb63nt820qjba1</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>990092887088-dv5af2nd2l6s3nvti50hfe479bjc0hap.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyATlSyRbDJqR7AjHq_wnHMm3MdtIGMcwwQ</string>
	<key>GCM_SENDER_ID</key>
	<string>990092887088</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.scorescast</string>
	<key>PROJECT_ID</key>
	<string>scorescast-326a7</string>
	<key>STORAGE_BUCKET</key>
	<string>scorescast-326a7.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:990092887088:ios:961dd3365ea0eec8c299f5</string>
</dict>
</plist>