import { IconName } from '@scorescast/design-system';
import { Screens, Navigators } from '../navigator/navigator';
import { i18next } from '@scorescast/translations';
import { SocialLoginType } from '@scorescast/formatters-constants';
import { openLink } from './open-link';
import { UserRoles } from '@scorescast/formatters-constants/src/models/user';
const capitalizeFirstLetter = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const mapProfileMenuItems = (navigation: any, roles: UserRoles[]) => {
  return [
    {
      section: 'single',
      items: [
        {
          icon: IconName.SETTINGS_ICON,
          title: i18next.t('PROFILE.ITEMS.DETAILS'),
          isHighlighted: false,
          onPress: () => navigation.navigate(Screens.DetailsScreen),
        },
      ],
    },
    {
      section: 'single',
      items: [
        {
          icon: IconName.FRAME,
          hidden:
            !roles.find((role) =>
              [
                UserRoles.CLUB_ADMIN,
                UserRoles.SUPER_ADMIN,
                UserRoles.TEAM_ADMIN,
              ].includes(role)
            ),
          title: i18next.t('PROFILE.ITEMS.MANAGE_SECTION'),
          isHighlighted: false,
          onPress: () =>
            navigation.navigate(Navigators.AdminNavigator, {
              screen: Screens.AdminManagementScreen,
            }),
        },
      ],
    },
    {
      section: 'grouped',
      items: [
        {
          icon: IconName.HUMAN,
          title: i18next.t('PROFILE.ITEMS.FOLLOWING'),
          isHighlighted: false,
          onPress: () => navigation.navigate(Screens.FollowingScreen),
        },
        {
          icon: IconName.NOTIFICATION_ICON,
          title: i18next.t('PROFILE.ITEMS.NOTIFICATIONS'),
          isHighlighted: false,
          onPress: () => navigation.navigate(Screens.NotificationsScreen),
        },
      ],
    },
    {
      section: 'single',
      items: [
        {
          icon: IconName.HUMAN,
          title: i18next.t('PROFILE.ITEMS.ABOUT'),
          isHighlighted: false,
          onPress: () => navigation.navigate(Screens.AboutScreen),
        },
      ],
    },
  ];
};

export const mapProfileDetails = (
  loginType: string,
  clearToken: () => void
) => {
  const icon =
    loginType === SocialLoginType.FACEBOOK
      ? IconName.FACEBOOK
      : loginType === SocialLoginType.GOOGLE
        ? IconName.GOOGLE
        : IconName.APPLE;
  const title = i18next.t('DETAILS.LINK_ACCOUNT_TYPE', {
    type: capitalizeFirstLetter(loginType),
  });
  return [
    {
      section: 'single',
      items: [
        {
          icon: icon,
          title: title,
          isHighlighted: false,
          hideArrow: true,
          withSeparator: false,
          disabled: true,
        },
      ],
    },

    {
      section: 'single',
      items: [
        {
          icon: IconName.LOGOUT_ICON,
          title: i18next.t('DETAILS.SIGN_OUT'),
          isHighlighted: true,
          onPress: () => clearToken(),
        },
      ],
    },
  ];
};

export const mapAboutMenuItems = (onPressDeleteAccount: () => void) => {
  return [
    {
      section: 'single',
      items: [
        {
          title: i18next.t('ABOUT.REPORT_A_BUG'),
          isHighlighted: false,
          onPress: () => openLink('https://www.scorescast.com/report-a-bug'),
        },
      ],
    },
    {
      section: 'grouped',
      items: [
        {
          title: i18next.t('ABOUT.PRIVACY_STATEMENT'),
          isHighlighted: false,
          onPress: () => openLink('https://www.scorescast.com/privacy-policy'),
        },
        {
          title: i18next.t('ABOUT.TERMS_OF_SERVICE'),
          isHighlighted: false,
          onPress: () =>
            openLink('https://www.scorescast.com/terms-of-service'),
        },
        {
          title: i18next.t('ABOUT.COMMUNITY_GUIDELINES'),
          isHighlighted: false,
          onPress: () => openLink('https://www.scorescast.com/about'),
        },
        {
          title: i18next.t('ABOUT.CREDITS'),
          isHighlighted: false,
          onPress: () => openLink('https://www.scorescast.com/credits'),
        },
      ],
    },
    {
      section: 'single',
      items: [
        {
          icon: IconName.DELETE,
          title: i18next.t('ABOUT.REQUEST_ACCOUNT_DELETION'),
          isHighlighted: true,
          onPress: onPressDeleteAccount,
        },
      ],
    },
  ];
};
