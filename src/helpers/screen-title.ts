import { MatchPossibleStatus } from '@scorescast/formatters-constants/src/constants/match';
import { i18next } from '@scorescast/translations';

export const getHeaderTitle = (status: MatchPossibleStatus) => {
  let _status;
  if (
    ![MatchPossibleStatus['END'], MatchPossibleStatus['NS']].includes(status)
  ) {
    _status = 'STARTED';
  } else if ([MatchPossibleStatus['NS']].includes(status)) {
    _status = 'SCHEDULED';
  } else {
    _status = status;
  }

  return i18next.t(`MATCH_SCREEN.${_status.toUpperCase()}_TITLE`);
};
