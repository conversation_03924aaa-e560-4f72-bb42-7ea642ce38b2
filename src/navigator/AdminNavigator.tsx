import React from 'react';
import { useTheme } from '@scorescast/design-system';
import { SafeAreaView } from 'react-native';
import {
  createNativeStackNavigator,
  NativeStackNavigationProp,
} from '@react-navigation/native-stack';
import { PlayerActions } from '../screens/AdminManagement/PlayersScreen/PlayerActionScreen/PlayerActionScreen';

import { Screens } from './navigator';
import {
  AdminManagementScreen,
  EditClubScreen,
  EditTeamScreen,
  ClubScreen,
  TeamScreen,
} from '../screens/AdminManagement';

import TeamAgentsScreen from '../screens/AdminManagement/TeamAgentsScreen/TeamAgentsScreen';
import TeamAdminsScreen from '../screens/AdminManagement/TeamAdminsScreen/TeamAdminsScreen';
import PlayersScreen from '../screens/AdminManagement/PlayersScreen/PlayersScreen';
import PlayerActionScreen from '../screens/AdminManagement/PlayersScreen/PlayerActionScreen/PlayerActionScreen';
import TeamMatchesScreen from '../screens/AdminManagement/TeamMatchesScreen/TeamMatchesScreen';
import TeamFollowersScreen from '../screens/AdminManagement/TeamFollowersScreen/TeamFollowersScreen';
import EditMatchScreen from '../screens/AdminManagement/EditMatchScreen/EditMatchScreen';

type AdminStackParamList = {
  AdminManagementScreen: undefined;
  EditClubScreen: {
    clubId?: number;
    isAddMode?: boolean;
  };
  EditTeamScreen: {
    teamId?: number;
    isAddMode?: boolean;
    clubId?: number;
    clubName?: string;
    teamDetails?: {
      name: string;
      shortName: string;
      image: string;
      ageGroup: string;
    };
  };
  ClubScreen: {
    clubId: number;
    clubName?: string;
  };
  TeamScreen: {
    teamId: number;
    teamName?: string;
    clubId?: number;
    clubName?: string;
  };
  TeamAgentsScreen: {
    teamId: number;
  };
  TeamAdminsScreen: {
    teamId: number;
  };
  PlayersScreen: {
    teamId: number;
    teamName?: string;
  };
  PlayerActionScreen: {
    action: PlayerActions;
    player?: any;
    players?: any[];
    teamId: number;
  };
  TeamMatchesScreen: {
    teamId: number;
    teamUniqueId?: string;
    teamName?: string;
  };
  TeamFollowersScreen: {
    teamId: number;
    teamName?: string;
  };
  EditMatchScreen: {
    teamId: number;
    teamName: string;
    teamUniqueId?: string;
    matchId?: string;
    isEditMode?: boolean;
    matchDetails?: any;
  };
};

export type AdminNavigationProp =
  NativeStackNavigationProp<AdminStackParamList>;

const Stack = createNativeStackNavigator<AdminStackParamList>();

export const AdminNavigator = () => {
  const { colors } = useTheme();

  return (
    <SafeAreaView style={{ backgroundColor: colors.neutral900, flex: 1 }}>
      <Stack.Navigator
        initialRouteName={Screens.AdminManagementScreen}
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen
          name={Screens.AdminManagementScreen}
          component={AdminManagementScreen}
        />
        <Stack.Screen
          name={Screens.EditClubScreen}
          component={EditClubScreen}
        />
        <Stack.Screen
          name={Screens.EditTeamScreen}
          component={EditTeamScreen}
        />
        <Stack.Screen name={Screens.ClubScreen} component={ClubScreen} />
        <Stack.Screen name={Screens.TeamScreen} component={TeamScreen} />
        <Stack.Screen
          name={Screens.TeamAgentsScreen}
          component={TeamAgentsScreen}
        />
        <Stack.Screen
          name={Screens.TeamAdminsScreen}
          component={TeamAdminsScreen}
        />
        <Stack.Screen name={Screens.PlayersScreen} component={PlayersScreen} />
        <Stack.Screen
          name={Screens.PlayerActionScreen}
          component={PlayerActionScreen}
        />
        <Stack.Screen
          name={Screens.TeamMatchesScreen}
          component={TeamMatchesScreen}
        />
        <Stack.Screen
          name={Screens.TeamFollowersScreen}
          component={TeamFollowersScreen}
        />
        <Stack.Screen
          name={Screens.EditMatchScreen}
          component={EditMatchScreen}
        />
      </Stack.Navigator>
    </SafeAreaView>
  );
};
