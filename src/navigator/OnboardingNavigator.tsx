import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

//screens
import OnboardingScreen from '../screens/Onboarding/OnboardingScreen';

//utils
import { Screens } from './navigator';

export type OnboardingStackParamList = {
  OnboardingScreen: { step: number };
};

const Stack = createNativeStackNavigator<OnboardingStackParamList>();

export const OnboardingNavigator = () => {
  return (
    <Stack.Navigator
      initialRouteName={Screens.OnboardingScreen}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name={Screens.OnboardingScreen}
        component={OnboardingScreen}
      />
    </Stack.Navigator>
  );
};
