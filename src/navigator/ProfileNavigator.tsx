import React from 'react';
import { useTheme } from '@scorescast/design-system';
import { SafeAreaView } from 'react-native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

//screens
import ProfileScreen from '../screens/Profile/ProfileScreen/ProfileScreen';
import DetailsScreen from '../screens/Profile/DetailsScreen/DetailsScreen';
import NotificationsScreen from '../screens/Profile/NotificationsScreen/NotificationsScreen';
import FollowingScreen from '../screens/Profile/FollowingScreen/FollowingScreen';
import AboutScreen from '../screens/Profile/AboutScreen/AboutScreen';

import { AdminNavigator } from './AdminNavigator';

import { Screens, Navigators } from './navigator';

const Stack = createNativeStackNavigator();

export const ProfileNavigator = () => {
  const { colors } = useTheme();

  return (
    <SafeAreaView style={{ backgroundColor: colors.neutral900, flex: 1 }}>
      <Stack.Navigator
        initialRouteName={Screens.ProfileScreen}
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name={Screens.ProfileScreen} component={ProfileScreen} />
        <Stack.Screen name={Screens.DetailsScreen} component={DetailsScreen} />
        <Stack.Screen
          name={Screens.NotificationsScreen}
          component={NotificationsScreen}
        />
        <Stack.Screen
          name={Screens.FollowingScreen}
          component={FollowingScreen}
        />
        <Stack.Screen name={Screens.AboutScreen} component={AboutScreen} />
        <Stack.Screen
          name={Navigators.AdminNavigator}
          component={AdminNavigator}
        />
      </Stack.Navigator>
    </SafeAreaView>
  );
};
