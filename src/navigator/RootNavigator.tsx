import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

//screens
import LoadingScreen from '../screens/Onboarding/LoadingScreen';

//navigators
import BottomBarNavigator from './BottomBarNavigator';
import { OnboardingNavigator } from './OnboardingNavigator';

//utils
import { Screens, Navigators } from './navigator';
import { useAuthStore } from '@scorescast/http-clients';

export type RootStackParamList = {
  OnboardingNavigator: { step: number };
  BottomTabNavigator: undefined;
  LoadingScreen: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const AuthenticatedNavigator = () => {
  return (
    <>
      <Stack.Screen
        name={Navigators.BottomTabNavigator}
        component={BottomBarNavigator}
      />
    </>
  );
};

const UnAuthenticatedNavigator = () => {
  return (
    <>
      <Stack.Screen
        name={Navigators.OnboardingNavigator}
        component={OnboardingNavigator}
      />
    </>
  );
};

export const RootNavigator = () => {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  return (
    <Stack.Navigator
      initialRouteName={Screens.LoadingScreen}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name={Screens.LoadingScreen} component={LoadingScreen} />
      {isAuthenticated ? AuthenticatedNavigator() : UnAuthenticatedNavigator()}
    </Stack.Navigator>
  );
};
