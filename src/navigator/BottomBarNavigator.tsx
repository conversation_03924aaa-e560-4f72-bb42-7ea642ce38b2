import {
  BottomTabBarProps,
  createBottomTabNavigator,
} from '@react-navigation/bottom-tabs';
import React, { useCallback } from 'react';

//components
import BottomBar from '../components/BottomBar/BottomBar';

//navigators
import { HomeNavigator } from './HomeNavigator';
import { ProfileNavigator } from './ProfileNavigator';

//screens
// import OngoingScreen from '../screens/Ongoing/OngoingScreen';

//utils
import { Navigators, Screens } from './navigator';
const Tab = createBottomTabNavigator();

const BottomBarNavigator = () => {
  const renderTabBar = useCallback(
    ({ state, navigation: bottomTabNavigation }: BottomTabBarProps) => {
      const currentRoute = state.routes[state.index];

      if (currentRoute.state) {
        const nestedState = currentRoute.state;
        const nestedRoute = nestedState.routes[nestedState.index || 0];

        const hiddenScreens = [Screens.AgentScreen, Navigators.AdminNavigator];

        if (hiddenScreens.includes(nestedRoute.name as any)) {
          return <></>;
        }
      }
      return (
        <BottomBar
          selectedIndex={state.index}
          navigation={bottomTabNavigation}
        />
      );
    },
    []
  );

  return (
    <Tab.Navigator screenOptions={{ headerShown: false }} tabBar={renderTabBar}>
      <Tab.Screen name={Navigators.HomeNavigator} component={HomeNavigator} />
      {/* <Tab.Screen name={Screens.OngoingScreen} component={OngoingScreen} /> to be added later*/}
      <Tab.Screen
        name={Navigators.ProfileNavigator}
        component={ProfileNavigator}
      />
    </Tab.Navigator>
  );
};

export default BottomBarNavigator;
