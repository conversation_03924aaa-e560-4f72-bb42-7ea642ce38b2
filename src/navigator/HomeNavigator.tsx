import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '@scorescast/design-system';
import { MatchPossibleStatus } from '@scorescast/formatters-constants';
//screens
import HomeScreen from '../screens/Home/HomeScreen/HomeScreen';
import MatchScreen from '../screens/MatchScreen/MatchScreen';
import TeamScreen from '../screens/Home/TeamScreen';
import AgentScreen from '../screens/Agent/AgentScreen';

//utils
import { SafeAreaView } from 'react-native';
import { Screens } from './navigator';

export type HomeStackParamList = {
  HomeScreen: undefined;
  TeamScreen: undefined;
  MatchScreen: { matchId: string; status: typeof MatchPossibleStatus };
  AgentScreen: {
    matchId: string;
    status: typeof MatchPossibleStatus;
    teamIds: { teamUniqueId: string; id: number };
  };
};

const Stack = createNativeStackNavigator<HomeStackParamList>();

export const HomeNavigator = () => {
  const { colors } = useTheme();

  return (
    <SafeAreaView style={{ backgroundColor: colors.neutral900, flex: 1 }}>
      <Stack.Navigator
        initialRouteName={Screens.HomeScreen}
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name={Screens.HomeScreen} component={HomeScreen} />
        <Stack.Screen name={Screens.TeamScreen} component={TeamScreen} />
        <Stack.Screen name={Screens.MatchScreen} component={MatchScreen} />
        <Stack.Screen name={Screens.AgentScreen} component={AgentScreen} />
      </Stack.Navigator>
    </SafeAreaView>
  );
};
