import { useNavigation } from '@react-navigation/native';
import {
  <PERSON><PERSON>,
  ProfileMenu,
  ViewContainer,
  useTheme,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { useAuthStore, storage, STORAGE_KEYS } from '@scorescast/http-clients';
import React from 'react';
import { Text } from 'react-native';
import { detailsScreenStyles } from './DetailsScreen.styles';
import { mapProfileDetails } from '../../../helpers/profile-menu';
import { UserAvatar } from '@scorescast/design-system';
const DetailsScreen = () => {
  const navigation = useNavigation();
  const { user, clearToken } = useAuthStore();
  const { colors, spacings, typography } = useTheme();
  const styles = detailsScreenStyles(colors, spacings, typography);
  const loginType = storage.getString(STORAGE_KEYS.LOGIN_TYPE);

  return (
    <ViewContainer>
      <Header
        title={i18next.t('DETAILS.TITLE')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />
      <UserAvatar
        withName
        firstName={user.firstName}
        lastName={user.lastName}
        email={user.email}
        sizeStyleContainer={styles.avatarContainer}
        sizeStyleText={styles.avatarText}
      />
      <Text style={styles.linkedAccountsText}>
        {i18next.t('DETAILS.LINK_ACCOUNT')}
      </Text>
      <ProfileMenu menuItems={mapProfileDetails(loginType!, clearToken)} />
    </ViewContainer>
  );
};

export default DetailsScreen;
