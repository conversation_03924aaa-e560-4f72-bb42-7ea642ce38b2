import {
  ColorStyles,
  SpacingStyles,
  TypographyStyles,
} from '@scorescast/design-system';
import { StyleSheet } from 'react-native';

export const detailsScreenStyles = (
  colors: typeof ColorStyles,
  spacings: typeof SpacingStyles,
  typography: typeof TypographyStyles
) =>
  StyleSheet.create({
    avatarContainer: {
      alignSelf: 'center',
      marginTop: spacings.xxxxlarge,
      height: 100,
      width: 100,
      borderRadius: 50,
    },
    avatarText: {
      ...typography['large-bold'],
    },
    linkedAccountsText: {
      ...typography['small-semibold'],
      color: colors.inverse900,
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.medium,
    },
  });
