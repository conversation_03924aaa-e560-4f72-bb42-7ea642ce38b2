import React, { useCallback, useMemo, useRef } from 'react';
import { Text } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import {
  <PERSON><PERSON>,
  ViewContainer,
  ProfileMenu,
  useTheme,
  ConfirmBottomSheet,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { useAuthStore, useUser } from '@scorescast/http-clients';
import { useNavigation } from '@react-navigation/native';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { mapAboutMenuItems } from '../../../helpers/profile-menu';
import { aboutScreenStyles } from './AboutScreen.styles';

const AboutScreen = () => {
  const navigation = useNavigation();
  const { spacings, colors, typography } = useTheme();
  const styles = aboutScreenStyles(spacings, colors, typography);
  const confirmBottomSheet = useRef<BottomSheetModal>(null);
  const { user } = useAuthStore();
  const { deleteUser } = useUser();

  const appVersion = useMemo(() => {
    const appVersion = DeviceInfo.getVersion();
    const buildNumber = DeviceInfo.getBuildNumber();
    return ` ${i18next.t('ABOUT.VERSION')}: ${appVersion} (${buildNumber})`;
  }, []);

  const onPressDeleteAccount = useCallback(() => {
    confirmBottomSheet.current?.present();
  }, [confirmBottomSheet]);

  return (
    <ViewContainer>
      <Header
        title={i18next.t('ABOUT.TITLE')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />
      <ProfileMenu
        containerStyle={styles.itemsContainer}
        menuItems={mapAboutMenuItems(onPressDeleteAccount)}
      />
      <Text style={styles.appVersion}>{appVersion}</Text>
      <ConfirmBottomSheet
        ref={confirmBottomSheet}
        onBtnPress={() => deleteUser({ email: user?.email })}
        titleText={i18next.t('ABOUT.DELETE_ACCOUNT.TITLE')}
        descriptionText={i18next.t('ABOUT.DELETE_ACCOUNT.DESCRIPTION')}
        buttonText={i18next.t('ABOUT.DELETE_ACCOUNT.DELETE')}
      />
    </ViewContainer>
  );
};

export default AboutScreen;
