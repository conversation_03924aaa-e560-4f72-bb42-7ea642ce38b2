import { useNavigation } from '@react-navigation/native';
import {
  <PERSON><PERSON>,
  useTheme,
  ViewC<PERSON>r,
  ManageTeamButton,
  Error,
  Lotties,
  ConfirmBottomSheet,
  ManageTeamBottomSheet,
} from '@scorescast/design-system';
import { useTeam } from '@scorescast/http-clients';
import { i18next } from '@scorescast/translations';
import { Team, NOTIFICATION_KEYS } from '@scorescast/formatters-constants';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { ScrollView, Share, Text, View } from 'react-native';
import React, { useCallback, useRef, useState } from 'react';
import { followingScreenStyles } from './FollowingScreen.styles';
import { useNotifications } from '../../../hooks/useNotifications';
import { useAppsFlyer } from '../../../hooks/useAppsFlyer';

interface TeamWithNotificationStatus extends Team {
  isSubscribedForNotifications: boolean;
}

const FollowingScreen = () => {
  const navigation = useNavigation();
  const { followedTeams, refetchFollowedTeams, agentTeams, unfollowTeam } =
    useTeam(true);
  const { colors, spacings, typography } = useTheme();
  const confirmBottomSheet = useRef<BottomSheetModal>(null);
  const manageTeamBottomSheet = useRef<BottomSheetModal>(null);
  const [selectedTeam, setSelectedTeam] =
    useState<TeamWithNotificationStatus | null>(null);
  const {
    removePropertyValue,
    addPropertyValue,
    isTeamSubscribedForNotificaitons,
  } = useNotifications();
  const { generateShareTeamLink } = useAppsFlyer();
  const styles = followingScreenStyles(colors, spacings, typography);

  const onPressTeam = useCallback(
    async (team: Team) => {
      if (team) {
        const notificationStatus = await isTeamSubscribedForNotificaitons(
          team?.teamUniqueId as string
        );
        setSelectedTeam({
          ...team,
          isSubscribedForNotifications: notificationStatus,
        });
        setTimeout(() => {
          manageTeamBottomSheet.current?.present();
        });
      }
    },
    [isTeamSubscribedForNotificaitons]
  );

  const onDeleteTeam = useCallback(() => {
    if (selectedTeam) {
      manageTeamBottomSheet.current?.dismiss();
      setSelectedTeam({
        ...(selectedTeam as TeamWithNotificationStatus),
        isSubscribedForNotifications: false,
      });
      confirmBottomSheet.current?.present();
    }
  }, [selectedTeam]);

  const onConfirmDeleteTeam = useCallback(() => {
    if (selectedTeam?.teamUniqueId) {
      unfollowTeam(
        { teamUniqueId: selectedTeam?.teamUniqueId },
        {
          onSuccess: () => {
            removePropertyValue(
              NOTIFICATION_KEYS.FOLLOWED_TEAMS,
              selectedTeam?.teamUniqueId as string
            );
            refetchFollowedTeams();
          },
        }
      );
    }
    confirmBottomSheet.current?.dismiss();
  }, [selectedTeam, unfollowTeam, removePropertyValue, refetchFollowedTeams]);

  const onShareTeam = useCallback(async () => {
    if (selectedTeam) {
      const link = await generateShareTeamLink(
        selectedTeam?.teamUniqueId as string
      );
      Share.share({
        message: i18next.t('FOLLOWING.SHARE_TEAM.SMS', {
          teamName: selectedTeam?.name,
          link: link,
        }),
      });
    }
  }, [generateShareTeamLink, selectedTeam]);

  const onToggleNotifications = useCallback(() => {
    if (selectedTeam) {
      if (selectedTeam?.isSubscribedForNotifications) {
        removePropertyValue(
          NOTIFICATION_KEYS.FOLLOWED_TEAMS,
          selectedTeam?.teamUniqueId as string
        );
      } else {
        addPropertyValue(
          NOTIFICATION_KEYS.FOLLOWED_TEAMS,
          selectedTeam?.teamUniqueId as string
        );
      }
      setSelectedTeam({
        ...(selectedTeam as TeamWithNotificationStatus),
        isSubscribedForNotifications:
          !selectedTeam?.isSubscribedForNotifications,
      });
    }
  }, [removePropertyValue, addPropertyValue, selectedTeam]);

  const renderTeamList = useCallback((title: string, teams: Team[]) => {
    if (!teams || teams.length === 0) return null;
    return (
      <View style={styles.section}>
        <Text style={styles.title}>{title}</Text>
        {teams.map((team, index) => (
          <View
            style={[styles.item, index === 0 && styles.itemFirst]}
            key={index}
          >
            <ManageTeamButton
              team={team}
              onPressDelete={onDeleteTeam}
              onPressButton={onPressTeam}
              disabled={true}
            />
          </View>
        ))}
      </View>
    );
  }, []);

  return (
    <ViewContainer>
      <Header
        title={i18next.t('FOLLOWING.TITLE')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />
      {followedTeams && followedTeams?.data?.length === 0 ? (
        <Error
          loop={true}
          lottie={Lotties.empty}
          title={i18next.t('ERRORS.NO_FOLLOWED_TEAMS.TITLE')}
          description={i18next.t('ERRORS.NO_FOLLOWED_TEAMS.DESCRIPTION')}
        />
      ) : (
        <ScrollView
          style={styles.scrollViewContainer}
          bounces={false}
          showsVerticalScrollIndicator={false}
        >
          {renderTeamList(i18next.t('FOLLOWING.TEAMS'), followedTeams?.data)}
          {renderTeamList(
            i18next.t('FOLLOWING.AGENT_ACCESS'),
            agentTeams?.data
          )}
        </ScrollView>
      )}
      <ConfirmBottomSheet
        ref={confirmBottomSheet}
        onBtnPress={onConfirmDeleteTeam}
        titleText={i18next.t('FOLLOWING.DELETE_TEAM.TITLE')}
        descriptionText={i18next.t('FOLLOWING.DELETE_TEAM.DESCRIPTION')}
        buttonText={i18next.t('FOLLOWING.DELETE_TEAM.DELETE')}
      />
      {selectedTeam && (
        <ManageTeamBottomSheet
          ref={manageTeamBottomSheet}
          team={selectedTeam}
          onToggleNotifications={onToggleNotifications}
          onShareTeam={onShareTeam}
          onDeleteTeam={onDeleteTeam}
        />
      )}
    </ViewContainer>
  );
};

export default FollowingScreen;
