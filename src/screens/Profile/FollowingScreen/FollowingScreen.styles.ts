import { StyleSheet } from 'react-native';
import {
  ColorStyles,
  SpacingStyles,
  TypographyStyles,
} from '@scorescast/design-system';

export const followingScreenStyles = (
  colors: typeof ColorStyles,
  spacings: typeof SpacingStyles,
  typography: typeof TypographyStyles
) =>
  StyleSheet.create({
    section: {
      marginTop: spacings.xxxxlarge,
    },
    scrollViewContainer: {
      marginTop: spacings.large,
    },
    title: {
      ...typography['medium-bold'],
      color: colors.inverse900,
    },
    item: {
      paddingBottom: spacings.medium,
    },
    itemFirst: {
      paddingTop: spacings.xlarge,
    },
  });
