import { useCallback } from 'react';
import { useNavigation } from '@react-navigation/native';
import {
  <PERSON>er,
  ViewContainer,
  NotificationOption,
  useTheme,
  Loader,
} from '@scorescast/design-system';
import { useNotificationsStore } from '@scorescast/http-clients';
import { i18next } from '@scorescast/translations';
import { NOTIFICATION_KEYS } from '@scorescast/formatters-constants';
import React from 'react';
import { View } from 'react-native';
import { notificationsScreenStyles } from './NotificationsScreen.styles';
import { useNotifications } from '../../../hooks/useNotifications';

const NotificationsScreen = () => {
  const navigation = useNavigation();
  const { spacings } = useTheme();
  const styles = notificationsScreenStyles(spacings);
  const { requestPermission, clearProperty } = useNotifications();
  const { notifications, setNotifications } = useNotificationsStore();

  const onToggleNotifications = useCallback(() => {
    if (!notifications) {
      requestPermission();
    } else {
      setNotifications(!notifications);
      clearProperty(NOTIFICATION_KEYS.FOLLOWED_TEAMS);
    }
  }, [notifications, requestPermission, clearProperty]);

  if (notifications === null) {
    return <Loader />;
  }

  return (
    <ViewContainer>
      <Header
        title={i18next.t('NOTIFICATIONS.TITLE')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />
      <View style={styles.container}>
        <NotificationOption
          title={i18next.t('NOTIFICATIONS.NOTIFICATIONS')}
          value={notifications}
          onToggle={onToggleNotifications}
          style={styles.notificationContainer}
        />
      </View>
    </ViewContainer>
  );
};

export default NotificationsScreen;
