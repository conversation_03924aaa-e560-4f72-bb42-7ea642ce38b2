import {
  <PERSON><PERSON>,
  useTheme,
  ProfileMenu,
  ViewContainer,
  UserAvatar,
  Loader,
  PremiumBadge,
  PremiumBottomSheet,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { useAuthStore, useUser } from '@scorescast/http-clients';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import React, { useEffect, useRef } from 'react';
import { profileScreenStyles } from './ProfileScreen.styles';
import { useNavigation } from '@react-navigation/native';
import { mapProfileMenuItems } from '../../../helpers/profile-menu';
const ProfileScreen = () => {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const { spacings, typography, colors } = useTheme();
  const styles = profileScreenStyles(colors, spacings, typography);
  const navigation = useNavigation();
  const { fetchUser, isFetchUserLoading } = useUser();
  const { user } = useAuthStore();

  useEffect(() => {
    fetchUser();
  }, []);

  if (!user || isFetchUserLoading) {
    return <Loader />;
  }

  return (
    <>
      <ViewContainer>
        <Header title={i18next.t('PROFILE.TITLE')} />
        <UserAvatar
          withName
          firstName={user.firstName}
          lastName={user.lastName}
          sizeStyleContainer={styles.avatarContainer}
          sizeStyleText={styles.avatarText}
        />
        <PremiumBadge
          onPress={() => bottomSheetRef.current?.present()}
          containerStyle={styles.premiumBadgeContainer}
        />
        <ProfileMenu
          menuItems={mapProfileMenuItems(navigation, user.roles)}
          containerStyle={styles.profileMenuContainer}
        />
      </ViewContainer>
      <PremiumBottomSheet
        ref={bottomSheetRef}
        onCallbackPressed={() => bottomSheetRef.current?.dismiss()}
      />
    </>
  );
};

export default ProfileScreen;
