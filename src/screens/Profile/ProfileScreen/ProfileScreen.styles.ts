import {
  SpacingStyles,
  TypographyStyles,
  ColorStyles,
} from '@scorescast/design-system';
import { StyleSheet } from 'react-native';

export const profileScreenStyles = (
  colors: typeof ColorStyles,
  spacings: typeof SpacingStyles,
  typography: typeof TypographyStyles
) =>
  StyleSheet.create({
    avatarContainer: {
      alignSelf: 'center',
      marginTop: spacings.xxxxlarge,
      height: 100,
      width: 100,
      borderRadius: 50,
    },
    avatarText: {
      ...typography['large-bold'],
    },
    profileMenuContainer: {
      marginTop: spacings.xlarge,
    },
    premiumBadgeContainer: {
      alignSelf: 'center',
      marginVertical: spacings.large,
    },
  });
