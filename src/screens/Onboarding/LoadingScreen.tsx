import { Loader } from '@scorescast/design-system';
import { storage, STORAGE_KEYS, useAuthStore } from '@scorescast/http-client';
import React, { useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import LottieSplashScreen from 'react-native-lottie-splash-screen';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Navigators, Screens } from '../../navigator/navigator';

const LoadingScreen = () => {
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  useEffect(() => {
    LottieSplashScreen.hide();
    const appLaunch = storage.getBoolean(STORAGE_KEYS.APP_LAUNCH);
    if (!isAuthenticated) {
      navigation.navigate(Navigators.OnboardingNavigator, {
        screen: Screens.OnboardingScreen,
        params: { step: appLaunch ? 4 : 0 },
      });
    } else {
      navigation.navigate(Navigators.BottomTabNavigator, {
        screen: Screens.HomeScreen,
      });
    }
  }, [isAuthenticated]);

  return <Loader />;
};

export default LoadingScreen;
