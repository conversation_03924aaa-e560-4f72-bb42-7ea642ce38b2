import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Onboarding, SocialLogin } from '@scorescast/design-system';
import { SocialLoginType } from '@scorescast/formatters-constants';
import { storage, STORAGE_KEYS } from '@scorescast/http-clients';
import { ONBOARDING_STEPS, OnboardingSteps } from '../../constants/onboarding';
import { OnboardingStackParamList } from '../../navigator/OnboardingNavigator';
import { Screens } from '../../navigator/navigator';
import { RouteProp } from '@react-navigation/native';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import useLogin from '../../hooks/useLogin';

type OnboardingScreenRouteProp = RouteProp<
  OnboardingStackParamList,
  Screens.OnboardingScreen
>;

interface Props {
  route: OnboardingScreenRouteProp;
}

const OnboardingScreen: React.FC<Props> = ({ route }) => {
  const { step } = route.params;
  const [currentStep, setCurrentStep] = useState<number>(step);
  const loginBottomSheetRef = useRef<BottomSheetModal>(null);
  const { login } = useLogin();

  const mapOnboardingStepToProps = useMemo(() => {
    switch (currentStep) {
      case 0:
        return OnboardingSteps.FIRST_STEP;
      case 1:
        return OnboardingSteps.SECOND_STEP;
      case 2:
        return OnboardingSteps.THIRD_STEP;
      case 3:
        return OnboardingSteps.FOURTH_STEP;
      case 4:
        return OnboardingSteps.FIFTH_STEP;
      default:
        return OnboardingSteps.FIRST_STEP;
    }
  }, [currentStep]);

  const onPress = useCallback(() => {
    if (currentStep > ONBOARDING_STEPS || currentStep === ONBOARDING_STEPS) {
      loginBottomSheetRef.current?.present();
    } else {
      storage.set(STORAGE_KEYS.APP_LAUNCH, true);
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, ONBOARDING_STEPS]);

  const onLogin = useCallback(
    async (type: typeof SocialLoginType) => {
      loginBottomSheetRef.current?.dismiss();
      login(type);
    },
    [login]
  );

  return (
    <>
      <Onboarding
        {...mapOnboardingStepToProps}
        totalSteps={ONBOARDING_STEPS}
        currentStep={currentStep}
        onPress={onPress}
      />
      <SocialLogin ref={loginBottomSheetRef} onLogin={onLogin} />
    </>
  );
};

export default OnboardingScreen;
