import {
  <PERSON><PERSON><PERSON>r,
  SearchTeam,
  Loader,
  AddTeamBottomSheet,
  QRCodeScannerBottomSheet,
} from '@scorescast/design-system';
import { useTeam } from '@scorescast/http-clients';
import { emitEvent, Events } from '@scorescast/formatters-constants';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import React, { useCallback, useRef } from 'react';
import { Teams } from '../../../components/Teams/Teams';

const HomeScreen = () => {
  const { followedTeams, isFollowedTeamsLoading } = useTeam(false, true);
  const searchTeamRef = useRef<BottomSheetModal>(null);
  const qrCodeScannerRef = useRef<BottomSheetModal>(null);

  const onCodeCompleted = useCallback((code: string) => {
    searchTeamRef.current?.dismiss();
    emitEvent(Events.SEARCH_TEAM_BY_ID, { teamUniqueId: code });
  }, []);

  const onQRCodeScanned = useCallback((code: string) => {
    qrCodeScannerRef.current?.dismiss();
    emitEvent(Events.SEARCH_TEAM_BY_ID, { teamUniqueId: code });
  }, []);

  const onQRCodeSearchPressed = useCallback(() => {
    searchTeamRef.current?.dismiss();
    qrCodeScannerRef.current?.present();
  }, [qrCodeScannerRef, searchTeamRef]);

  if (isFollowedTeamsLoading) {
    return <Loader />;
  }

  return (
    <>
      <ViewContainer>
        {followedTeams?.data.length > 0 ? (
          <Teams
            teams={followedTeams.data}
            onPressAddTeam={() => searchTeamRef.current?.present()}
          />
        ) : (
          <SearchTeam
            onCodeCompleted={onCodeCompleted}
            onQRCodeSearchPressed={onQRCodeSearchPressed}
          />
        )}
      </ViewContainer>
      <AddTeamBottomSheet
        ref={searchTeamRef}
        onCodeCompleted={onCodeCompleted}
        onQRCodeSearchPressed={onQRCodeSearchPressed}
      />
      <QRCodeScannerBottomSheet
        ref={qrCodeScannerRef}
        onQRCodeScanned={onQRCodeScanned}
      />
    </>
  );
};

export default HomeScreen;
