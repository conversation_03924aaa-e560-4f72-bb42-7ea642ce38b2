import React from 'react';
import { RouteProp } from '@react-navigation/native';
import { View } from 'react-native';
import { useTheme, Loader } from '@scorescast/design-system';
import {
  useRetrieveMatchDetails,
  useRetrievePlayersForTeam,
} from '@scorescast/http-client';
import { useKeepAwake } from '@sayem314/react-native-keep-awake';
import { Screens } from '../../navigator/navigator';
import { HomeStackParamList } from '../../navigator/HomeNavigator';
import { AgentProvider } from './AgentContext';
import AgentWrapper from './AgentWrapper';
import { agentScreenStyles } from './AgentScreen.styles';
type AgentScreenRouteProp = RouteProp<HomeStackParamList, Screens.AgentScreen>;

interface Props {
  route: AgentScreenRouteProp;
}

const AgentScreen: React.FC<Props> = ({ route }) => {
  const { matchId, status, teamIds } = route.params;
  const { colors } = useTheme();
  // Use our new controller hooks
  const { data: match, isLoading: isMatchLoading } = useRetrieveMatchDetails(
    {
      matchId: matchId,
    },
    {
      enabled: !!matchId,
    }
  );

  const { data: teamPlayersRetrieve } = useRetrievePlayersForTeam(
    {
      teamId: teamIds.id,
    },
    {
      enabled: !!teamIds.id,
    }
  );

  const styles = agentScreenStyles(colors);

  useKeepAwake();

  if (isMatchLoading || !match) {
    return <Loader />;
  }

  return (
    <View style={styles.container}>
      <AgentProvider
        match={match?.data}
        team={teamPlayersRetrieve?.data}
        teamId={teamIds.id}
      >
        <AgentWrapper match={match?.data} />
      </AgentProvider>
    </View>
  );
};

export default AgentScreen;
