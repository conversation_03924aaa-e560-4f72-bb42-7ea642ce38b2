import { Loader } from './../../../packages/design-system/components/Loader/Loader';
import Claim from './flows/before-start/claim/Claim';
import EditTeam from './flows/before-start/edit-team/EditTeam';
import PlayerAction from './flows/before-start/edit-team/player-action/PlayerAction';
import Kickoff from './flows/before-start/kickoff/Kickoff';
import LiveGame from './flows/in-play/live-game/LiveGame';
import Goal from './flows/in-play/goal/Goal';
import Foul from './flows/in-play/foul/Foul';
import FoulSelection from './flows/in-play/foul-selection/FoulSelection';
import FeedView from './flows/in-play/feed-view/FeedView';
import EndGame from './flows/after-play/end-game/EndGame';
import Penalties from './flows/in-play/penalties/Penalties';
import GoalSelection from './flows/in-play/goal-selection/GoalSelection';
import PenaltiesSelection from './flows/in-play/penalties-selection/PenaltiesSelection';
import { AgentViews } from './AgentCurrentState.config';

const agentConfig: { [key: string]: React.FC<any> } = {
  [AgentViews.CLAIM_VIEW]: Claim,
  [AgentViews.TEAM_MANAGEMENT_VIEW]: EditTeam,
  [AgentViews.PLAYER_ACTION_VIEW]: PlayerAction,
  [AgentViews.KICKOFF]: Kickoff,
  [AgentViews.P1_VIEW]: LiveGame,
  [AgentViews.P1_VIEW__4_PERIODS]: LiveGame,
  [AgentViews.P1_VIEW__1_PERIODS]: LiveGame,
  [AgentViews.P2_VIEW__4_PERIODS]: LiveGame,
  [AgentViews.HT_VIEW]: Kickoff,
  [AgentViews.HT_VIEW__4_PERIODS]: Kickoff,
  [AgentViews.P1_BREAK_VIEW]: Kickoff,
  [AgentViews.P3_BREAK_VIEW]: Kickoff,
  [AgentViews.FT_VIEW]: Kickoff,
  [AgentViews.FT_VIEW__1_PERIODS]: Kickoff,
  [AgentViews.ETHT_VIEW]: Kickoff,
  [AgentViews.END_VIEW]: EndGame,
  [AgentViews.P2_VIEW]: LiveGame,
  [AgentViews.P3_VIEW]: LiveGame,
  [AgentViews.P4_VIEW]: LiveGame,
  [AgentViews.ET_VIEW]: LiveGame,
  [AgentViews.ET1_VIEW]: LiveGame,
  [AgentViews.ET2_VIEW]: LiveGame,
  [AgentViews.AET_VIEW]: Kickoff,
  [AgentViews.PENS_VIEW]: Penalties,
  [AgentViews.FOUL__PENS_VIEW]: Penalties,
  [AgentViews.FOUL_VIEW]: Foul,
  [AgentViews.FOUL_VIEW_SELECTION]: FoulSelection,
  [AgentViews.FEED_VIEW]: FeedView,
  [AgentViews.GOAL_VIEW]: Goal,
  [AgentViews.GOAL_VIEW_SELECTION]: GoalSelection,
  [AgentViews.PENS_VIEW_SELECTION]: PenaltiesSelection,
  [AgentViews.FALLBACK_VIEW]: Loader,
};

export default agentConfig;
