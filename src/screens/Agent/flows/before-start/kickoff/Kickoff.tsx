import React, { useEffect, useRef, useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import {
  ConfirmBottomSheet,
  But<PERSON>,
  useTheme,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>Header,
  Separator,
  DraggableButton,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { useAgent, useLiveMatch } from '@scorescast/http-clients';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useNavigation } from '@react-navigation/native';
import { getHeaderTitle } from '../../../../../helpers/screen-title';
import { useAgentView } from '../../../AgentContext';
import { kickoffStyles } from './Kickoff.styles';
import { AgentViews } from '../../../AgentCurrentState.config';
import { useQueryClient } from '@tanstack/react-query';
import { MatchSetup } from '@scorescast/formatters-constants/src/models/match';

const Kickoff: React.FC = () => {
  const { spacings, typography, colors } = useTheme();
  const [headerTitle, setHeaderTitle] = useState('');
  const [isAsyncEvent, setIsAsyncEvent] = useState(false);
  const agentConfirmNextStep = useRef<BottomSheetModal>(null);
  const agentConfirmEndGame = useRef<BottomSheetModal>(null);
  const agentConfirmPenalties = useRef<BottomSheetModal>(null);
  const navigation = useNavigation<any>();
  const {
    agentView: {
      state: { match },
      options: {
        editTeamVisibility,
        kickoffVisibility,
        endGameVisibility,
        penaltyVisibility,
        nextStepAction,
        endGameAction,
        penaltiesAction,
      },
    },
    triggerAgentViewChange,
  } = useAgentView();
  const { createEvent } = useAgent();
  const queryClient = useQueryClient();
  const { refetchMatch } = useLiveMatch(match.id, match.status, queryClient);

  useEffect(() => {
    if (match?.status) {
      setHeaderTitle(getHeaderTitle(match.status));
    }
  }, [match]);

  const styles = kickoffStyles(typography, spacings, colors);

  return (
    <ViewContainer animated={true}>
      <Header
        title={headerTitle}
        withBackButton
        hasFeed
        onFeedPress={() =>
          triggerAgentViewChange({
            view: AgentViews.FEED_VIEW,
          })
        }
        onBackButtonPress={() => navigation.goBack()}
      />
      {match && (
        <MatchHeader
          match={match}
          containerStyle={styles.matchHeaderContainer}
        />
      )}
      <Separator containerStyle={styles.separator} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        bounces={false}
        disableScrollViewPanResponder={true}
        disableIntervalMomentum={true}
      >
        <Text style={[styles.infoDescription]}>
          {i18next.t(`AGENT.KICKOFF_MATCH.${match.status}.SHORT_DESCRIPTION`)}
        </Text>
        <View style={styles.buttonContainer}>
          {editTeamVisibility && (
            <Button
              title={i18next.t(
                `AGENT.KICKOFF_MATCH.${match.status}.EDIT_TEAM_CTA`
              )}
              onPress={() => {
                triggerAgentViewChange({
                  view: AgentViews.TEAM_MANAGEMENT_VIEW,
                });
              }}
            />
          )}
          {kickoffVisibility && (
            <>
              <DraggableButton
                label={i18next.t(
                  `AGENT.KICKOFF_MATCH.${match.matchSetup === MatchSetup.P1 ? `${MatchSetup.P1}__${match.status}` : match.status}.KICKOFF_CTA`
                )}
                onPress={() => agentConfirmNextStep.current?.present()}
              />
            </>
          )}
          {endGameVisibility && (
            <DraggableButton
              label={i18next.t(
                `AGENT.KICKOFF_MATCH.${match.status}.END_GAME_CTA`
              )}
              onPress={() => agentConfirmEndGame.current?.present()}
            />
          )}
          {penaltyVisibility && (
            <DraggableButton
              label={i18next.t(
                `AGENT.KICKOFF_MATCH.${match.status}.PENALTIES_CTA`
              )}
              onPress={() => agentConfirmPenalties.current?.present()}
            />
          )}
        </View>
        <Separator containerStyle={styles.separator} />
        <Text style={[styles.infoDescription]}>
          {i18next.t(`AGENT.KICKOFF_MATCH.${match.status}.LONG_DESCRIPTION`)}
        </Text>
      </ScrollView>
      <ConfirmBottomSheet
        ref={agentConfirmNextStep}
        titleText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.NEXT_STEP.${match.matchSetup === MatchSetup.P1 ? `${MatchSetup.P1}__${match.status}` : match.status}.TITLE`
        )}
        descriptionText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.NEXT_STEP.${match.matchSetup === MatchSetup.P1 ? `${MatchSetup.P1}__${match.status}` : match.status}.DESCRIPTION`
        )}
        buttonText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.NEXT_STEP.${match.matchSetup === MatchSetup.P1 ? `${MatchSetup.P1}__${match.status}` : match.status}.CTA`
        )}
        onBtnPress={() => {
          if (isAsyncEvent) return;
          setIsAsyncEvent(true);
          agentConfirmNextStep.current?.dismiss();
          if (!nextStepAction) return;
          createEvent(
            nextStepAction,
            {
              matchId: match.id,
              action: match.status,
            },
            () => {
              refetchMatch();
              setIsAsyncEvent(false);
            }
          );
        }}
      />
      <ConfirmBottomSheet
        ref={agentConfirmEndGame}
        titleText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.END_GAME.${match.status}.TITLE`
        )}
        descriptionText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.END_GAME.${match.status}.DESCRIPTION`
        )}
        buttonText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.END_GAME.${match.status}.CTA`
        )}
        onBtnPress={() => {
          if (isAsyncEvent) return;
          setIsAsyncEvent(true);
          agentConfirmEndGame.current?.dismiss();
          if (!endGameAction) return;
          createEvent(
            endGameAction,
            {
              matchId: match.id,
              action: match.status,
            },
            () => {
              refetchMatch();
              setIsAsyncEvent(false);
            }
          );
        }}
      />
      <ConfirmBottomSheet
        ref={agentConfirmPenalties}
        titleText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.PENALTIES.${match.status}.TITLE`
        )}
        descriptionText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.PENALTIES.${match.status}.DESCRIPTION`
        )}
        buttonText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.PENALTIES.${match.status}.CTA`
        )}
        onBtnPress={() => {
          if (isAsyncEvent) return;
          setIsAsyncEvent(true);
          agentConfirmPenalties.current?.dismiss();
          if (!penaltiesAction) return;
          createEvent(
            penaltiesAction,
            {
              matchId: match.id,
              action: match.status,
            },
            () => {
              refetchMatch();
              setIsAsyncEvent(false);
            }
          );
        }}
      />
    </ViewContainer>
  );
};

export default Kickoff;
