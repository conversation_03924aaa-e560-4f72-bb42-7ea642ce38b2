import React, { useState, useEffect, useRef } from 'react';
import { Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import {
  <PERSON><PERSON>,
  useTheme,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  MatchHeader,
  Separator,
  DraggableButton,
  ConfirmBottomSheet,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { ClaimStatuses } from '@scorescast/formatters-constants';
import { useAgent, useLiveMatch } from '@scorescast/http-clients';
import { getHeaderTitle } from '../../../../../helpers/screen-title';
import { claimStyles } from './Claim.styles';
import { useAgentView } from '../../../AgentContext';

const Claim: React.FC = () => {
  const {
    agentView: {
      state: { match },
    },
  } = useAgentView();

  const { spacings, typography, colors } = useTheme();
  const navigation = useNavigation<any>();
  const [headerTitle, setHeaderTitle] = useState('');
  const agentClaimConfirm = useRef<BottomSheetModal>(null);
  const { claimMatch } = useAgent();
  const { refetchMatch } = useLiveMatch(match.id, match.status);
  const styles = claimStyles(typography, spacings, colors);

  const claimButtonText =
    match.claimStatus === ClaimStatuses.CAN_CLAIM
      ? i18next.t('AGENT.CLAIM_MATCH.CLAIM_CTA')
      : i18next.t('AGENT.CLAIM_MATCH.ALREADY_CLAIMED_CTA');

  const matchClaimed = match.claimStatus === ClaimStatuses.CLAIMED;

  useEffect(() => {
    if (match?.status) {
      setHeaderTitle(getHeaderTitle(match.status));
    }
  }, [match]);

  return (
    <ViewContainer animated={true}>
      {match?.claimStatus && (
        <>
          <Header
            title={headerTitle}
            withBackButton
            onBackButtonPress={() => navigation.goBack()}
          />
          {match && (
            <MatchHeader
              match={match}
              containerStyle={styles.matchHeaderContainer}
            />
          )}
          <Separator containerStyle={styles.separator} />
          <Text style={[styles.infoDescription]}>
            {i18next.t('AGENT.CLAIM_MATCH.REGISTERED_AGENT_FOR_MATCH_INFO')}
          </Text>
          <DraggableButton
            label={claimButtonText}
            disabled={matchClaimed}
            containerStyle={styles.button}
            onPress={() => {
              agentClaimConfirm.current?.present();
            }}
          />
          {matchClaimed && (
            <Button
              tertiary
              title={i18next.t('AGENT.CLAIM_MATCH.GO_BACK_BUTTON')}
              containerStyle={styles.button}
              onPress={() => navigation.goBack()}
            />
          )}
          <Separator containerStyle={styles.separator} />
          <ConfirmBottomSheet
            ref={agentClaimConfirm}
            titleText={i18next.t('AGENT.CLAIM_MATCH.CONFIRMATION.TITLE')}
            descriptionText={i18next.t(
              'AGENT.CLAIM_MATCH.CONFIRMATION.DESCRIPTION'
            )}
            buttonText={i18next.t('AGENT.CLAIM_MATCH.CONFIRMATION.CTA')}
            onBtnPress={() => {
              agentClaimConfirm.current?.dismiss();
              claimMatch(
                { matchId: match.id },
                {
                  onSuccess: () => {
                    refetchMatch();
                  },
                }
              );
            }}
          />
        </>
      )}
    </ViewContainer>
  );
};

export default Claim;
