import { StyleSheet } from 'react-native';
import {
  TypographyStyles,
  SpacingsStyle,
  ColorStyles,
} from '@scorescast/design-system';

export const claimStyles = (
  typography: typeof TypographyStyles,
  spacings: typeof SpacingsStyle,
  colors: typeof ColorStyles
) =>
  StyleSheet.create({
    separator: {
      marginVertical: spacings.medium,
    },
    infoDescription: {
      ...typography['small'],
      textAlign: 'center',
      color: colors.inverse900,
      paddingVertical: spacings.xlarge,
    },
    matchHeaderContainer: {
      paddingVertical: spacings.xxxlarge,
    },
    button: {
      marginBottom: spacings.xlarge,
    },
  });
