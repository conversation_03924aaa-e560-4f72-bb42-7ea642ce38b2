import React, { useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>Container,
  TeamAvatar,
  Button,
  Icon,
  useTheme,
} from '@scorescast/design-system';
import { ScrollView } from 'react-native';
import { Text, TouchableOpacity, View } from 'react-native';
import { i18next } from '@scorescast/translations';
import { useAgentView } from '../../../AgentContext';
import { editTeamStyles } from './EditTeam.styles';
import { IconName } from '@scorescast/design-system/components/Icon/Icon.types';
import { AgentViews } from '../../../AgentCurrentState.config';
import { UseTeamPlayers } from '@scorescast/http-clients/src/hooks/useTeam';
import { useAgentStore } from '@scorescast/http-clients/src/stores/useAgentStore';

interface EditTeamProps {
  team: any;
}

export enum PlayerActions {
  ADD = 'ADD',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
}

const EditTeam: React.FC<EditTeamProps> = () => {
  const { usTeamImage } = useAgentStore();
  const { agentView, triggerAgentViewChange } = useAgentView();
  const { spacings, typography, colors } = useTheme();

  const styles = editTeamStyles(typography, spacings, colors);
  const { teamPlayersRetrieve } = UseTeamPlayers(agentView?.options?.teamId);

  const teamDetails = useMemo(() => {
    if (teamPlayersRetrieve?.data.length === 0 || !teamPlayersRetrieve.data)
      return;
    return {
      teamName: teamPlayersRetrieve?.data[0]?.team?.name,
      teamId: teamPlayersRetrieve?.data[0]?.team?.teamUniqueId,
      teamPlayers: teamPlayersRetrieve?.data.map((player: any) => {
        return {
          id: player?.playerId,
          jerseyName: player?.jerseyName,
          number: player?.number,
        };
      }),
    };
  }, [teamPlayersRetrieve]);

  return (
    <ViewContainer style={styles.editTeamContainer} animated={true}>
      <View style={styles.headerContainer}>
        <Header
          title={`${i18next.t(`AGENT.TEAM_MANAGEMENT.TITLE`)} `}
          withBackButton
          onBackButtonPress={() => triggerAgentViewChange()}
        />
        <TeamAvatar
          disabled
          team={{ name: teamDetails?.teamName, image: usTeamImage }}
        />
      </View>
      <View style={styles.controlsPlayer}>
        <Text style={styles.playersCount}>
          {`${teamDetails?.teamPlayers?.length ?? 0} ${i18next.t(`AGENT.TEAM_MANAGEMENT.MEMBERS`)}`}
        </Text>
        <Button
          title={i18next.t(`AGENT.TEAM_MANAGEMENT.ADD_MEMBER`)}
          containerStyle={styles.addMemberButton}
          containerTitleStyle={styles.addMemberButtonText}
          onPress={() =>
            triggerAgentViewChange({
              view: AgentViews.PLAYER_ACTION_VIEW,
              options: {
                action: PlayerActions.ADD,
                player: {
                  jerseyName: '',
                  number: '',
                },
                players: teamDetails?.teamPlayers,
                teamId: teamDetails?.teamId,
              },
            })
          }
        />
      </View>
      <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
        <View style={styles.teamMembers}>
          {teamDetails?.teamPlayers?.map((player: any) => {
            return (
              <View
                key={`${player?.jerseyName}_${player.number}_${Math.random()}`}
                style={styles.playerRow}
              >
                <View style={styles.playerRowCircle}>
                  <Text style={styles.playerRowCircleText}>
                    {player?.number}
                  </Text>
                </View>
                <Text style={styles.playerRowNameText}>
                  {player?.jerseyName}
                </Text>
                <TouchableOpacity
                  style={styles.playerRowControls}
                  onPress={() =>
                    triggerAgentViewChange({
                      view: AgentViews.PLAYER_ACTION_VIEW,
                      options: {
                        player,
                        action: PlayerActions.EDIT,
                        teamId: teamDetails?.teamId,
                      },
                    })
                  }
                >
                  <Icon
                    name={IconName.ARROW_RIGHT_LIGHT}
                    isPng={true}
                    width={17}
                    height={28}
                  />
                </TouchableOpacity>
              </View>
            );
          })}
        </View>
      </ScrollView>
    </ViewContainer>
  );
};

export default EditTeam;
