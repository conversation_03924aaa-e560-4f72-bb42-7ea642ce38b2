import React, { useState, useMemo, useRef } from 'react';
import {
  <PERSON><PERSON>,
  ViewContainer,
  useTheme,
  Button,
  IconName,
  DraggableButton,
  BottomSheet,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { Text, View, TextInput } from 'react-native';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useAgentView } from '../../../../AgentContext';
import { playerActionStyles } from './PlayerAction.styles';
import { AgentViews } from '../../../../AgentCurrentState.config';
import { PlayerActions } from '../EditTeam';
import {
  UseTeamPlayers,
  useTeam,
} from '@scorescast/http-clients/src/hooks/useTeam';
import { useToasterStore } from '@scorescast/http-clients/src/stores/useToasterStore';
import { ToasterType } from '@scorescast/formatters-constants';

const PlayerAction: React.FC = () => {
  const { spacings, typography, colors } = useTheme();
  const styles = playerActionStyles(typography, spacings, colors);
  const { triggerAgentViewChange, agentView } = useAgentView();
  const [localPlayer, setLocalPlayer] = useState(agentView.options.player);

  const { addPlayerToTeam, deletePlayerFromTeam } = useTeam();
  const { refetchTeamPlayers } = UseTeamPlayers(agentView.options.teamId);

  const { addToaster } = useToasterStore();
  const deleteConfirmationRef = useRef<BottomSheetModal>(null);

  const isLocalPlayerBasicValid = useMemo(
    () =>
      Object.values(localPlayer).every(
        (prop) => typeof prop === 'string' && prop.trim() !== ''
      ),
    [localPlayer]
  );

  return (
    <ViewContainer animated={true}>
      <Header
        title={`${i18next.t(`AGENT.PLAYER_ACTION.${agentView.options.action}.TITLE`)} `}
        withBackButton
        onBackButtonPress={() =>
          triggerAgentViewChange({
            view: AgentViews.TEAM_MANAGEMENT_VIEW,
          })
        }
      />
      {agentView.options.player && (
        <View style={styles.playerHeader}>
          <View style={styles.playerCircleNumber}>
            <Text style={styles.playerCircleText}>{localPlayer.number}</Text>
          </View>
          <Text style={styles.playerRowNameText}>{localPlayer.jerseyName}</Text>
        </View>
      )}

      <View style={styles.playerForm}>
        <View style={styles.formRow}>
          <Text style={styles.rowLabel}>
            {`${i18next.t(`AGENT.PLAYER_ACTION.FORM.JERSEY_NAME`)}`}
          </Text>
          <TextInput
            editable={agentView.options.action === PlayerActions.ADD}
            style={styles.rowInput}
            value={localPlayer.jerseyName}
            onChangeText={(text) => {
              if (/^[a-zA-Z\s]*$/.test(text)) {
                setLocalPlayer({ ...localPlayer, ['jerseyName']: text });
              }
            }}
          />
        </View>
        <View style={styles.formRow}>
          <Text style={styles.rowLabel}>
            {`${i18next.t(`AGENT.PLAYER_ACTION.FORM.JERSEY_NUMBER`)}`}
          </Text>
          <TextInput
            editable={agentView.options.action === PlayerActions.ADD}
            style={styles.rowInput}
            value={localPlayer.number}
            onChangeText={(text) => {
              if (/^\d*$/.test(text)) {
                setLocalPlayer({ ...localPlayer, ['number']: text });
              }
            }}
          />
        </View>
        {agentView.options.action === PlayerActions.EDIT && (
          <Button
            containerStyle={styles.deleteButton}
            containerTitleStyle={styles.deleteButtonTitle}
            iconStyle={styles.deleteButtonIcon}
            pngDimensions={18}
            title={i18next.t(`AGENT.PLAYER_ACTION.FORM.DELETE_PLAYER`)}
            onPress={() => {
              deleteConfirmationRef.current?.present();
            }}
            icon={IconName.DELETE}
          />
        )}
        {agentView.options.action === PlayerActions.ADD && (
          <DraggableButton
            disabled={!isLocalPlayerBasicValid}
            label={i18next.t(`AGENT.PLAYER_ACTION.FORM.ADD_PLAYER`)}
            onPress={() => {
              //check if jerseyNumber exists
              const numberExists = agentView.options.players
                ? agentView.options.players.filter(
                    (player) => player.number === localPlayer.number
                  )
                : [];
              if (numberExists.length) {
                addToaster({
                  type: ToasterType.ERROR,
                  title: i18next.t(
                    'AGENT.PLAYER_ACTION.ADD.VALIDATION.EXISTING_NUMBER_TITLE'
                  ),
                  description: i18next.t(
                    'AGENT.PLAYER_ACTION.ADD.VALIDATION.EXISTING_NUMBER_DESCRIPTION',
                    {
                      jerseyNumber: numberExists[0].number,
                      player: numberExists[0].jerseyName,
                    }
                  ),
                });
                return;
              }

              addPlayerToTeam({
                ...localPlayer,
                teamId: agentView?.options?.teamId,
              }).then(() => {
                refetchTeamPlayers().then(() => {
                  triggerAgentViewChange({
                    view: AgentViews.TEAM_MANAGEMENT_VIEW,
                  });
                });
              });
            }}
          />
        )}
      </View>

      <BottomSheet ref={deleteConfirmationRef}>
        <View style={styles.confirmContainer}>
          <Text style={styles.confirmTitle}>
            {i18next.t('AGENT.PLAYER_ACTION.DELETE_CONFIRM_TITLE')}
          </Text>
          <Text style={styles.confirmSubtitle}>
            {i18next.t('AGENT.PLAYER_ACTION.DELETE_CONFIRM_MESSAGE', {
              playerName: localPlayer.jerseyName,
            })}
          </Text>
          <View style={styles.confirmButtonContainer}>
            <Button
              title={i18next.t('AGENT.PLAYER_ACTION.CANCEL')}
              onPress={() => deleteConfirmationRef.current?.dismiss()}
              containerStyle={styles.cancelButton}
            />
            <Button
              title={i18next.t('AGENT.PLAYER_ACTION.CONFIRM_DELETE')}
              onPress={() => {
                deleteConfirmationRef.current?.dismiss();
                deletePlayerFromTeam({ id: localPlayer.id }).then(() => {
                  refetchTeamPlayers().then(() => {
                    triggerAgentViewChange({
                      view: AgentViews.TEAM_MANAGEMENT_VIEW,
                    });
                  });
                });
              }}
              containerStyle={styles.confirmDeleteButton}
              containerTitleStyle={styles.confirmDeleteButtonText}
            />
          </View>
        </View>
      </BottomSheet>
    </ViewContainer>
  );
};

export default PlayerAction;
