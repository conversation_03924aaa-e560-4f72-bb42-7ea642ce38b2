import { StyleSheet } from 'react-native';
import {
  TypographyStyles,
  SpacingsStyle,
  ColorStyles,
} from '@scorescast/design-system';

export const playerActionStyles = (
  typography: typeof TypographyStyles,
  spacings: typeof SpacingsStyle,
  colors: typeof ColorStyles
) =>
  StyleSheet.create({
    playerHeader: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      gap: spacings.large,
      marginVertical: spacings.xxlarge,
      ...typography['medium-semibold'],
    },
    playerCircleNumber: {
      width: 70,
      height: 70,
      borderRadius: 35,
      backgroundColor: colors.inverse900,
      justifyContent: 'center',
      alignItems: 'center',
    },
    playerCircleText: {
      color: colors.inverse100,
      ...typography['small-bold'],
    },
    playerRowNameText: {
      color: colors.inverse900,
      ...typography['small-bold'],
    },
    playerForm: {
      display: 'flex',
      flexDirection: 'column',
    },
    formRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'baseline',
      height: 40,
      marginBottom: spacings.xxlarge,
    },
    rowLabel: {
      flexBasis: '40%',
      color: colors.inverse900,
      ...typography['small-semibold'],
      paddingBottom: spacings.medium,
    },
    rowInput: {
      flex: 1,
      color: colors.inverse900,
      ...typography['small-semibold'],
      borderBottomWidth: 1,
      borderBottomColor: colors.neutral700,
      height: 40,
    },
    deleteButton: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    deleteButtonTitle: {
      color: colors.red,
    },
    deleteButtonIcon: {
      marginRight: 0,
      height: 30,
      width: 30,
    },
    confirmContainer: {
      paddingHorizontal: spacings.large,
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.xxxxlarge,
    },
    confirmTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      textAlign: 'center',
      color: colors.inverse900,
    },
    confirmSubtitle: {
      fontSize: 14,
      color: colors.inverse900,
      paddingVertical: spacings.xxxlarge,
      textAlign: 'center',
    },
    confirmButtonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
    },
    cancelButton: {
      flex: 1,
      marginRight: spacings.medium,
    },
    confirmDeleteButton: {
      flex: 1,
      marginLeft: spacings.medium,
      backgroundColor: colors.neutral700,
      borderRadius: 0,
    },
    confirmDeleteButtonText: {
      color: colors.red,
    },
  });
