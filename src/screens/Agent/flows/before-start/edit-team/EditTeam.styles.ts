import { StyleSheet } from 'react-native';
import {
  TypographyStyles,
  SpacingsStyle,
  ColorStyles,
} from '@scorescast/design-system';

export const editTeamStyles = (
  typography: typeof TypographyStyles,
  spacings: typeof SpacingsStyle,
  colors: typeof ColorStyles
) =>
  StyleSheet.create({
    editTeamContainer: {
      flex: 1,
    },
    headerContainer: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
      gap: spacings.large,
    },
    controlsPlayer: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: spacings.xxxxxlarge,
    },
    playersCount: {
      flex: 1,
      color: colors.inverse850,
      opacity: 0.6,
      ...typography['small-semibold'],
    },
    addMemberButton: {
      flex: 1,
      borderRadius: 5,
      height: 40,
    },
    addMemberButtonText: {
      ...typography['small-semibold'],
    },
    teamMembers: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
      gap: spacings.xlarge,
      marginTop: spacings.xxxlarge,
    },
    playerRow: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      ...typography['medium-semibold'],
    },
    playerRowCircle: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: colors.inverse900,
      justifyContent: 'center',
      alignItems: 'center',
    },
    playerRowCircleText: {
      color: colors.inverse100,
      ...typography['small-bold'],
    },
    playerRowNameText: {
      color: colors.inverse900,
      ...typography['small-bold'],
      marginLeft: spacings.large,
    },
    playerRowControls: {
      marginLeft: 'auto',
    },
  });
