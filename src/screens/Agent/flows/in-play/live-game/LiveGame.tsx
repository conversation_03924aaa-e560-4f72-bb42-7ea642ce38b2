import React, { useCallback, useEffect, useRef, useState } from 'react';
import { ScrollView, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  ConfirmBottomSheet,
  useTheme,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  MatchHeader,
  SliderSelect,
  BoxSelector,
  Separator,
  DraggableButton,
  Button,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { useAgent, useLiveMatch } from '@scorescast/http-clients';
import {
  MatchEventTypes,
  MatchPossibleEvents,
  MatchPossibleTeams,
} from '@scorescast/formatters-constants';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { getHeaderTitle } from '../../../../../helpers/screen-title';
import { useAgentView } from '../../../AgentContext';
import { AgentViews } from '../../../AgentCurrentState.config';
import { liveGameStyles } from './LiveGame.styles';
import { useAgentStore } from '@scorescast/http-clients/src/stores/useAgentStore';
import { MatchSetup } from '@scorescast/formatters-constants/src/models/match';

const LiveGame: React.FC = () => {
  const { setSideSelection, sideSelection } = useAgentStore();
  const { agentView, triggerAgentViewChange } = useAgentView();
  const { spacings, typography, colors } = useTheme();
  const agentConfirmAction = useRef<BottomSheetModal>(null);
  const [isAsyncEvent, setIsAsyncEvent] = useState(false);

  const navigation = useNavigation<any>();
  const [headerTitle, setHeaderTitle] = useState('');
  const [selectedTeam, setSelectedTeam] = useState(sideSelection);
  const { createEvent } = useAgent();
  const { refetchMatch } = useLiveMatch(
    agentView.state.match.id,
    agentView.state.match.status
  );
  const styles = liveGameStyles(typography, spacings, colors);

  useEffect(() => {
    if (agentView.state.match?.status) {
      setHeaderTitle(getHeaderTitle(agentView.state.match.status));
    }
  }, [agentView.state.match]);

  const onSelectEvent = useCallback(
    (selected: any) => {
      if (selected === MatchEventTypes.GOAL_VIEW) {
        triggerAgentViewChange({
          view: AgentViews.GOAL_VIEW,
        });
      } else if (selected === MatchEventTypes.FOUL_VIEW) {
        triggerAgentViewChange({
          view: AgentViews.FOUL_VIEW,
        });
      }
    },
    [selectedTeam, triggerAgentViewChange]
  );

  return (
    <ViewContainer animated={true}>
      <Header
        title={headerTitle}
        withBackButton
        hasFeed
        onFeedPress={() =>
          triggerAgentViewChange({
            view: AgentViews.FEED_VIEW,
          })
        }
        onBackButtonPress={() => navigation.goBack()}
      />
      {agentView.state.match && (
        <MatchHeader
          match={agentView.state.match}
          containerStyle={styles.matchHeaderContainer}
        />
      )}
      <Separator containerStyle={styles.separator} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        bounces={false}
        disableScrollViewPanResponder={true}
        disableIntervalMomentum={true}
      >
        <SliderSelect
          containerStyle={styles.sliderSelect}
          options={MatchPossibleTeams}
          selected={selectedTeam}
          onToggle={(
            selection: (typeof MatchPossibleTeams)[keyof typeof MatchPossibleTeams]
          ) => {
            setSelectedTeam(selection);
            setSideSelection(selection);
          }}
        />
        <BoxSelector options={MatchPossibleEvents} onSelect={onSelectEvent} />
        <DraggableButton
          label={i18next.t(
            `AGENT.LIVE_MATCH.${agentView.state.match.matchSetup === MatchSetup.P4 ? `${MatchSetup.P4}__${agentView.state.match.status}` : agentView.state.match.status}.END_PERIOD`
          )}
          onPress={() => agentConfirmAction.current?.present()}
          containerStyle={styles.buttonStyle}
        />
        <Button
          title={i18next.t(`AGENT.LIVE_MATCH.EDIT_TEAM_CTA`)}
          onPress={() => {
            triggerAgentViewChange({
              view: AgentViews.TEAM_MANAGEMENT_VIEW,
            });
          }}
          containerStyle={styles.editTeamButton}
        />
        <Separator containerStyle={styles.separator} />
        <Text style={[styles.infoDescription]}>
          {i18next.t(`AGENT.LIVE_MATCH.INFO`)}
        </Text>
      </ScrollView>
      <ConfirmBottomSheet
        ref={agentConfirmAction}
        titleText={i18next.t(
          `AGENT.LIVE_GAME_CONFIRM_SHEET.${agentView.state.match.status}.TITLE`
        )}
        descriptionText={i18next.t(
          `AGENT.LIVE_GAME_CONFIRM_SHEET.${agentView.state.match.status}.DESCRIPTION`
        )}
        buttonText={i18next.t(
          `AGENT.LIVE_GAME_CONFIRM_SHEET.${agentView.state.match.status}.CTA`
        )}
        onBtnPress={() => {
          if (isAsyncEvent) return;
          setIsAsyncEvent(true);
          agentConfirmAction.current?.dismiss();
          createEvent(
            agentView?.options?.nextStepAction,
            {
              matchId: agentView.state.match.id,
              action: agentView.state.match.status,
            },
            () => {
              refetchMatch();
              setIsAsyncEvent(false);
            }
          );
        }}
      />
    </ViewContainer>
  );
};

export default LiveGame;
