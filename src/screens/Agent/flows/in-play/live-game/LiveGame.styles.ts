import { StyleSheet } from 'react-native';
import {
  TypographyStyles,
  SpacingsStyle,
  ColorStyles,
} from '@scorescast/design-system';

export const liveGameStyles = (
  typography: typeof TypographyStyles,
  spacings: typeof SpacingsStyle,
  colors: typeof ColorStyles
) =>
  StyleSheet.create({
    matchHeaderContainer: {
      paddingVertical: spacings.xxxlarge,
    },
    separator: {
      marginBottom: spacings.medium,
      marginTop: spacings.xlarge,
    },
    sliderSelect: {
      marginTop: spacings.xlarge,
      marginBottom: spacings.xxxxlarge,
    },
    buttonStyle: {
      marginTop: spacings.xxxlarge,
      marginBottom: spacings.xxxlarge,
    },
    infoDescription: {
      ...typography['small'],
      textAlign: 'center',
      color: colors.inverse900,
      paddingVertical: spacings.xlarge,
    },
    iconWrapper: {
      position: 'absolute',
      top: 30,
      right: 30,
      zIndex: 999,
      elevation: 5,
    },
  });
