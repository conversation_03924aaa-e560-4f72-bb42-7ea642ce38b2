import React, { useCallback, useMemo, useRef, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import {
  useTheme,
  ViewContainer,
  Header,
  Select,
  AgentConfirmPlayEvent,
  CardSelection,
  Button,
} from '@scorescast/design-system';
import {
  AgentEventSideSelection,
  MatchEventTypes,
  getMatchPossibilitesCards,
} from '@scorescast/formatters-constants';
import { i18next } from '@scorescast/translations';
import {
  useCreateEvent,
  useRetrieveMatchDetails,
} from '@scorescast/http-client';
import { foulSelectionStyles } from './FoulSelection.styles';
import { useAgentView } from '../../../AgentContext';
import { AgentViews } from '../../../AgentCurrentState.config';
import { useAgentStore } from '@scorescast/http-client';

const FoulSelection: React.FC = () => {
  const { sideSelection } = useAgentStore();
  const { agentView, triggerAgentViewChange } = useAgentView();
  const { spacings, colors } = useTheme();

  // Use our new controller hooks
  const createEventMutation = useCreateEvent({});
  const { refetch: refetchMatch } = useRetrieveMatchDetails(
    {
      matchId: agentView.state.match.id,
    },
    {
      enabled: !!agentView.state.match.id,
    }
  );

  const playerFoulSelection = useRef<BottomSheetModal>(null);
  const foulConfirmation = useRef<BottomSheetModal>(null);
  const [isAsyncEvent, setIsAsyncEvent] = useState(false);

  const [isCardAwarded, setCardAwarded] = useState(null);

  const cards = getMatchPossibilitesCards(colors);

  const playersList = useMemo(() => {
    if (!agentView.state.team) return;
    return agentView.state.team.map((player) => {
      return {
        id: player.playerId,
        name: player.jerseyName,
        number: player.number,
      };
    });
  }, [agentView.state.team]);

  const [foulPlayer, setFoulPlayer] = useState<{
    id: string;
    name: string;
    number: string;
  } | null>(null);

  const styles = foulSelectionStyles(spacings);

  const onConfirmFoul = useCallback(() => {
    if (isAsyncEvent) return;
    setIsAsyncEvent(true);
    foulConfirmation.current?.dismiss();
    if (!foulPlayer?.name && sideSelection.id === AgentEventSideSelection.US)
      return;

    if (
      agentView?.options?.selection === MatchEventTypes.CARDS &&
      isCardAwarded
    ) {
      // Direct card event
      createEventMutation.mutate(
        {
          payload: {
            playerId: foulPlayer ? parseInt(foulPlayer.id) : 0,
            matchId: agentView.state.match.id,
            who: sideSelection.id,
            action: agentView?.options?.selection,
            outcome: MatchEventTypes[isCardAwarded],
            period: agentView.state.match.status,
            timestamp: Date.now(),
            latitude: 0,
            longitude: 0,
          },
        },
        {
          onSuccess: () => {
            setIsAsyncEvent(false);
          },
          onError: () => {
            setIsAsyncEvent(false);
          },
        }
      );
    } else {
      // Foul event, potentially followed by card
      createEventMutation.mutate(
        {
          payload: {
            playerId: foulPlayer ? parseInt(foulPlayer.id) : 0,
            matchId: agentView.state.match.id,
            who: sideSelection.id,
            action: agentView?.options?.selection,
            outcome: 'FOUL',
            period: agentView.state.match.status,
            timestamp: Date.now() - 2000,
            latitude: 0,
            longitude: 0,
          },
        },
        {
          onSuccess: () => {
            if (isCardAwarded) {
              // Create card event after foul
              createEventMutation.mutate(
                {
                  payload: {
                    playerId: foulPlayer ? parseInt(foulPlayer.id) : 0,
                    matchId: agentView.state.match.id,
                    who: sideSelection.id,
                    action: MatchEventTypes[isCardAwarded],
                    outcome: 'CARD',
                    period: agentView.state.match.status,
                    timestamp: Date.now(),
                    latitude: 0,
                    longitude: 0,
                  },
                },
                {
                  onSuccess: () => {
                    if (
                      agentView.options.selection ===
                      MatchEventTypes.FOUL_PENALTY
                    ) {
                      triggerAgentViewChange({
                        view: AgentViews.FOUL__PENS_VIEW,
                      });
                    } else {
                      refetchMatch();
                      triggerAgentViewChange();
                      setIsAsyncEvent(false);
                    }
                  },
                  onError: () => {
                    setIsAsyncEvent(false);
                  },
                }
              );
            } else {
              if (
                agentView.options.selection === MatchEventTypes.FOUL_PENALTY
              ) {
                triggerAgentViewChange({
                  view: AgentViews.FOUL__PENS_VIEW,
                });
              } else {
                refetchMatch();
                triggerAgentViewChange();
                setIsAsyncEvent(false);
              }
            }
          },
          onError: () => {
            setIsAsyncEvent(false);
          },
        }
      );
    }
  }, [
    agentView,
    createEventMutation,
    isAsyncEvent,
    foulPlayer,
    isCardAwarded,
    sideSelection,
    refetchMatch,
    triggerAgentViewChange,
  ]);

  return (
    <ViewContainer animated={true}>
      <Header
        title={`${i18next.t(`AGENT.FOUL_SELECTION.FOUL`)} > ${i18next.t(`AGENT.FOUL_SELECTION.${agentView.options.selection}`)} `}
        withBackButton
        onBackButtonPress={() =>
          triggerAgentViewChange({
            view: AgentViews.FOUL_VIEW,
          })
        }
      />
      <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
        <CardSelection cards={cards} onSelect={setCardAwarded} />
        {sideSelection.id !== AgentEventSideSelection.THEM && (
          <Select
            ref={playerFoulSelection}
            selectLabel={i18next.t(`AGENT.FOUL_SELECTION.SELECT_PLAYER`)}
            list={playersList}
            onOpen={() => {
              playerFoulSelection.current?.present();
            }}
            onItemSelect={(player: any) => {
              playerFoulSelection.current?.dismiss();
              setFoulPlayer(player);
            }}
          />
        )}
        <View style={styles.buttonContainer}>
          <Button
            shadow
            containerStyle={styles.button}
            title={i18next.t(`AGENT.FOUL_SELECTION.CANCEL`)}
            onPress={() =>
              triggerAgentViewChange({
                view: AgentViews.FOUL_VIEW,
              })
            }
          />
          <Button
            containerStyle={styles.button}
            title={i18next.t(`AGENT.FOUL_SELECTION.CONFIRM`)}
            secondary
            disabled={
              (!foulPlayer &&
                sideSelection.id === AgentEventSideSelection.US) ||
              (agentView?.options?.selection === MatchEventTypes.CARDS &&
                !isCardAwarded)
            }
            onPress={() => foulConfirmation.current?.present()}
          />
        </View>
      </ScrollView>
      <AgentConfirmPlayEvent
        ref={foulConfirmation}
        title={`${i18next.t(`AGENT.FOUL_SELECTION.CONFIRM`)} ${i18next.t(`AGENT.FOUL_SELECTION.FOUL`)} > ${i18next.t(`AGENT.FOUL_SELECTION.${agentView.options.selection}`)}`}
        firstAction={{
          label: `${i18next.t(`AGENT.FOUL_SELECTION.PLAYER`)}`,
          number: foulPlayer?.number,
          name: foulPlayer?.name,
        }}
        cardAwarded={cards.find((card: any) => card.id === isCardAwarded)}
        buttonText={`${i18next.t(`AGENT.FOUL_SELECTION.CONFIRM`)}`}
        onBtnPress={onConfirmFoul}
      />
    </ViewContainer>
  );
};

export default FoulSelection;
