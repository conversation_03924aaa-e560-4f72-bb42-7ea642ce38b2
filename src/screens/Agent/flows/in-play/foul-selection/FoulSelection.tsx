import React, { useCallback, useMemo, useRef, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import {
  useTheme,
  <PERSON>Container,
  Header,
  Select,
  AgentConfirmPlayEvent,
  CardSelection,
  Button,
} from '@scorescast/design-system';
import {
  AgentEventSideSelection,
  MatchEventTypes,
  getMatchPossibilitesCards,
} from '@scorescast/formatters-constants';
import { i18next } from '@scorescast/translations';
import { useAgent, useLiveMatch } from '@scorescast/http-clients';
import { foulSelectionStyles } from './FoulSelection.styles';
import { useAgentView } from '../../../AgentContext';
import { AgentViews } from '../../../AgentCurrentState.config';
import { useAgentStore } from '@scorescast/http-clients/src/stores/useAgentStore';

const FoulSelection: React.FC = () => {
  const { sideSelection } = useAgentStore();
  const { agentView, triggerAgentViewChange } = useAgentView();
  const { spacings, colors } = useTheme();
  const { createEvent } = useAgent();
  const { refetchMatch } = useLiveMatch(
    agentView.state.match.id,
    agentView.state.match.status
  );

  const playerFoulSelection = useRef<BottomSheetModal>(null);
  const foulConfirmation = useRef<BottomSheetModal>(null);
  const [isAsyncEvent, setIsAsyncEvent] = useState(false);

  const [isCardAwarded, setCardAwarded] = useState(null);

  const cards = getMatchPossibilitesCards(colors);

  const playersList = useMemo(() => {
    if (!agentView.state.team) return;
    return agentView.state.team.map((player) => {
      return {
        id: player.playerId,
        name: player.jerseyName,
        number: player.number,
      };
    });
  }, [agentView.state.team]);

  const [foulPlayer, setFoulPlayer] = useState<{
    id: string;
    name: string;
    number: string;
  } | null>(null);

  const styles = foulSelectionStyles(spacings);

  const onConfirmFoul = useCallback(() => {
    if (isAsyncEvent) return;
    setIsAsyncEvent(true);
    foulConfirmation.current?.dismiss();
    if (!foulPlayer?.name && sideSelection.id === AgentEventSideSelection.US)
      return;

    if (
      agentView?.options?.selection === MatchEventTypes.CARDS &&
      isCardAwarded
    ) {
      createEvent(
        agentView?.options?.selection,
        {
          matchId: agentView.state.match.id,
          playerId: foulPlayer ? foulPlayer.id : null,
          who: sideSelection.id,
          period: agentView.state.match.status,
          outcome: MatchEventTypes[isCardAwarded],
        },
        () => setIsAsyncEvent(false)
      );
    } else {
      createEvent(
        agentView?.options?.selection,
        {
          matchId: agentView.state.match.id,
          playerId: foulPlayer ? foulPlayer.id : null,
          who: sideSelection.id,
          period: agentView.state.match.status,
          timestamp: Date.now() - 2000,
        },
        () => {
          if (isCardAwarded) {
            createEvent(
              MatchEventTypes[isCardAwarded],
              {
                matchId: agentView.state.match.id,
                playerId: foulPlayer ? foulPlayer.id : null,
                who: sideSelection.id,
                period: agentView.state.match.status,
              },
              () => {
                if (
                  agentView.options.selection === MatchEventTypes.FOUL_PENALTY
                ) {
                  triggerAgentViewChange({
                    view: AgentViews.FOUL__PENS_VIEW,
                  });
                } else {
                  refetchMatch();
                  triggerAgentViewChange();
                  setIsAsyncEvent(false);
                }
              }
            );
          } else {
            if (agentView.options.selection === MatchEventTypes.FOUL_PENALTY) {
              triggerAgentViewChange({
                view: AgentViews.FOUL__PENS_VIEW,
              });
            } else {
              refetchMatch();
              triggerAgentViewChange();
              setIsAsyncEvent(false);
            }
          }
        }
      );
    }
  }, [
    agentView,
    createEvent,
    isAsyncEvent,
    foulPlayer,
    isCardAwarded,
    agentView.state.match.id,
    agentView.state.match.status,
    refetchMatch,
    triggerAgentViewChange,
  ]);

  return (
    <ViewContainer animated={true}>
      <Header
        title={`${i18next.t(`AGENT.FOUL_SELECTION.FOUL`)} > ${i18next.t(`AGENT.FOUL_SELECTION.${agentView.options.selection}`)} `}
        withBackButton
        onBackButtonPress={() =>
          triggerAgentViewChange({
            view: AgentViews.FOUL_VIEW,
          })
        }
      />
      <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
        <CardSelection cards={cards} onSelect={setCardAwarded} />
        {sideSelection.id !== AgentEventSideSelection.THEM && (
          <Select
            ref={playerFoulSelection}
            selectLabel={i18next.t(`AGENT.FOUL_SELECTION.SELECT_PLAYER`)}
            list={playersList}
            onOpen={() => {
              playerFoulSelection.current?.present();
            }}
            onItemSelect={(player: any) => {
              playerFoulSelection.current?.dismiss();
              setFoulPlayer(player);
            }}
          />
        )}
        <View style={styles.buttonContainer}>
          <Button
            shadow
            containerStyle={styles.button}
            title={i18next.t(`AGENT.FOUL_SELECTION.CANCEL`)}
            onPress={() =>
              triggerAgentViewChange({
                view: AgentViews.FOUL_VIEW,
              })
            }
          />
          <Button
            containerStyle={styles.button}
            title={i18next.t(`AGENT.FOUL_SELECTION.CONFIRM`)}
            secondary
            disabled={
              (!foulPlayer &&
                sideSelection.id === AgentEventSideSelection.US) ||
              (agentView?.options?.selection === MatchEventTypes.CARDS &&
                !isCardAwarded)
            }
            onPress={() => foulConfirmation.current?.present()}
          />
        </View>
      </ScrollView>
      <AgentConfirmPlayEvent
        ref={foulConfirmation}
        title={`${i18next.t(`AGENT.FOUL_SELECTION.CONFIRM`)} ${i18next.t(`AGENT.FOUL_SELECTION.FOUL`)} > ${i18next.t(`AGENT.FOUL_SELECTION.${agentView.options.selection}`)}`}
        firstAction={{
          label: `${i18next.t(`AGENT.FOUL_SELECTION.PLAYER`)}`,
          number: foulPlayer?.number,
          name: foulPlayer?.name,
        }}
        cardAwarded={cards.find((card: any) => card.id === isCardAwarded)}
        buttonText={`${i18next.t(`AGENT.FOUL_SELECTION.CONFIRM`)}`}
        onBtnPress={onConfirmFoul}
      />
    </ViewContainer>
  );
};

export default FoulSelection;
