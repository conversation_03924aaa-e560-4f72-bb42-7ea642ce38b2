import React, { useCallback, useState, useRef, useEffect } from 'react';
import {
  MatchPossibleTeams,
  MatchPossiblePenaltyTypes,
} from '@scorescast/formatters-constants';
import {
  useTheme,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  MatchHeader,
  Separator,
  <PERSON>liderSelect,
  BoxSelector,
  DraggableButton,
  ConfirmBottomSheet,
} from '@scorescast/design-system';
import { penaltiesStyes } from './Penalties.styles';
import { useNavigation } from '@react-navigation/native';
import { i18next } from '@scorescast/translations';
import { useAgentView } from '../../../AgentContext';
import { ScrollView } from 'react-native';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useAgent, useLiveMatch } from '@scorescast/http-clients';
import {
  AgentEventSideSelection,
  MatchPossiblePenaltyTypesWhenFoulPenalty,
} from '@scorescast/formatters-constants/src/constants/live-match';
import { MatchEventTypes } from '@scorescast/formatters-constants/src/constants/match';
import { AgentViews } from '../../../AgentCurrentState.config';
import { useAgentStore } from '@scorescast/http-clients/src/stores/useAgentStore';

const Penalties: React.FC = () => {
  const { setSideSelection, sideSelection } = useAgentStore();

  const { agentView, triggerAgentViewChange } = useAgentView();
  const { spacings } = useTheme();
  const { createEvent } = useAgent();
  const { refetchMatch } = useLiveMatch(
    agentView.state.match.id,
    agentView.state.match.status
  );

  const navigation = useNavigation<any>();
  const [penaltySelection, setPenaltySelection] = useState<
    MatchEventTypes.PENALTY_MISS | MatchEventTypes.PENALTY_SAVE | null
  >(null);
  const [selectedTeam, setSelectedTeam] = useState(sideSelection);
  const [isAsyncEvent, setIsAsyncEvent] = useState(false);

  const agentConfirmEndGame = useRef<BottomSheetModal>(null);
  const agentConfirmPenaltyGoal = useRef<BottomSheetModal>(null);

  useEffect(() => {
    if (agentView.currentView === AgentViews.FOUL__PENS_VIEW) {
      setSelectedTeam(
        MatchPossibleTeams.find((option) => option.id !== sideSelection.id)
      );
    }
  }, []);
  const onSelectPenaltyType = useCallback(
    (selected: any) => {
      setPenaltySelection(selected);

      if (
        selectedTeam.id === AgentEventSideSelection.THEM &&
        (selected === MatchEventTypes.PENALTY_GOAL ||
          selected === MatchEventTypes.PENALTY_MISS)
      ) {
        agentConfirmPenaltyGoal.current?.present();
        return;
      }

      triggerAgentViewChange({
        view: AgentViews.PENS_VIEW_SELECTION,
        options: {
          selection: selected,
          sideSelection: selectedTeam,
          previousView:
            agentView.currentView === AgentViews.FOUL__PENS_VIEW
              ? AgentViews.FOUL__PENS_VIEW
              : '',
        },
      });
    },
    [selectedTeam]
  );

  const styles = penaltiesStyes(spacings);
  return (
    <ViewContainer>
      <Header
        title={i18next.t(`MATCH_SCREEN.STARTED_TITLE`)}
        withBackButton
        hasFeed={agentView.currentView !== AgentViews.FOUL__PENS_VIEW}
        onFeedPress={() =>
          triggerAgentViewChange({
            view: AgentViews.FEED_VIEW,
          })
        }
        onBackButtonPress={() => {
          if (agentView.currentView === AgentViews.FOUL__PENS_VIEW) {
            triggerAgentViewChange();
          } else {
            navigation.goBack();
          }
        }}
      />
      {agentView.state.match && (
        <MatchHeader
          match={agentView.state.match}
          containerStyle={styles.matchHeaderContainer}
        />
      )}
      <Separator containerStyle={styles.separator} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        bounces={false}
        disableScrollViewPanResponder={true}
        disableIntervalMomentum={true}
      >
        <SliderSelect
          containerStyle={styles.sliderSelect}
          options={MatchPossibleTeams}
          selected={selectedTeam}
          isDisabled={agentView.currentView === AgentViews.FOUL__PENS_VIEW}
          onToggle={(
            selection: (typeof MatchPossibleTeams)[keyof typeof MatchPossibleTeams]
          ) => {
            setSelectedTeam(selection);
            setSideSelection(selection);
          }}
        />
        <BoxSelector
          containerStyle={styles.boxSelector}
          options={
            agentView.currentView === AgentViews.FOUL__PENS_VIEW &&
            selectedTeam.id === AgentEventSideSelection.US
              ? MatchPossiblePenaltyTypesWhenFoulPenalty
              : MatchPossiblePenaltyTypes
          }
          onSelect={onSelectPenaltyType}
        />
        {agentView?.options?.endGameAction && (
          <DraggableButton
            label={i18next.t(
              `AGENT.KICKOFF_MATCH.${agentView.state.match.status}.END_GAME_CTA`
            )}
            onPress={() => agentConfirmEndGame.current?.present()}
          />
        )}
        <Separator containerStyle={styles.separator} />
      </ScrollView>

      <ConfirmBottomSheet
        ref={agentConfirmEndGame}
        titleText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.END_GAME.${agentView.state.match.status}.TITLE`
        )}
        descriptionText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.END_GAME.${agentView.state.match.status}.DESCRIPTION`
        )}
        buttonText={i18next.t(
          `AGENT.KICKOFF_CONFIRM_SHEET.END_GAME.${agentView.state.match.status}.CTA`
        )}
        onBtnPress={() => {
          if (isAsyncEvent) return;
          setIsAsyncEvent(true);
          agentConfirmEndGame.current?.dismiss();
          createEvent(
            agentView?.options?.endGameAction,
            {
              matchId: agentView.state.match.id,
              action: agentView.state.match.status,
              period: agentView.state.match.status,
            },
            () => {
              refetchMatch();
              setIsAsyncEvent(false);
            }
          );
        }}
      />
      <ConfirmBottomSheet
        ref={agentConfirmPenaltyGoal}
        titleText={i18next.t(
          `AGENT.PENALTY_CONFIRM_SHEET.${penaltySelection}.TITLE`
        )}
        descriptionText={i18next.t(
          `AGENT.PENALTY_CONFIRM_SHEET.${penaltySelection}.DESCRIPTION`
        )}
        buttonText={i18next.t(
          `AGENT.PENALTY_CONFIRM_SHEET.${penaltySelection}.CTA`
        )}
        onBtnPress={() => {
          if (isAsyncEvent) return;
          setIsAsyncEvent(true);
          agentConfirmEndGame.current?.dismiss();
          createEvent(
            penaltySelection,
            {
              matchId: agentView.state.match.id,
              period: agentView.state.match.status,
              who: selectedTeam.id,
            },
            () => {
              refetchMatch();
              if (agentView.currentView === AgentViews.FOUL__PENS_VIEW)
                triggerAgentViewChange();
              agentConfirmPenaltyGoal.current?.dismiss();
              setIsAsyncEvent(false);
            }
          );
        }}
      />
    </ViewContainer>
  );
};

export default Penalties;
