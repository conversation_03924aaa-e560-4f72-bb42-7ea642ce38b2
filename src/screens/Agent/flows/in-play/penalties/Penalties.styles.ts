import { StyleSheet } from 'react-native';
import { SpacingsStyle } from '@scorescast/design-system';

export const penaltiesStyes = (spacings: typeof SpacingsStyle) =>
  StyleSheet.create({
    separator: {
      marginVertical: spacings.medium,
    },
    sliderSelect: {
      marginTop: spacings.xlarge,
      marginBottom: spacings.xxxxlarge,
    },
    matchHeaderContainer: {
      paddingVertical: spacings.xxxlarge,
      paddingHorizontal: spacings.large,
    },
    boxSelector: {
      marginBottom: spacings.xlarge,
    },
  });
