import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { View, ScrollView } from 'react-native';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import {
  Button,
  Select,
  AgentConfirmPlayEvent,
  Header,
  useTheme,
  ViewContainer,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { MatchEventTypes } from '@scorescast/formatters-constants';
import { useAgent, useLiveMatch } from '@scorescast/http-clients';
import { penaltiesSelectionStyles } from './PenaltiesSelection.styles';
import { useAgentView } from '../../../AgentContext';
import { AgentViews } from '../../../AgentCurrentState.config';
import { AgentEventSideSelection } from '@scorescast/formatters-constants/src/constants/live-match';
import { UseTeamPlayers } from '@scorescast/http-clients/src/hooks/useTeam';
import { Player } from '@scorescast/http-clients/src/services/agent-api';

const PenaltiesSelection: React.FC = () => {
  const { spacings } = useTheme();
  const { agentView, triggerAgentViewChange } = useAgentView();
  const { createEvent } = useAgent();
  const { refetchMatch } = useLiveMatch(
    agentView.state.match.id,
    agentView.state.match.status
  );

  const { teamPlayersRetrieve } = UseTeamPlayers(agentView.state?.teamId);

  const penaltiesSelection = useRef<BottomSheetModal>(null);
  const penaltyEventConfirmation = useRef<BottomSheetModal>(null);
  const [isAsyncEvent, setIsAsyncEvent] = useState(false);

  const styles = penaltiesSelectionStyles(spacings);

  const [goalPlayer, setGoalPlayer] = useState<{
    id: string;
    name: string;
    number: string;
  } | null>(null);

  const playersList = useMemo(() => {
    if (!teamPlayersRetrieve?.data) return;
    return teamPlayersRetrieve?.data.map((player: Player) => {
      return {
        id: player.playerId,
        name: player.jerseyName,
        number: player.number,
      };
    });
  }, [teamPlayersRetrieve]);

  const handlePenaltyEventConfirmation = useCallback(() => {
    if (isAsyncEvent) return;
    setIsAsyncEvent(true);
    penaltyEventConfirmation.current?.dismiss();
    if (!goalPlayer?.name) return;
    if (
      agentView?.options?.selection === MatchEventTypes.PENALTY_SAVE &&
      agentView.options.sideSelection.id === AgentEventSideSelection.THEM
    ) {
      createEvent(
        MatchEventTypes.PENALTY_MISS,
        {
          matchId: agentView.state.match.id,
          who: agentView.options.sideSelection.id,
          period: agentView.state.match.status,
          timestamp: Date.now() - 2000,
        },
        () => {
          createEvent(
            agentView?.options?.selection,
            {
              matchId: agentView.state.match.id,
              playerId: goalPlayer.id,
              action: 'SAVE',
              who: AgentEventSideSelection.US,
              period: agentView.state.match.status,
            },
            () => {
              refetchMatch();
              triggerAgentViewChange();
              setIsAsyncEvent(false);
            }
          );
        }
      );
    } else {
      createEvent(
        agentView?.options?.selection,
        {
          matchId: agentView.state.match.id,
          playerId: goalPlayer.id,
          who: agentView.options.sideSelection.id,
          period: agentView.state.match.status,
        },
        () => {
          refetchMatch();
          triggerAgentViewChange();
          setIsAsyncEvent(false);
        }
      );
    }
  }, [
    agentView,
    createEvent,
    goalPlayer,
    isAsyncEvent,
    agentView.state.match.id,
    agentView.state.match.status,
    refetchMatch,
    triggerAgentViewChange,
  ]);

  return (
    <ViewContainer animated={true}>
      <Header
        title={`${i18next.t(`AGENT.PENALTY_SELECTION.${agentView.options.selection}`)} `}
        withBackButton
        onBackButtonPress={() =>
          triggerAgentViewChange({
            view:
              agentView.previousView === AgentViews.FOUL__PENS_VIEW
                ? AgentViews.FOUL__PENS_VIEW
                : AgentViews.PENS_VIEW,
          })
        }
      />
      <ScrollView showsVerticalScrollIndicator={false} bounces>
        <Select
          ref={penaltiesSelection}
          selectLabel={i18next.t(`AGENT.PENALTY_SELECTION.SELECT_PLAYER`)}
          list={playersList}
          onOpen={() => {
            penaltiesSelection.current?.present();
          }}
          onItemSelect={(player: any) => {
            penaltiesSelection.current?.dismiss();
            setGoalPlayer(player);
          }}
        />
        <View style={styles.buttonContainer}>
          <Button
            title={i18next.t(`AGENT.PENALTY_SELECTION.CANCEL`)}
            shadow
            containerStyle={styles.actionButton}
            onPress={() =>
              triggerAgentViewChange({
                view:
                  agentView.previousView === AgentViews.FOUL__PENS_VIEW
                    ? AgentViews.FOUL__PENS_VIEW
                    : AgentViews.PENS_VIEW,
              })
            }
          />
          <Button
            title={i18next.t(`AGENT.PENALTY_SELECTION.CONFIRM`)}
            disabled={!goalPlayer}
            containerStyle={styles.actionButton}
            onPress={() => penaltyEventConfirmation.current?.present()}
          />
        </View>
      </ScrollView>
      <AgentConfirmPlayEvent
        ref={penaltyEventConfirmation}
        title={`${i18next.t(`AGENT.PENALTY_SELECTION.${agentView.options.selection}_EVENT_NAME`)}`}
        firstAction={{
          label: `${i18next.t(`AGENT.PENALTY_SELECTION.PLAYER`)}`,
          number: goalPlayer?.number,
          name: goalPlayer?.name,
        }}
        buttonText={`${i18next.t(`AGENT.PENALTY_SELECTION.CONFIRM`)}`}
        onBtnPress={handlePenaltyEventConfirmation}
      />
    </ViewContainer>
  );
};

export default PenaltiesSelection;
