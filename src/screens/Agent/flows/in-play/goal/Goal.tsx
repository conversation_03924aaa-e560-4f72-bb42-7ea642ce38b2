import React, { useRef, useState, useCallback } from 'react';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { ScrollView } from 'react-native';
import {
  useTheme,
  <PERSON><PERSON>ontainer,
  Header,
  <PERSON>liderSelect,
  BoxSelector,
  AgentConfirmPlayEvent,
  MatchHeaderLight,
  Separator,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { useAgent, useLiveMatch } from '@scorescast/http-clients';
import {
  AgentEventSideSelection,
  MatchEventTypes,
  MatchPossibleTeams,
  MatchPossibleGoalTypes,
} from '@scorescast/formatters-constants';
import { useAgentView } from '../../../AgentContext';
import { AgentViews } from '../../../AgentCurrentState.config';
import { goalStyles } from './Goal.styles';
import { useAgentStore } from '@scorescast/http-clients/src/stores/useAgentStore';

const Goal: React.FC = () => {
  const { setSideSelection, sideSelection } = useAgentStore();

  const { spacings } = useTheme();
  const agentConfirmOwnGoalForUs = useRef<BottomSheetModal>(null);
  const agentConfirmThemTypeOfGoal = useRef<BottomSheetModal>(null);
  const [isAsyncEvent, setIsAsyncEvent] = useState(false);

  const { agentView, triggerAgentViewChange } = useAgentView();
  const { createEvent } = useAgent();
  const { refetchMatch } = useLiveMatch(
    agentView.state.match.id,
    agentView.state.match.status
  );

  const [themTypeOfGoal, setThemTypeOfGoal] = useState(null);
  const [selectedTeam, setSelectedTeam] = useState(sideSelection);
  const styles = goalStyles(spacings);

  const onSelectGoalType = useCallback(
    (selected: any) => {
      if (
        sideSelection.id === AgentEventSideSelection.THEM &&
        selected !== MatchEventTypes.OWN_GOAL
      ) {
        setThemTypeOfGoal(selected);
        agentConfirmThemTypeOfGoal.current?.present();
        return;
      }
      switch (selected) {
        case MatchEventTypes.SHOT:
          triggerAgentViewChange({
            view: AgentViews.GOAL_VIEW_SELECTION,
            options: {
              selection: MatchEventTypes.SHOT,
            },
          });
          break;
        case MatchEventTypes.FREEKICK:
          triggerAgentViewChange({
            view: AgentViews.GOAL_VIEW_SELECTION,
            options: {
              selection: MatchEventTypes.FREEKICK,
            },
          });
          break;
        case MatchEventTypes.PENALTY:
          triggerAgentViewChange({
            view: AgentViews.GOAL_VIEW_SELECTION,
            options: {
              selection: MatchEventTypes.PENALTY,
            },
          });
          break;
        case MatchEventTypes.OWN_GOAL:
          if (sideSelection.id === AgentEventSideSelection.US) {
            agentConfirmOwnGoalForUs.current?.present();
          } else {
            triggerAgentViewChange({
              view: AgentViews.GOAL_VIEW_SELECTION,
              options: {
                selection: MatchEventTypes.OWN_GOAL,
              },
            });
          }
          break;

        default:
          break;
      }
    },
    [sideSelection, triggerAgentViewChange]
  );

  return (
    <ViewContainer animated={true}>
      <Header
        title={i18next.t(`AGENT.GOAL_SELECTION.GOAL`)}
        withBackButton
        onBackButtonPress={triggerAgentViewChange}
      />
      <MatchHeaderLight
        match={agentView.state.match}
        selectedTeam={selectedTeam}
      />
      <Separator containerStyle={styles.separator} />
      <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
        <SliderSelect
          containerStyle={styles.sliderSelect}
          options={MatchPossibleTeams}
          isDisabled={true}
          selected={selectedTeam}
          onToggle={(
            selection: (typeof MatchPossibleTeams)[keyof typeof MatchPossibleTeams]
          ) => {
            setSelectedTeam(selection);
            setSideSelection(selection);
          }}
        />
        <BoxSelector
          containerStyle={styles.boxSelector}
          options={MatchPossibleGoalTypes}
          onSelect={onSelectGoalType}
        />
        <Separator containerStyle={styles.separator} />
      </ScrollView>
      <AgentConfirmPlayEvent
        ref={agentConfirmOwnGoalForUs}
        title={`${i18next.t(`AGENT.GOAL_SELECTION.CONFIRM`)} ${i18next.t(`AGENT.GOAL_SELECTION.GOAL`)} > Own Goal`}
        buttonText={`${i18next.t(`AGENT.GOAL_SELECTION.CONFIRM`)}`}
        infoText={`${i18next.t(`AGENT.GOAL_SELECTION.CONFIRM_OPOSITION_OWN_GOAL`)}`}
        onBtnPress={() => {
          if (isAsyncEvent) return;
          setIsAsyncEvent(true);
          createEvent(
            MatchEventTypes['OWN_GOAL'],
            {
              matchId: agentView.state.match.id,
              playerId: null,
              action: MatchEventTypes['OWN_GOAL'],
              who: sideSelection.id,
              period: agentView.state.match.status,
            },
            () => {
              refetchMatch();
              triggerAgentViewChange();
              setIsAsyncEvent(false);
            }
          );
        }}
      />
      <AgentConfirmPlayEvent
        ref={agentConfirmThemTypeOfGoal}
        title={`${i18next.t(`AGENT.GOAL_SELECTION.CONFIRM`)} ${i18next.t(`AGENT.GOAL_SELECTION.GOAL`)} > ${themTypeOfGoal}`}
        buttonText={`${i18next.t(`AGENT.GOAL_SELECTION.CONFIRM`)}`}
        onBtnPress={() => {
          if (isAsyncEvent) return;
          setIsAsyncEvent(true);
          if (!themTypeOfGoal) return;
          createEvent(
            themTypeOfGoal,
            {
              matchId: agentView.state.match.id,
              playerId: null,
              action: themTypeOfGoal,
              who: sideSelection.id,
              period: agentView.state.match.status,
            },
            () => {
              refetchMatch();
              triggerAgentViewChange();
              setIsAsyncEvent(false);
            }
          );
        }}
      />
    </ViewContainer>
  );
};

export default Goal;
