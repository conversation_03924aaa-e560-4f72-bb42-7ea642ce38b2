import React from 'react';
import {
  useTheme,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  MatchHeader,
} from '@scorescast/design-system';
import { useAgentView } from '../../../AgentContext';
import { feedViewStyles } from './FeedView.styles';
import { i18next } from '@scorescast/translations';

const FeedView: React.FC = () => {
  const { spacings } = useTheme();
  const { agentView, triggerAgentViewChange } = useAgentView();
  const styles = feedViewStyles(spacings);

  return (
    <ViewContainer animated={true}>
      <Header
        title={i18next.t(`AGENT.FEED_VIEW.TITLE`)}
        withBackButton
        onBackButtonPress={() => triggerAgentViewChange()}
      />
      <MatchHeader
        match={agentView.state.match}
        containerStyle={styles.matchHeaderContainer}
      />
      <MatchFeed match={agentView.state.match} />
    </ViewContainer>
  );
};

export default FeedView;
