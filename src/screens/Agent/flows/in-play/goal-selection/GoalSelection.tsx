import React, { useCallback, useMemo, useRef, useState } from 'react';
import { View, ScrollView } from 'react-native';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import {
  Button,
  Select,
  AgentConfirmPlayEvent,
  Header,
  useTheme,
  ViewContainer,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { MatchEventTypes } from '@scorescast/formatters-constants';
import {
  useCreateEvent,
  useRetrieveMatchDetails,
  useAgentStore,
} from '@scorescast/http-client';
import { goalSelectionStyles } from './GoalSelection.styles';
import { useAgentView } from '../../../AgentContext';
import { AgentViews } from '../../../AgentCurrentState.config';

const GoalSelection: React.FC = () => {
  const { sideSelection } = useAgentStore();

  const { spacings } = useTheme();
  const { agentView, triggerAgentViewChange } = useAgentView();

  // Use our new controller hooks
  const createEventMutation = useCreateEvent({});
  const { refetch: refetchMatch } = useRetrieveMatchDetails(
    {
      matchId: agentView.state.match.id,
    },
    {
      enabled: !!agentView.state.match.id,
    }
  );

  const playerGoalSelection = useRef<BottomSheetModal>(null);
  const playerAssistSelection = useRef<BottomSheetModal>(null);
  const goalConfirmation = useRef<BottomSheetModal>(null);

  const styles = goalSelectionStyles(spacings);

  const [isAsyncEvent, setIsAsyncEvent] = useState(false);
  const [goalPlayer, setGoalPlayer] = useState<{
    id: string;
    name: string;
    number: string;
  } | null>(null);
  const [assistlPlayer, setAssistPlayer] = useState<{
    id: string;
    name: string;
    number: string;
  } | null>(null);

  const playersList = useMemo(() => {
    if (!agentView.state.team) return;
    return agentView.state.team.map((player) => {
      return {
        id: player.playerId,
        name: player.jerseyName,
        number: player.number,
      };
    });
  }, [agentView.state.team]);

  const handleGoalConfirmation = useCallback(() => {
    if (isAsyncEvent) return;
    setIsAsyncEvent(true);
    goalConfirmation.current?.dismiss();
    if (!goalPlayer?.name) return;

    if (assistlPlayer?.name) {
      // First create assist event, then goal event
      createEventMutation.mutate(
        {
          payload: {
            playerId: parseInt(assistlPlayer.id),
            matchId: agentView.state.match.id,
            who: sideSelection.id,
            action: MatchEventTypes['PASS'],
            outcome: 'ASSIST',
            period: agentView.state.match.status,
            timestamp: Date.now() - 2000,
            latitude: 0,
            longitude: 0,
          },
        },
        {
          onSuccess: () => {
            // Then create the goal event
            createEventMutation.mutate(
              {
                payload: {
                  playerId: parseInt(goalPlayer.id),
                  matchId: agentView.state.match.id,
                  who: sideSelection.id,
                  action: MatchEventTypes[agentView?.options?.selection],
                  outcome: 'GOAL',
                  period: agentView.state.match.status,
                  timestamp: Date.now(),
                  latitude: 0,
                  longitude: 0,
                },
              },
              {
                onSuccess: () => {
                  refetchMatch();
                  triggerAgentViewChange();
                  setIsAsyncEvent(false);
                },
                onError: () => {
                  setIsAsyncEvent(false);
                },
              }
            );
          },
          onError: () => {
            setIsAsyncEvent(false);
          },
        }
      );
    } else {
      createEventMutation.mutate(
        {
          payload: {
            playerId: parseInt(goalPlayer.id),
            matchId: agentView.state.match.id,
            who: sideSelection.id,
            action: MatchEventTypes[agentView?.options?.selection],
            outcome: 'GOAL',
            period: agentView.state.match.status,
            timestamp: Date.now(),
            latitude: 0,
            longitude: 0,
          },
        },
        {
          onSuccess: () => {
            refetchMatch();
            triggerAgentViewChange();
            setIsAsyncEvent(false);
          },
          onError: () => {
            setIsAsyncEvent(false);
          },
        }
      );
    }
  }, [
    agentView,
    createEventMutation,
    isAsyncEvent,
    goalPlayer,
    assistlPlayer,
    sideSelection,
    refetchMatch,
    triggerAgentViewChange,
  ]);

  return (
    <ViewContainer animated={true}>
      <Header
        title={`${i18next.t(`AGENT.GOAL_SELECTION.GOAL`)} > ${i18next.t(`AGENT.GOAL_SELECTION.${agentView.options.selection}`)} `}
        withBackButton
        onBackButtonPress={() =>
          triggerAgentViewChange({
            view: AgentViews.GOAL_VIEW,
          })
        }
      />
      <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
        <Select
          ref={playerGoalSelection}
          selectLabel={i18next.t(`AGENT.GOAL_SELECTION.SELECT_PLAYER`)}
          list={playersList}
          onOpen={() => {
            playerGoalSelection.current?.present();
          }}
          onItemSelect={(player: any) => {
            playerGoalSelection.current?.dismiss();
            setGoalPlayer(player);
          }}
        />
        {agentView?.options?.selection === MatchEventTypes['SHOT'] && (
          <Select
            ref={playerAssistSelection}
            selectLabel={i18next.t(`AGENT.GOAL_SELECTION.SELECT_ASSIST`)}
            list={playersList}
            onOpen={() => {
              playerAssistSelection.current?.present();
            }}
            onItemSelect={(player: any) => {
              playerAssistSelection.current?.dismiss();
              setAssistPlayer(player);
            }}
          />
        )}
        <View style={styles.buttonContainer}>
          <Button
            title={i18next.t(`AGENT.GOAL_SELECTION.CANCEL`)}
            shadow
            containerStyle={styles.actionButton}
            onPress={() =>
              triggerAgentViewChange({
                view: AgentViews.GOAL_VIEW,
              })
            }
          />
          <Button
            title={i18next.t(`AGENT.GOAL_SELECTION.CONFIRM`)}
            disabled={!goalPlayer}
            secondary
            containerStyle={styles.actionButton}
            onPress={() => goalConfirmation.current?.present()}
          />
        </View>
      </ScrollView>
      <AgentConfirmPlayEvent
        ref={goalConfirmation}
        title={`${i18next.t(`AGENT.GOAL_SELECTION.CONFIRM`)} ${i18next.t(`AGENT.GOAL_SELECTION.GOAL`)} > ${i18next.t(`AGENT.GOAL_SELECTION.${agentView.options.selection}`)}`}
        firstAction={{
          label: `${i18next.t(`AGENT.GOAL_SELECTION.PLAYER`)}`,
          number: goalPlayer?.number,
          name: goalPlayer?.name,
        }}
        secondAction={{
          label: `${i18next.t(`AGENT.GOAL_SELECTION.ASSIST`)}`,
          number: assistlPlayer?.number,
          name: assistlPlayer?.name,
        }}
        buttonText={`${i18next.t(`AGENT.GOAL_SELECTION.CONFIRM`)}`}
        onBtnPress={handleGoalConfirmation}
      />
    </ViewContainer>
  );
};

export default GoalSelection;
