import { StyleSheet } from 'react-native';
import { SpacingsStyle } from '@scorescast/design-system';

export const goalSelectionStyles = (spacings: typeof SpacingsStyle) =>
  StyleSheet.create({
    matchHeaderContainer: {
      paddingVertical: spacings.xxxlarge,
      paddingHorizontal: spacings.large,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: spacings.xxxxlarge,
    },
    actionButton: {
      width: '48%',
    },
  });
