import React, { useRef, useState, useCallback } from 'react';
import {
  ConfirmBottomSheet,
  useTheme,
  <PERSON>C<PERSON><PERSON>,
  Header,
  MatchHeaderLight,
  SliderSelect,
  BoxSelector,
  Separator,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import {
  MatchPossibleTeams,
  MatchEventTypes,
  MatchPossibleFoulTypes,
} from '@scorescast/formatters-constants';
import { useAgentView } from '../../../AgentContext';
import { AgentViews } from '../../../AgentCurrentState.config';
import { foulStyles } from './Foul.styles';
import { ScrollView } from 'react-native';
import { useAgentStore } from '@scorescast/http-clients/src/stores/useAgentStore';

const Foul: React.FC = () => {
  const { setSideSelection, sideSelection } = useAgentStore();

  const { spacings } = useTheme();
  const { agentView, triggerAgentViewChange } = useAgentView();
  const [selectedTeam, setSelectedTeam] = useState(sideSelection);
  const styles = foulStyles(spacings);

  const onSelectFoulType = useCallback(
    (selected: any) => {
      switch (selected) {
        case MatchEventTypes.FOUL_FREEKICK:
          triggerAgentViewChange({
            view: AgentViews.FOUL_VIEW_SELECTION,
            options: {
              selection: MatchEventTypes.FOUL_FREEKICK,
            },
          });
          break;
        case MatchEventTypes.FOUL_PENALTY:
          triggerAgentViewChange({
            view: AgentViews.FOUL_VIEW_SELECTION,
            options: {
              selection: MatchEventTypes.FOUL_PENALTY,
            },
          });
          break;
        case MatchEventTypes.CARDS:
          triggerAgentViewChange({
            view: AgentViews.FOUL_VIEW_SELECTION,
            options: {
              selection: MatchEventTypes.CARDS,
            },
          });
          break;
        default:
          break;
      }
    },
    [triggerAgentViewChange]
  );

  return (
    <ViewContainer animated={true}>
      <Header
        title={i18next.t(`AGENT.FOUL_SELECTION.FOUL`)}
        withBackButton
        onBackButtonPress={() => triggerAgentViewChange()}
      />
      <MatchHeaderLight
        match={agentView.state.match}
        selectedTeam={selectedTeam}
      />
      <Separator containerStyle={styles.separator} />
      <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
        <SliderSelect
          containerStyle={styles.sliderSelect}
          options={MatchPossibleTeams}
          isDisabled={true}
          selected={selectedTeam}
          onToggle={(
            selection: (typeof MatchPossibleTeams)[keyof typeof MatchPossibleTeams]
          ) => {
            setSelectedTeam(selection);
            setSideSelection(selection);
          }}
        />
        <BoxSelector
          containerStyle={styles.boxSelector}
          options={MatchPossibleFoulTypes}
          onSelect={onSelectFoulType}
        />
        <Separator containerStyle={styles.separator} />
      </ScrollView>
    </ViewContainer>
  );
};

export default Foul;
