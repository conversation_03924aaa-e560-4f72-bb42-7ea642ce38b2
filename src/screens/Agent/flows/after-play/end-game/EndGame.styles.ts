import { StyleSheet } from 'react-native';
import {
  TypographyStyles,
  SpacingsStyle,
  ColorStyles,
} from '@scorescast/design-system';

export const endGameStyles = (
  typography: typeof TypographyStyles,
  spacings: typeof SpacingsStyle,
  colors: typeof ColorStyles
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.neutral900,
      paddingHorizontal: spacings.xxlarge,
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: spacings.xxxlarge,
    },
    topSection: {
      width: '100%',
    },
    title: {
      ...typography['xxLarge-bold'],
      color: colors.inverse900,
      textAlign: 'center',
    },
    description: {
      ...typography['small'],
      color: colors.inverse900,
      textAlign: 'center',
      paddingTop: spacings.xlarge,
      marginHorizontal: spacings.xlarge,
    },
    separator: {
      marginVertical: spacings.xxxxlarge,
    },
    complete: {
      width: '100%',
      height: 300,
      paddingBottom: spacings.xlarge,
    },
    congrats: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: 300,
      width: '100%',
    },
    button: {
      width: '100%',
    },
  });
