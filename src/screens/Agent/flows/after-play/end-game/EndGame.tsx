import React, { useMemo } from 'react';
import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import LottieView from 'lottie-react-native';
import {
  useTheme,
  Button,
  Lotties,
  Separator,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { endGameStyles } from './EndGame.styles';
import { Screens } from '../../../../../navigator/navigator';
import { useAgentView } from '../../../AgentContext';

const EndGame: React.FC = () => {
  const { spacings, typography, colors } = useTheme();
  const navigation = useNavigation<any>();
  const styles = endGameStyles(typography, spacings, colors);
  const {
    agentView: {
      state: { match },
    },
  } = useAgentView();

  const randomLottie = useMemo(() => {
    const lotties = [
      Lotties.agentCongrats1,
      Lotties.agentCongrats2,
      Lotties.agentCongrats3,
      Lotties.agentCongrats4,
    ];
    return lotties[Math.floor(Math.random() * lotties.length)];
  }, []);

  return (
    <View style={styles.container}>
      <View />
      <View style={styles.topSection}>
        <LottieView
          source={randomLottie}
          autoPlay
          loop={true}
          style={styles.complete}
          resizeMode="cover"
        />
        <Separator containerStyle={styles.separator} />
        <Text style={[styles.title]}>{i18next.t('AGENT.END_GAME.TITLE')}</Text>
        <Text style={[styles.description]}>
          {i18next.t('AGENT.END_GAME.DESCRIPTION')}
        </Text>
        <Separator containerStyle={styles.separator} />
      </View>
      <Button
        tertiary
        title={i18next.t('AGENT.END_GAME.BUTTON')}
        containerStyle={styles.button}
        onPress={() => {
          navigation.goBack();
          navigation.navigate(Screens.MatchScreen, { matchId: match.id });
        }}
      />
    </View>
  );
};

export default EndGame;
