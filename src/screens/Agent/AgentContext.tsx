import { Match } from '@scorescast/formatters-constants/src/models/match';
import React, { createContext, useState, ReactNode, useContext } from 'react';
import { AgentViews, buildAgentCurrentState } from './AgentCurrentState.config';
import { FullTeam } from '@scorescast/formatters-constants/src/models/team';

export interface CurrentAgentState {
  previousView: string | null;
  currentView: string | null;
  options: any;
  state: {
    match: Match;
    team: FullTeam[];
    teamId: number;
  };
}

export interface ChangeParams {
  view?: AgentViews;
  options?: any;
}

interface AgentContextType {
  agentView: CurrentAgentState;
  triggerAgentViewChange: (changeParams?: ChangeParams) => void;
}

const AgentContext = createContext<AgentContextType | undefined>(undefined);

export const defaultCurrentAgentState: CurrentAgentState = {
  previousView: null,
  currentView: null,
  options: null,
  state: {
    match: null as unknown as Match,
    team: [] as FullTeam[],
  },
};

export const AgentProvider: React.FC<{
  children: ReactNode;
  match: Match;
  team: FullTeam[];
  teamId: number;
}> = ({ children, match, team, teamId }) => {
  const [agentView, setAgentView] = useState<CurrentAgentState>(
    defaultCurrentAgentState
  );

  const triggerAgentViewChange = (changeParams?: ChangeParams) => {
    if (!match && !team) return;
    setAgentView(buildAgentCurrentState(changeParams, match, team, teamId));
  };

  return (
    <AgentContext.Provider value={{ agentView, triggerAgentViewChange }}>
      {children}
    </AgentContext.Provider>
  );
};

export const useAgentView = (): AgentContextType => {
  const context = useContext(AgentContext);
  if (!context) {
    throw new Error('useAgentView must be used within an AgentProvider');
  }
  return context;
};

export default AgentContext;
