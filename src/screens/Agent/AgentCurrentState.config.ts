import {
  Match,
  MatchSetup,
} from '@scorescast/formatters-constants/src/models/match';
import { ChangeParams, defaultCurrentAgentState } from './AgentContext';
import {
  ClaimStatuses,
  MatchEventTypes,
  MatchPossibleStatus,
} from '@scorescast/formatters-constants/src/constants/match';
import { FullTeam } from '@scorescast/formatters-constants/src/models/team';

export enum AgentViews {
  FALLBACK_VIEW = 'FALLBACK_VIEW', //fallback if undefined
  KICKOFF = 'KICKOFF', //before start views
  CLAIM_VIEW = 'CLAIM_VIEW', //before start views
  TEAM_MANAGEMENT_VIEW = 'TEAM_MANAGEMENT_VIEW', //before start views
  PLAYER_ACTION_VIEW = 'PLAYER_ACTION_VIEW', //before start views

  GOAL_VIEW = 'GOAL_VIEW',
  GOAL_VIEW_SELECTION = 'GOAL_VIEW_SELECTION',

  FOUL_VIEW = 'FOUL_VIEW',
  FOUL_VIEW_SELECTION = 'FOUL_VIEW_SELECTION',
  FEED_VIEW = 'FEED_VIEW',

  P1_VIEW = 'P1_VIEW',
  P1_VIEW__1_PERIODS = 'P1_VIEW__1_PERIODS',
  P1_VIEW__4_PERIODS = 'P1_VIEW__4_PERIODS',
  P2_VIEW__4_PERIODS = 'P2_VIEW__4_PERIODS',
  P2_VIEW = 'P2_VIEW',
  P3_VIEW = 'P3_VIEW', //applies for 4 periods case match
  P4_VIEW = 'P4_VIEW', //applies for 4 periods case match

  P1_BREAK_VIEW = 'P1_BREAK_VIEW', //applies for 4 periods case match
  P3_BREAK_VIEW = 'P3_BREAK_VIEW', //applies for 4 periods case match
  HT_VIEW = 'HT_VIEW',
  HT_VIEW__4_PERIODS = 'HT_VIEW__4_PERIODS',
  FT_VIEW = 'FT_VIEW',
  FT_VIEW__1_PERIODS = 'FT_VIEW__1_PERIODS',

  ET_VIEW = 'ET_VIEW', //overtime views
  ET1_VIEW = 'ET1_VIEW', //overtime views
  ET2_VIEW = 'ET2_VIEW', //overtime views

  ETHT_VIEW = 'ETHT_VIEW', //overtime views
  AET_VIEW = 'AET_VIEW', //overtime views

  PENS_VIEW = 'PENS_VIEW',
  FOUL__PENS_VIEW = 'FOUL__PENS_VIEW',
  PENS_VIEW_SELECTION = 'PENS_VIEW_SELECTION',

  END_VIEW = 'END_VIEW',
}

export const buildAgentCurrentState = (
  changeParams: ChangeParams | undefined,
  match: Match,
  team: FullTeam[],
  teamId: number
) => {
  let _return;
  if (!changeParams?.view) {
    if (
      match?.claimStatus === ClaimStatuses.CLAIMED_BY_YOU &&
      match?.status === 'NS'
    ) {
      _return = possibleStates[AgentViews.KICKOFF];
    } else if (
      match?.claimStatus === ClaimStatuses.CAN_CLAIM ||
      match?.claimStatus === ClaimStatuses.CLAIMED
    ) {
      _return = possibleStates[AgentViews.CLAIM_VIEW];
    } else {
      //else decide view by match[status]
      switch (match?.status) {
        case MatchPossibleStatus['P1']: {
          if (match.matchSetup === MatchSetup.P4) {
            _return = possibleStates[AgentViews.P1_VIEW__4_PERIODS];
          } else if (match.matchSetup === MatchSetup.P1) {
            _return = possibleStates[AgentViews.P1_VIEW__1_PERIODS];
          } else {
            _return = possibleStates[AgentViews.P1_VIEW];
          }
          break;
        }
        case MatchPossibleStatus['P1_BREAK']: {
          _return = possibleStates[AgentViews.P1_BREAK_VIEW];
          break;
        }
        case MatchPossibleStatus['P3_BREAK']: {
          _return = possibleStates[AgentViews.P3_BREAK_VIEW];
          break;
        }
        case MatchPossibleStatus['HT']: {
          if (match.matchSetup === MatchSetup.P4) {
            _return = possibleStates[AgentViews.HT_VIEW__4_PERIODS];
          } else {
            _return = possibleStates[AgentViews.HT_VIEW];
          }
          break;
        }
        case MatchPossibleStatus['P2']: {
          if (match.matchSetup === MatchSetup.P4) {
            _return = possibleStates[AgentViews.P2_VIEW__4_PERIODS];
          } else {
            _return = possibleStates[AgentViews.P2_VIEW];
          }
          break;
        }
        case MatchPossibleStatus['P3']: {
          _return = possibleStates[AgentViews.P3_VIEW];
          break;
        }
        case MatchPossibleStatus['P4']: {
          _return = possibleStates[AgentViews.P4_VIEW];
          break;
        }
        case MatchPossibleStatus['FT']: {
          if (match.matchSetup === MatchSetup.P1) {
            _return = possibleStates[AgentViews.FT_VIEW__1_PERIODS];
          } else {
            _return = possibleStates[AgentViews.FT_VIEW];
          }
          break;
        }
        case MatchPossibleStatus['ET']: {
          _return = possibleStates[AgentViews.ET_VIEW];
          break;
        }
        case MatchPossibleStatus['ET1']: {
          _return = possibleStates[AgentViews.ET1_VIEW];
          break;
        }
        case MatchPossibleStatus['ETHT']: {
          _return = possibleStates[AgentViews.ETHT_VIEW];
          break;
        }
        case MatchPossibleStatus['ET2']: {
          _return = possibleStates[AgentViews.ET2_VIEW];
          break;
        }
        case MatchPossibleStatus['AET']: {
          _return = possibleStates[AgentViews.AET_VIEW];
          break;
        }
        case MatchPossibleStatus['PENS']: {
          _return = possibleStates[AgentViews.PENS_VIEW];
          break;
        }
        case MatchPossibleStatus['END']: {
          _return = possibleStates[AgentViews.END_VIEW];
          break;
        }
        default: {
          _return = defaultCurrentAgentState;
        }
      }
    }
  } else {
    //handle specific agent actions that don't reflect a match[status] change (e.g: goals/fouls)
    switch (changeParams.view) {
      case AgentViews.TEAM_MANAGEMENT_VIEW: {
        _return = {
          ...possibleStates[AgentViews.TEAM_MANAGEMENT_VIEW],
          previousView: match.status,
          options: {
            teamId: teamId,
          },
        };
        break;
      }
      case AgentViews.PLAYER_ACTION_VIEW: {
        _return = {
          ...possibleStates[AgentViews.PLAYER_ACTION_VIEW],
          previousView: AgentViews.TEAM_MANAGEMENT_VIEW,
          options: {
            player: changeParams?.options?.player,
            action: changeParams?.options?.action,
            teamId: teamId,
            players: changeParams?.options?.players,
          },
        };
        break;
      }
      case AgentViews.GOAL_VIEW: {
        _return = {
          ...possibleStates[AgentViews.GOAL_VIEW],
          previousView: match.status,
        };
        break;
      }
      case AgentViews.GOAL_VIEW_SELECTION: {
        _return = {
          ...possibleStates[AgentViews.GOAL_VIEW_SELECTION],
          previousView: AgentViews.GOAL_VIEW,
          options: {
            selection: changeParams?.options?.selection,
          },
        };
        break;
      }
      case AgentViews.FOUL__PENS_VIEW: {
        _return = {
          ...possibleStates[AgentViews.FOUL__PENS_VIEW],
        };
        break;
      }
      case AgentViews.PENS_VIEW: {
        _return = {
          ...possibleStates[AgentViews.PENS_VIEW],
        };
        break;
      }
      case AgentViews.PENS_VIEW_SELECTION: {
        _return = {
          ...possibleStates[AgentViews.PENS_VIEW_SELECTION],
          previousView:
            changeParams?.options?.previousView || AgentViews.PENS_VIEW,
          options: {
            selection: changeParams?.options?.selection,
            sideSelection: changeParams?.options?.sideSelection,
          },
        };
        break;
      }
      case AgentViews.FOUL_VIEW: {
        _return = {
          ...possibleStates[AgentViews.FOUL_VIEW],
          previousView: match.status,
          options: {},
        };
        break;
      }
      case AgentViews.FEED_VIEW: {
        _return = {
          ...possibleStates[AgentViews.FEED_VIEW],
          previousView: '',
        };
        break;
      }
      case AgentViews.FOUL_VIEW_SELECTION: {
        _return = {
          ...possibleStates[AgentViews.FOUL_VIEW_SELECTION],
          previousView: AgentViews.FOUL_VIEW,
          options: {
            selection: changeParams?.options?.selection,
          },
        };
        break;
      }
      default: {
        _return = defaultCurrentAgentState;
      }
    }
  }

  return {
    ..._return,
    state: {
      match,
      team,
      teamId,
    },
  };
};

const possibleStates = {
  [AgentViews.TEAM_MANAGEMENT_VIEW]: {
    previousView: '',
    currentView: AgentViews.TEAM_MANAGEMENT_VIEW,
    options: {
      player: null,
      action: null,
    },
  },
  [AgentViews.PLAYER_ACTION_VIEW]: {
    previousView: AgentViews.TEAM_MANAGEMENT_VIEW,
    currentView: AgentViews.PLAYER_ACTION_VIEW,
    options: {
      players: null,
      player: null,
      action: null,
      teamId: null,
    },
  },
  [AgentViews.KICKOFF]: {
    previousView: null,
    currentView: AgentViews.KICKOFF,
    options: {
      action: null,
      editTeamVisibility: true,
      kickoffVisibility: true,
      endGameVisibility: false,
      penaltyVisibility: false,
      nextStepAction: MatchEventTypes['KICKOFF'],
    },
  },
  [AgentViews.HT_VIEW]: {
    previousView: null,
    currentView: AgentViews.HT_VIEW,
    options: {
      action: null,
      editTeamVisibility: true,
      kickoffVisibility: true,
      endGameVisibility: false,
      penaltyVisibility: false,
      nextStepAction: MatchEventTypes['P2_KICKOFF'],
    },
  },
  [AgentViews.HT_VIEW__4_PERIODS]: {
    previousView: null,
    currentView: AgentViews.HT_VIEW__4_PERIODS,
    options: {
      action: null,
      editTeamVisibility: true,
      kickoffVisibility: true,
      endGameVisibility: false,
      penaltyVisibility: false,
      nextStepAction: MatchEventTypes['P3_KICKOFF'],
    },
  },
  [AgentViews.P1_BREAK_VIEW]: {
    previousView: null,
    currentView: AgentViews.P1_BREAK_VIEW,
    options: {
      action: null,
      editTeamVisibility: true,
      kickoffVisibility: true,
      endGameVisibility: false,
      penaltyVisibility: false,
      nextStepAction: MatchEventTypes['P2_KICKOFF'],
    },
  },
  [AgentViews.P3_BREAK_VIEW]: {
    previousView: null,
    currentView: AgentViews.P3_BREAK_VIEW,
    options: {
      action: null,
      editTeamVisibility: true,
      kickoffVisibility: true,
      endGameVisibility: false,
      penaltyVisibility: false,
      nextStepAction: MatchEventTypes['P4_KICKOFF'],
    },
  },
  [AgentViews.FT_VIEW__1_PERIODS]: {
    previousView: null,
    currentView: AgentViews.FT_VIEW__1_PERIODS,
    options: {
      action: null,
      editTeamVisibility: true,
      kickoffVisibility: true,
      endGameVisibility: true,
      penaltyVisibility: true,
      nextStepAction: MatchEventTypes['ET_KICKOFF__1_PERIODS'],
      endGameAction: MatchEventTypes['END_GAME'],
      penaltiesAction: MatchEventTypes['PENS_START'],
    },
  },
  [AgentViews.FT_VIEW]: {
    previousView: null,
    currentView: AgentViews.FT_VIEW,
    options: {
      action: null,
      editTeamVisibility: true,
      kickoffVisibility: true,
      endGameVisibility: true,
      penaltyVisibility: true,
      nextStepAction: MatchEventTypes['ET_KICKOFF'],
      endGameAction: MatchEventTypes['END_GAME'],
      penaltiesAction: MatchEventTypes['PENS_START'],
    },
  },
  [AgentViews.ETHT_VIEW]: {
    previousView: null,
    currentView: AgentViews.ETHT_VIEW,
    options: {
      action: null,
      editTeamVisibility: true,
      kickoffVisibility: true,
      endGameVisibility: true,
      penaltyVisibility: false,
      nextStepAction: MatchEventTypes['ET2_KICKOFF'],
      endGameAction: MatchEventTypes['END_GAME'],
    },
  },
  [AgentViews.AET_VIEW]: {
    previousView: null,
    currentView: AgentViews.AET_VIEW,
    options: {
      action: null,
      editTeamVisibility: true,
      kickoffVisibility: false,
      endGameVisibility: true,
      penaltyVisibility: true,
      penaltiesAction: MatchEventTypes['PENS_START'],
      endGameAction: MatchEventTypes['END_GAME'],
    },
  },
  [AgentViews.CLAIM_VIEW]: {
    previousView: null,
    currentView: AgentViews.CLAIM_VIEW,
    options: {
      action: null,
    },
  },
  [AgentViews.FEED_VIEW]: {
    previousView: null,
    currentView: AgentViews.FEED_VIEW,
  },
  [AgentViews.P1_VIEW]: {
    previousView: null,
    currentView: AgentViews.P1_VIEW,
    options: {
      action: null,
      nextStepAction: MatchEventTypes['P1_END'],
    },
  },
  [AgentViews.P1_VIEW__1_PERIODS]: {
    previousView: null,
    currentView: AgentViews.P1_VIEW__1_PERIODS,
    options: {
      action: null,
      nextStepAction: MatchEventTypes['P1_END__1_PERIODS'],
    },
  },
  [AgentViews.P1_VIEW__4_PERIODS]: {
    previousView: null,
    currentView: AgentViews.P1_VIEW__4_PERIODS,
    options: {
      action: null,
      nextStepAction: MatchEventTypes['P1_END__4_PERIODS'],
    },
  },
  [AgentViews.P2_VIEW__4_PERIODS]: {
    previousView: null,
    currentView: AgentViews.P2_VIEW__4_PERIODS,
    options: {
      action: null,
      nextStepAction: MatchEventTypes['P2_END__4_PERIODS'],
    },
  },
  [AgentViews.P2_VIEW]: {
    previousView: null,
    currentView: AgentViews.P2_VIEW,
    options: {
      action: null,
      nextStepAction: MatchEventTypes['P2_END'],
    },
  },
  [AgentViews.P3_VIEW]: {
    previousView: null,
    currentView: AgentViews.P3_VIEW,
    options: {
      action: null,
      nextStepAction: MatchEventTypes['P3_END'],
    },
  },
  [AgentViews.P4_VIEW]: {
    previousView: null,
    currentView: AgentViews.P4_VIEW,
    options: {
      action: null,
      nextStepAction: MatchEventTypes['P4_END'],
    },
  },
  [AgentViews.ET_VIEW]: {
    previousView: null,
    currentView: AgentViews.ET_VIEW,
    options: {
      action: null,
      nextStepAction: MatchEventTypes['ET_END'],
    },
  },
  [AgentViews.ET1_VIEW]: {
    previousView: null,
    currentView: AgentViews.ET1_VIEW,
    options: {
      action: null,
      nextStepAction: MatchEventTypes['ET1_END'],
    },
  },
  [AgentViews.ET2_VIEW]: {
    previousView: null,
    currentView: AgentViews.ET2_VIEW,
    options: {
      action: null,
      nextStepAction: MatchEventTypes['ET2_END'],
    },
  },
  [AgentViews.GOAL_VIEW]: {
    previousView: '',
    currentView: AgentViews.GOAL_VIEW,
    options: {
      action: null,
    },
  },
  [AgentViews.GOAL_VIEW_SELECTION]: {
    previousView: '',
    currentView: AgentViews.GOAL_VIEW_SELECTION,
    options: {
      action: null,
      selection: null,
    },
  },
  [AgentViews.PENS_VIEW_SELECTION]: {
    previousView: '',
    currentView: AgentViews.PENS_VIEW_SELECTION,
    options: {
      action: null,
      selection: null,
    },
  },
  [AgentViews.FOUL_VIEW]: {
    previousView: '',
    currentView: AgentViews.FOUL_VIEW,
    options: {
      action: null,
    },
  },
  [AgentViews.FOUL_VIEW_SELECTION]: {
    previousView: '',
    currentView: AgentViews.FOUL_VIEW_SELECTION,
    options: {
      action: null,
      selection: null,
    },
  },
  [AgentViews.END_VIEW]: {
    previousView: null,
    currentView: AgentViews.END_VIEW,
    options: {
      action: null,
    },
  },
  [AgentViews.FOUL__PENS_VIEW]: {
    previousView: null,
    currentView: AgentViews.FOUL__PENS_VIEW,
    options: {
      action: null,
    },
  },
  [AgentViews.PENS_VIEW]: {
    previousView: null,
    currentView: AgentViews.PENS_VIEW,
    options: {
      action: null,
      endGameAction: MatchEventTypes['END_GAME'],
    },
  },
};
