import React, { useEffect } from 'react';
import { useAgentView } from './AgentContext';
import agentConfig from './AgentConfig';
import { AgentViews } from './AgentCurrentState.config';
import { Loader } from '@scorescast/design-system';
import { useAgentStore } from '@scorescast/http-clients/src/stores/useAgentStore';

const AgentWrapper: React.FC<any> = ({ match }) => {
  const { setCurrentMatchLocation } = useAgentStore();
  const { agentView, triggerAgentViewChange } = useAgentView();

  const ActiveComponent =
    agentConfig[agentView?.currentView ?? AgentViews.FALLBACK_VIEW];

  useEffect(() => {
    if (match?.status) {
      setCurrentMatchLocation(match.location);
      //TODO refactor agent solution . this is temporary
      if (
        [
          AgentViews.FOUL__PENS_VIEW,
          AgentViews.FEED_VIEW,
          AgentViews.GOAL_VIEW,
          AgentViews.FOUL_VIEW,
          AgentViews.PENS_VIEW_SELECTION,
          AgentViews.GOAL_VIEW_SELECTION,
          AgentViews.FOUL_VIEW_SELECTION,
          AgentViews.TEAM_MANAGEMENT_VIEW,
          AgentViews.PLAYER_ACTION_VIEW,
        ].includes(agentView.currentView)
      )
        return;

      triggerAgentViewChange();
    }
  }, [match]);

  if (!ActiveComponent) return <Loader />;
  return <ActiveComponent />;
};

export default AgentWrapper;
