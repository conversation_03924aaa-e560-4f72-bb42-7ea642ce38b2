import { RouteProp } from '@react-navigation/native';
import {
  useTheme,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  MatchStats,
  MatchFeed,
  Loader,
} from '@scorescast/design-system';
import { useLiveMatch, useUser } from '@scorescast/http-clients';
import { MatchTabsKey } from '@scorescast/formatters-constants';
import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { Screens } from '../../navigator/navigator';
import { HomeStackParamList } from '../../navigator/HomeNavigator';
import { matchScreenStyles } from './MatchScreen.styles';
import { getHeaderTitle } from '../../helpers/screen-title';
import { matchTabs } from '../../constants/tabs';

type MatchScreenRouteProp = RouteProp<HomeStackParamList, Screens.MatchScreen>;

interface Props {
  route: MatchScreenRouteProp;
}

const MatchScreen: React.FC<Props> = ({ route }) => {
  const { matchId, status } = route.params;
  const [headerTitle, setHeaderTitle] = useState('');
  const [selectedTab, setSelectedTab] = useState(matchTabs[0]);
  const navigation = useNavigation<any>();
  const { match, isMatchLoading, refetchMatch } = useLiveMatch(matchId, status);
  const { reactEvent } = useUser();
  const { spacings } = useTheme();
  const styles = matchScreenStyles(spacings);

  useEffect(() => {
    if (match?.data) {
      setHeaderTitle(getHeaderTitle(match.data.status));
    }
  }, [match]);

  const renderTabContent = () => {
    switch (selectedTab.key) {
      case MatchTabsKey.STATS:
        return <MatchStats />;
      case MatchTabsKey.FEED:
        return (
          <MatchFeed
            match={match.data}
            refetchMatch={refetchMatch}
            reactEvent={reactEvent}
          />
        );
    }
  };

  if (isMatchLoading || !match) {
    return <Loader />;
  }

  return (
    <ViewContainer>
      <Header
        title={headerTitle}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />
      {match && match.data && (
        <MatchHeader
          match={match.data}
          containerStyle={styles.matchHeaderContainer}
        />
      )}
      <Tabs
        containerStyle={styles.tabsContainer}
        tabs={matchTabs.map((t) => t.label)}
        onTabPress={(tab) =>
          setSelectedTab(matchTabs.find((t) => t.label === tab))
        }
      />
      {match && match.data && renderTabContent()}
    </ViewContainer>
  );
};

export default MatchScreen;
