import React, { useEffect, useState, useRef } from 'react';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { View, Text } from 'react-native';
import {
  <PERSON><PERSON>,
  ViewContainer,
  useTheme,
  Loader,
  IconInfoList,
  BottomSheet,
  Input,
  Button,
} from '@scorescast/design-system';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { i18next } from '@scorescast/translations';
import { useClub } from '@scorescast/http-clients';
import { teamAdminsScreenStyles } from './TeamAdminsScreen.styles';

interface Admin {
  firstName: string;
  lastName: string;
  email: string;
}

type TeamAdminsScreenRouteProp = RouteProp<
  {
    TeamAdminsScreen: {
      teamId: number;
    };
  },
  'TeamAdminsScreen'
>;

const TeamAdminsScreen = () => {
  const { colors, spacings } = useTheme();
  const navigation = useNavigation();
  const route = useRoute<TeamAdminsScreenRouteProp>();
  const { retrieveTeamAdmins } = useClub();
  const styles = teamAdminsScreenStyles(colors, spacings);

  const [admins, setAdmins] = useState<Admin[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedAdmin, setSelectedAdmin] = useState<Admin | null>(null);
  const adminDetailsRef = useRef<BottomSheetModal>(null);

  useEffect(() => {
    const fetchTeamAdmins = async () => {
      try {
        const teamId = route.params.teamId;
        const response = await retrieveTeamAdmins(teamId);

        if (response?.status === 'SUCCESS' && response?.data) {
          setAdmins(response.data);
        }
      } catch (error) {
        console.error('Error fetching team admins:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (route.params.teamId) {
      fetchTeamAdmins();
    }
  }, [route.params.teamId]);

  const handleAdminPress = (admin: Admin) => {
    setSelectedAdmin(admin);
    adminDetailsRef.current?.present();
  };

  const handleRemoveAccess = () => {
    // TODO: Implement remove access functionality
    adminDetailsRef.current?.dismiss();
  };

  const closeAdminDetails = () => {
    adminDetailsRef.current?.dismiss();
    setSelectedAdmin(null);
  };

  const handleAddAdmin = () => {
    // TODO: Implement add admin functionality
    console.log('Add admin clicked for teamId:', route.params.teamId);
  };

  if (isLoading) {
    return <Loader />;
  }

  return (
    <ViewContainer>
      <Header
        title={i18next.t('TEAM.TEAM_ADMINS')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      <Button
        title={i18next.t('TEAM.ADD_ADMIN')}
        onPress={handleAddAdmin}
        secondary
        containerStyle={styles.addAdminButton}
      />

      {admins.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: colors.inverse400 }]}>
            {i18next.t('TEAM.NO_ADMINS')}
          </Text>
        </View>
      ) : (
        <View style={styles.adminsContainer}>
          <IconInfoList
            items={admins.map((admin, index) => ({
              id: `admin-${admin.email}-${index}`,
              title: `${admin.firstName} ${admin.lastName}`,
              subtitle: admin.email,
              noIcon: true,
              onPress: () => handleAdminPress(admin),
            }))}
          />
        </View>
      )}

      <BottomSheet ref={adminDetailsRef}>
        <View style={styles.bottomSheetContainer}>
          <Text style={[styles.bottomSheetTitle, { color: colors.inverse900 }]}>
            {i18next.t('TEAM.ADMIN_DETAILS')}
          </Text>

          {selectedAdmin && (
            <View style={styles.adminDetailsContainer}>
              <Input
                label={i18next.t('TEAM.FIRST_NAME')}
                value={selectedAdmin.firstName}
                editable={false}
                containerStyle={styles.inputContainer}
              />

              <Input
                label={i18next.t('TEAM.LAST_NAME')}
                value={selectedAdmin.lastName}
                editable={false}
                containerStyle={styles.inputContainer}
              />

              <Input
                label={i18next.t('TEAM.EMAIL')}
                value={selectedAdmin.email}
                editable={false}
                containerStyle={styles.inputContainer}
              />

              <Button
                title={i18next.t('TEAM.REMOVE_ACCESS')}
                onPress={handleRemoveAccess}
                containerStyle={styles.removeAccessButton}
                containerTitleStyle={styles.removeAccessButtonText}
              />
            </View>
          )}
        </View>
      </BottomSheet>
    </ViewContainer>
  );
};

export default TeamAdminsScreen;
