import { StyleSheet } from 'react-native';
import { ColorStyles, SpacingStyles } from '@scorescast/design-system';

export const teamAdminsScreenStyles = (
  colors: typeof ColorStyles,
  spacings?: typeof SpacingStyles
) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacings?.large || 16,
    },
    emptyText: {
      fontSize: 16,
      textAlign: 'center',
    },
    addAdminButton: {
      marginTop: spacings?.medium || 12,
      marginBottom: spacings?.medium || 12,
      width: '50%',
      alignSelf: 'center',
    },
    adminsContainer: {
      flex: 1,
      padding: spacings?.medium || 12,
    },
    bottomSheetContainer: {
      padding: spacings?.large || 16,
    },
    bottomSheetTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      textAlign: 'center',
      marginBottom: spacings?.large || 16,
    },
    adminDetailsContainer: {
      gap: spacings?.medium || 12,
    },
    inputContainer: {
      marginBottom: spacings?.small || 8,
    },
    removeAccessButton: {
      marginTop: spacings?.large || 16,
      backgroundColor: colors.neutral700,
      borderRadius: 0,
    },
    removeAccessButtonText: {
      color: colors.red,
      fontSize: 16,
    },
  });
