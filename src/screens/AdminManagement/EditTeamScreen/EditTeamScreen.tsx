import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Text,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { AdminNavigationProp } from '../../../navigator/AdminNavigator';
import {
  useTheme,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  ImageUploader,
  ViewContainer,
  Select,
} from '@scorescast/design-system';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { i18next } from '@scorescast/translations';
import { Input } from '@scorescast/design-system';
import {
  useRetrieveUserDetails,
  useRetrieveAdminTeams,
  useCreateTeam,
  useUpdateTeam,
  useDeleteTeamById,
  useAppStore,
} from '@scorescast/http-client';
import { editTeamScreenStyles } from './EditTeamScreen.styles';

interface Team {
  id: number;
  name: string;
  image: string;
  shortName: string;
  ageGroup?: string;
  clubName?: string;
}

type EditTeamScreenParams = {
  teamId?: number;
  isAddMode?: boolean;
  clubId?: number;
  clubName?: string;
  teamDetails?: {
    name: string;
    shortName: string;
    image: string;
    ageGroup: string;
  };
};

type EditTeamScreenRouteProp = RouteProp<
  { EditTeamScreen: EditTeamScreenParams },
  'EditTeamScreen'
>;

const EditTeamScreen: React.FC = () => {
  const { colors } = useTheme();
  const navigation = useNavigation<AdminNavigationProp>();
  const route = useRoute<EditTeamScreenRouteProp>();
  // Use our new controller hooks
  const { data: adminForTeams, refetch: refetchRetrieveAdminForTeams } =
    useRetrieveAdminTeams({
      enabled: true,
    });

  const createTeamMutation = useCreateTeam({});
  const updateTeamMutation = useUpdateTeam({});

  const isCreateTeamLoading = createTeamMutation.isPending;
  const isUpdateTeamLoading = updateTeamMutation.isPending;
  const styles = editTeamScreenStyles(colors);
  const config = useAppStore((state) => state.config);
  const ageGroupSelection = useRef<BottomSheetModal>(null);

  const isAddMode = route.params?.isAddMode || false;
  const clubName = route.params?.clubName;
  const isLoading = isAddMode ? isCreateTeamLoading : isUpdateTeamLoading;

  const [team, setTeam] = useState<Team | null>(null);
  const [teamName, setTeamName] = useState('');
  const [teamShortName, setTeamShortName] = useState('');
  const [teamImage, setTeamImage] = useState('');
  const [teamAgeGroup, setTeamAgeGroup] = useState('');
  const [originalName, setOriginalName] = useState('');
  const [originalShortName, setOriginalShortName] = useState('');
  const [originalImage, setOriginalImage] = useState('');
  const [originalAgeGroup, setOriginalAgeGroup] = useState('');
  const [localLoading, setLocalLoading] = useState(false);

  const validateImageUrl = (url: string): boolean => {
    return !!url && typeof url === 'string' && url.startsWith('https');
  };

  const validateTeamName = (value: string): boolean => {
    return value.trim().length >= 3;
  };

  const validateTeamShortName = (value: string): boolean => {
    return value.trim().length >= 2;
  };

  const validateTeamAgeGroup = (value: string): boolean => {
    return value.trim().length > 0;
  };

  const isNameValid = useMemo(() => validateTeamName(teamName), [teamName]);
  const isShortNameValid = useMemo(
    () => validateTeamShortName(teamShortName),
    [teamShortName]
  );
  const isImageValid = useMemo(() => validateImageUrl(teamImage), [teamImage]);
  const isAgeGroupValid = useMemo(
    () => validateTeamAgeGroup(teamAgeGroup),
    [teamAgeGroup]
  );

  const isFormValid = useMemo(() => {
    return isNameValid && isShortNameValid && isImageValid && isAgeGroupValid;
  }, [isNameValid, isShortNameValid, isImageValid, isAgeGroupValid]);

  const hasDataChanged = useMemo(() => {
    if (isAddMode) return true;
    return (
      teamName !== originalName ||
      teamShortName !== originalShortName ||
      teamImage !== originalImage ||
      teamAgeGroup !== originalAgeGroup
    );
  }, [
    isAddMode,
    teamName,
    originalName,
    teamShortName,
    originalShortName,
    teamImage,
    originalImage,
    teamAgeGroup,
    originalAgeGroup,
  ]);

  useEffect(() => {
    if (isAddMode) {
      setTeam(null);
      setTeamName('');
      setTeamShortName('');
      setTeamImage('');
      setTeamAgeGroup('');
      setOriginalName('');
      setOriginalShortName('');
      setOriginalImage('');
      setOriginalAgeGroup('');

      navigation.setOptions({
        title: i18next.t('ADMIN.EDIT_TEAM.ADD_TEAM'),
      });
    } else if (route.params?.teamDetails) {
      const teamDetails = route.params.teamDetails;

      const foundTeam: Team = {
        id: route.params.teamId || 0,
        name: teamDetails.name,
        shortName: teamDetails.shortName,
        image: teamDetails.image,
        ageGroup: teamDetails.ageGroup,
      };

      setTeam(foundTeam);
      setTeamName(foundTeam.name || '');
      setOriginalName(foundTeam.name || '');

      setTeamShortName(foundTeam.shortName || '');
      setOriginalShortName(foundTeam.shortName || '');

      const isValidImageUrl = validateImageUrl(foundTeam.image);
      setTeamImage(foundTeam.image || '');
      setOriginalImage(isValidImageUrl ? foundTeam.image : '');

      setTeamAgeGroup(foundTeam.ageGroup || '');
      setOriginalAgeGroup(foundTeam.ageGroup || '');

      navigation.setOptions({
        title: i18next.t('ADMIN.EDIT_TEAM.EDIT_TEAM'),
      });
    } else if (adminForTeams && route.params?.teamId) {
      let foundTeam: any = null;

      try {
        const adminTeamsData = adminForTeams as any;
        if (
          adminTeamsData &&
          typeof adminTeamsData === 'object' &&
          adminTeamsData.data &&
          typeof adminTeamsData.data === 'object'
        ) {
          Object.keys(adminTeamsData.data).forEach((clubKey) => {
            const clubTeams = adminTeamsData.data[clubKey];
            if (Array.isArray(clubTeams)) {
              const team = clubTeams.find(
                (t: any) => t.id === route.params.teamId
              );
              if (team) {
                foundTeam = team;
              }
            }
          });
        }
      } catch (error) {
        console.error('Error processing team data:', error);
      }

      if (foundTeam) {
        setTeam(foundTeam);
        setTeamName(foundTeam.name || '');
        setOriginalName(foundTeam.name || '');

        setTeamShortName(foundTeam.shortName || '');
        setOriginalShortName(foundTeam.shortName || '');

        const isValidImageUrl = validateImageUrl(foundTeam.image);
        setTeamImage(foundTeam.image || '');
        setOriginalImage(isValidImageUrl ? foundTeam.image : '');

        setTeamAgeGroup(foundTeam.ageGroup || '');
        setOriginalAgeGroup(foundTeam.ageGroup || '');

        navigation.setOptions({
          title: i18next.t('ADMIN.EDIT_TEAM.EDIT_TEAM'),
        });
      }
    }
  }, [
    adminForTeams,
    route.params?.teamId,
    route.params?.teamDetails,
    isAddMode,
    navigation,
  ]);

  const handleImageUploaded = (imageUrl: string) => {
    setTeamImage(imageUrl);
  };

  const handleSaveChanges = () => {
    if (!isFormValid) {
      return;
    }

    setLocalLoading(true);

    if (isAddMode) {
      if (!clubName) {
        setLocalLoading(false);
        return;
      }

      const newTeamData = {
        name: teamName,
        shortName: teamShortName,
        image: teamImage,
        clubName: clubName,
        ageGroup: teamAgeGroup,
      };

      createTeamMutation.mutate(
        {
          payload: newTeamData,
        },
        {
          onSuccess: () => {
            refetchRetrieveAdminForTeams()
              .then(() => navigation.goBack())
              .catch(() => navigation.goBack())
              .finally(() => {
                setLocalLoading(false);
              });
          },
          onError: () => {
            setLocalLoading(false);
          },
        }
      );
    } else {
      if (!hasDataChanged) {
        setLocalLoading(false);
        return;
      }

      if (!team?.id) {
        setLocalLoading(false);
        return;
      }

      const updatedTeamData = {
        id: team.id,
        name: teamName,
        image: teamImage,
        shortName: teamShortName,
        ageGroup: teamAgeGroup,
      };

      setTeam({
        ...team,
        name: teamName,
        shortName: teamShortName,
        image: teamImage,
        ageGroup: teamAgeGroup,
      });

      updateTeamMutation.mutate(
        {
          payload: updatedTeamData,
        },
        {
          onSuccess: () => {
            refetchRetrieveAdminForTeams()
              .then(() => {
                // Navigate back to TeamScreen with the team details
                navigation.navigate('TeamScreen', {
                  teamId: team.id,
                  clubId: route.params?.clubId,
                  clubName: route.params?.clubName,
                });
              })
              .catch(() => navigation.goBack())
              .finally(() => {
                setLocalLoading(false);
              });
          },
          onError: () => {
            setLocalLoading(false);
          },
        }
      );
    }
  };

  return (
    <ViewContainer>
      <Header
        title={
          isAddMode
            ? i18next.t('ADMIN.EDIT_TEAM.ADD_TEAM')
            : i18next.t('ADMIN.EDIT_TEAM.EDIT_TEAM')
        }
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.logoContainer}>
            <ImageUploader
              initialImage={teamImage}
              onImageUploaded={handleImageUploaded}
              isEditAllowed={true}
            />
            {!isImageValid && (
              <Text style={[styles.helperText, { color: colors.red }]}>
                {i18next.t('ADMIN.EDIT_TEAM.IMAGE_REQUIRED')}
              </Text>
            )}
          </View>

          <Input
            label={i18next.t('ADMIN.EDIT_TEAM.TEAM_NAME')}
            value={teamName}
            onChangeText={setTeamName}
            placeholder={i18next.t('ADMIN.EDIT_TEAM.ENTER_TEAM_NAME')}
          />

          <Input
            label={i18next.t('ADMIN.EDIT_TEAM.TEAM_SHORT_NAME')}
            value={teamShortName}
            onChangeText={setTeamShortName}
            placeholder={i18next.t('ADMIN.EDIT_TEAM.ENTER_TEAM_SHORT_NAME')}
          />

          {config?.availableAgeGroups ? (
            <Select
              ref={ageGroupSelection}
              selectLabel={i18next.t('ADMIN.EDIT_TEAM.TEAM_AGE_GROUP')}
              list={config.availableAgeGroups.map((ageGroup: string) => ({
                id: ageGroup,
                name: ageGroup,
                number: ageGroup.substring(0, 2),
              }))}
              defaultValue={teamAgeGroup}
              onOpen={() => {
                ageGroupSelection.current?.present();
              }}
              onItemSelect={(selectedItem) => {
                ageGroupSelection.current?.dismiss();
                setTeamAgeGroup(selectedItem.name);
              }}
            />
          ) : (
            <Input
              label={i18next.t('ADMIN.EDIT_TEAM.TEAM_AGE_GROUP')}
              value={teamAgeGroup}
              onChangeText={setTeamAgeGroup}
              placeholder={i18next.t('ADMIN.EDIT_TEAM.ENTER_TEAM_AGE_GROUP')}
            />
          )}

          <Button
            title={
              isAddMode
                ? i18next.t('ADMIN.EDIT_TEAM.CREATE_TEAM')
                : i18next.t('ADMIN.EDIT_TEAM.SAVE_CHANGES')
            }
            onPress={handleSaveChanges}
            loading={isLoading || localLoading}
            disabled={
              !isFormValid ||
              (!isAddMode && !hasDataChanged) ||
              isLoading ||
              localLoading
            }
            containerStyle={styles.saveButton}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </ViewContainer>
  );
};

export default EditTeamScreen;
