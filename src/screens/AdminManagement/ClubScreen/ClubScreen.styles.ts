import { StyleSheet } from 'react-native';
import { ColorStyles } from '@scorescast/design-system';
import { SpacingStyles } from '@scorescast/design-system/theme/spacing';

export const clubScreenStyles = (
  colors: typeof ColorStyles,
  spacings?: SpacingStyles
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    centerContent: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorText: {
      fontSize: 18,
      textAlign: 'center',
    },
    clubContainer: {
      padding: spacings?.large || 16,
      marginTop: spacings?.xxlarge || 20,
    },
    addTeamButton: {
      marginTop: 8,
      marginBottom: 16,
      width: '50%',
      alignSelf: 'flex-start',
      marginLeft: 16,
    },
    teamsContainer: {
      flex: 1,
      width: '90%',
      alignSelf: 'center',
      marginTop: spacings?.xxlarge || 20,
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 16,
    },
    noTeamsText: {
      fontSize: 16,
      textAlign: 'center',
      marginTop: 20,
    },
    deleteButton: {
      marginTop: 16,
      marginBottom: 24,
      width: '90%',
      alignSelf: 'center',
      backgroundColor: colors.neutral700,
      borderRadius: 0,
    },
    deleteButtonText: {
      color: colors.red,
      fontSize: 16,
    },
    confirmContainer: {
      paddingHorizontal: spacings?.large || 16,
      paddingTop: spacings?.xxlarge || 20,
      paddingBottom: spacings?.xxxxlarge || 32,
    },
    confirmTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      textAlign: 'center',
      color: colors.inverse900,
    },
    confirmSubtitle: {
      fontSize: 14,
      color: colors.inverse900,
      paddingVertical: spacings?.xxxlarge || 24,
      textAlign: 'center',
    },
    confirmButtonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
    },
    cancelButton: {
      flex: 1,
      marginRight: spacings?.medium || 12,
      backgroundColor: 'transparent',
    },
    confirmDeleteButton: {
      flex: 1,
      marginLeft: spacings?.medium || 12,
      backgroundColor: 'transparent',
    },
    confirmDeleteButtonText: {
      color: colors.red,
    },
  });
