import React, { useEffect, useState, useRef } from 'react';
import { Text, View } from 'react-native';
import {
  useTheme,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>lay<PERSON><PERSON>,
  ViewContainer,
  IconInfoList,
  BottomSheet,
} from '@scorescast/design-system';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { i18next } from '@scorescast/translations';
import {
  useNavigation,
  useRoute,
  RouteProp,
  NavigationProp,
} from '@react-navigation/native';
import { useUser, useUserRoles, useClub } from '@scorescast/http-clients';
import { clubScreenStyles } from './ClubScreen.styles';

interface Team {
  id: number;
  name: string;
  image?: string;
  teamUniqueId?: string;
  clubName?: string;
}

interface Club {
  id: number;
  name: string;
  crest: string;
  website?: string;
}

type ClubScreenParams = {
  clubId: number;
  clubName?: string;
};

type RootStackParamList = {
  ClubScreen: ClubScreenParams;
  EditClubScreen: { clubId?: number; isAddMode?: boolean };
  EditTeamScreen: {
    teamId?: number;
    isAddMode?: boolean;
    clubId?: number;
    clubName?: string;
  };
  TeamScreen: {
    teamId: number;
    teamName: string;
    clubId?: number;
    clubName?: string;
  };
};

type ClubScreenRouteProp = RouteProp<
  { ClubScreen: ClubScreenParams },
  'ClubScreen'
>;

type ClubScreenNavigationProp = NavigationProp<RootStackParamList>;

const ClubScreen: React.FC = () => {
  const { colors, spacings } = useTheme();
  const navigation = useNavigation<ClubScreenNavigationProp>();
  const route = useRoute<ClubScreenRouteProp>();
  const {
    adminForClubs,
    isRetrieveAdminForClubsLoading,
    adminForTeams,
    isRetrieveAdminForTeamsLoading,
    retrieveAdminForClubs,
  } = useUser();
  const {
    user,
    isSuperAdmin,
    isClubAdmin,
    isTeamAdmin,
    canEditClubs: canEditClub,
  } = useUserRoles();
  const { deleteClub, isDeleteClubLoading } = useClub();
  const styles = clubScreenStyles(colors, spacings);

  const [club, setClub] = useState<Club | null>(null);
  const [teams, setTeams] = useState<Team[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const deleteConfirmationRef = useRef<BottomSheetModal>(null);

  useEffect(() => {
    const clubId = route.params.clubId;
    const clubName = route.params.clubName;
    let foundClub: Club | null = null;

    // For SUPER_ADMIN and CLUB_ADMIN, use adminForClubs data
    if ((isSuperAdmin || isClubAdmin) && adminForClubs) {
      let clubsData: any[] = [];
      const adminClubs = adminForClubs as any;

      if (Array.isArray(adminClubs)) {
        clubsData = adminClubs;
      } else if (
        adminClubs &&
        typeof adminClubs === 'object' &&
        adminClubs.data
      ) {
        clubsData = Array.isArray(adminClubs.data) ? adminClubs.data : [];
      }

      foundClub = clubsData.find((club: any) => club.id === clubId);
    }

    // For TEAM_ADMIN, use clubName to find club info from adminForTeams data
    if (isTeamAdmin && adminForTeams && clubName && !foundClub) {
      try {
        const adminTeamsData = adminForTeams as any;
        if (
          adminTeamsData &&
          typeof adminTeamsData === 'object' &&
          adminTeamsData.data &&
          typeof adminTeamsData.data === 'object' &&
          adminTeamsData.data[clubName]
        ) {
          const teams = adminTeamsData.data[clubName];
          if (Array.isArray(teams) && teams.length > 0) {
            const firstTeam = teams[0];
            foundClub = {
              id: clubId,
              name: clubName,
              crest: firstTeam.clubCrest || '',
              website: firstTeam.clubWebsite || '',
            };
          }
        }
      } catch (error) {
        console.error('Error processing adminForTeams data for club:', error);
      }
    }

    if (foundClub) {
      setClub(foundClub);
    }

    setIsLoading(false);
  }, [
    adminForClubs,
    adminForTeams,
    route.params.clubId,
    route.params.clubName,
    isSuperAdmin,
    isClubAdmin,
    isTeamAdmin,
  ]);

  useEffect(() => {
    if (adminForTeams && club) {
      let teamsData: Team[] = [];
      const adminTeamsData = adminForTeams as any;

      try {
        if (
          adminTeamsData &&
          typeof adminTeamsData === 'object' &&
          adminTeamsData.data &&
          typeof adminTeamsData.data === 'object'
        ) {
          const clubName = club.name;

          if (
            adminTeamsData.data[clubName] &&
            Array.isArray(adminTeamsData.data[clubName])
          ) {
            teamsData = adminTeamsData.data[clubName];
          }
        }
      } catch (error) {
        console.error('Error processing adminForTeams data:', error);
      }

      setTeams(teamsData);
    } else {
      setTeams([]);
    }
  }, [adminForTeams, club]);

  const editClub = () => {
    if (club) {
      navigation.navigate('EditClubScreen', { clubId: club.id });
    }
  };

  const handleAddTeam = () => {
    if (club) {
      navigation.navigate('EditTeamScreen', {
        isAddMode: true,
        clubId: club.id,
        clubName: club.name,
      });
    }
  };

  const handleDeleteClub = () => {
    deleteConfirmationRef.current?.present();
  };

  const confirmDeleteClub = () => {
    if (club) {
      deleteConfirmationRef.current?.dismiss();
      deleteClub(club.id, {
        onSuccess: () => {
          retrieveAdminForClubs();
          navigation.goBack();
        },
      });
    }
  };

  const cancelDeleteClub = () => {
    deleteConfirmationRef.current?.dismiss();
  };

  if (
    isRetrieveAdminForClubsLoading ||
    isRetrieveAdminForTeamsLoading ||
    isLoading
  ) {
    return <Loader />;
  }

  const isValidCrestUrl =
    club &&
    club.crest &&
    typeof club.crest === 'string' &&
    club.crest.startsWith('https');
  return (
    <ViewContainer>
      <Header
        title={i18next.t('ADMIN.VIEW_CLUB.CLUB_DETAILS')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      <View style={styles.clubContainer}>
        <DisplayCard
          image={isValidCrestUrl ? club?.crest : null}
          title={club?.name || ''}
          website={club?.website || ''}
          onEditPress={canEditClub ? editClub : undefined}
          onPress={() => {}}
        />
      </View>

      {canEditClub && (
        <Button
          title={i18next.t('ADMIN.VIEW_CLUB.ADD_TEAM')}
          onPress={handleAddTeam}
          secondary
          containerStyle={styles.addTeamButton}
        />
      )}

      <View style={styles.teamsContainer}>
        <Text style={[styles.sectionTitle, { color: colors.inverse700 }]}>
          {i18next.t('ADMIN.VIEW_CLUB.TEAMS')}
        </Text>

        {teams.length === 0 ? (
          <Text style={[styles.noTeamsText, { color: colors.inverse400 }]}>
            {i18next.t('ADMIN.VIEW_CLUB.NO_TEAMS')}
          </Text>
        ) : (
          <IconInfoList
            items={teams.map((team, index) => ({
              id: `team-${team.id}-${index}`,
              image: team.image,
              title: team.name || 'Team Name',
              onPress: () => {
                navigation.navigate('TeamScreen', {
                  teamId: team.id,
                  teamName: team.name,
                  clubId: club?.id,
                  clubName: club?.name,
                });
              },
            }))}
          />
        )}
      </View>

      {(isClubAdmin || isSuperAdmin) && (
        <Button
          title={i18next.t('ADMIN.VIEW_CLUB.DELETE_CLUB')}
          onPress={handleDeleteClub}
          containerStyle={styles.deleteButton}
          containerTitleStyle={styles.deleteButtonText}
          loading={isDeleteClubLoading}
        />
      )}

      <BottomSheet ref={deleteConfirmationRef}>
        <View style={styles.confirmContainer}>
          <Text style={styles.confirmTitle}>
            {i18next.t('ADMIN.VIEW_CLUB.DELETE_CONFIRM_TITLE')}
          </Text>
          <Text style={styles.confirmSubtitle}>
            {i18next.t('ADMIN.VIEW_CLUB.DELETE_CONFIRM_MESSAGE')}
          </Text>
          <View style={styles.confirmButtonContainer}>
            <Button
              title={i18next.t('ADMIN.VIEW_CLUB.CANCEL')}
              onPress={cancelDeleteClub}
              containerStyle={styles.cancelButton}
            />
            <Button
              title={i18next.t('ADMIN.VIEW_CLUB.CONFIRM_DELETE')}
              onPress={confirmDeleteClub}
              containerStyle={styles.confirmDeleteButton}
              containerTitleStyle={styles.confirmDeleteButtonText}
            />
          </View>
        </View>
      </BottomSheet>
    </ViewContainer>
  );
};

export default ClubScreen;
