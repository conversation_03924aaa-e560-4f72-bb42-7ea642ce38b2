import React, { useState, useEffect, useRef } from 'react';
import { View, Share, Text } from 'react-native';
import {
  useTheme,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>lay<PERSON>ard,
  ViewContainer,
  BottomSheet,
} from '@scorescast/design-system';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { CustomIconInfoList } from './CustomIconInfoList';
import { i18next } from '@scorescast/translations';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { AdminNavigationProp } from '../../../navigator/AdminNavigator';
import { useUser, useTeam } from '@scorescast/http-clients';
import { teamScreenStyles } from './TeamScreen.styles';

interface TeamDetails {
  id: number;
  name: string;
  image?: string;
  website?: string;
  teamUniqueId?: string;
  clubName?: string;
  shortName?: string;
  ageGroup?: string;
}

type TeamScreenParams = {
  teamId: number;
  teamName: string;
  clubId?: number;
  clubName?: string;
};

type RootStackParamList = {
  TeamScreen: TeamScreenParams;
  EditTeamScreen: {
    teamId: number;
    isAddMode?: boolean;
    clubId?: number;
    clubName?: string;
    teamDetails?: {
      name: string;
      shortName: string;
      image: string;
      ageGroup: string;
    };
  };
  ClubScreen: {
    clubId: number;
  };
  TeamAgentsScreen: {
    teamId: number;
  };
  TeamAdminsScreen: {
    teamId: number;
  };
  PlayersScreen: {
    teamId: number;
    teamName?: string;
  };
  TeamMatchesScreen: undefined;
  TeamFollowersScreen: undefined;
};

type TeamScreenRouteProp = RouteProp<
  { TeamScreen: TeamScreenParams },
  'TeamScreen'
>;

const TeamScreen: React.FC = () => {
  const { colors, spacings } = useTheme();
  const navigation = useNavigation<AdminNavigationProp>();
  const route = useRoute<TeamScreenRouteProp>();
  const { adminForTeams, isRetrieveAdminForTeamsLoading } = useUser();
  const { deleteTeam } = useTeam();
  const styles = teamScreenStyles(colors, spacings);

  const [team, setTeam] = useState<TeamDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const deleteConfirmationRef = useRef<BottomSheetModal>(null);

  useEffect(() => {
    if (adminForTeams && route.params.teamId) {
      const { teamId } = route.params;
      let foundTeam: TeamDetails | null = null;

      try {
        const adminTeamsData = adminForTeams as any;
        if (
          adminTeamsData &&
          typeof adminTeamsData === 'object' &&
          adminTeamsData.data &&
          typeof adminTeamsData.data === 'object'
        ) {
          Object.keys(adminTeamsData.data).forEach((clubKey) => {
            const clubTeams = adminTeamsData.data[clubKey];
            if (Array.isArray(clubTeams)) {
              const team = clubTeams.find((t: any) => t.id === teamId);
              if (team) {
                foundTeam = team;
              }
            }
          });
        }
      } catch (error) {
        console.error('Error processing team data:', error);
      }

      if (foundTeam) {
        setTeam(foundTeam);
      }

      setIsLoading(false);
    }
  }, [adminForTeams, route.params]);

  const handleEditTeam = () => {
    if (team) {
      navigation.navigate('EditTeamScreen', {
        teamId: team.id,
        isAddMode: false,
        clubId: route.params.clubId,
        clubName: route.params.clubName,
        teamDetails: {
          name: team.name,
          shortName: team.shortName || '',
          image: team.image || '',
          ageGroup: team.ageGroup || '',
        },
      });
    }
  };

  const handleShareTeam = async () => {
    if (team) {
      try {
        await Share.share({
          message: i18next.t('FOLLOWING.SHARE_TEAM.SMS', {
            teamName: team.name,
            link: `https://scorescast.app/team/${team.teamUniqueId || team.id}`,
          }),
        });
      } catch (error) {
        console.error('Error sharing team:', error);
      }
    }
  };

  const handleDeleteTeam = () => {
    if (team) {
      deleteConfirmationRef.current?.present();
    }
  };

  const confirmDeleteTeam = () => {
    if (team) {
      deleteConfirmationRef.current?.dismiss();
      setIsDeleting(true);
      deleteTeam(team.id, {
        onSuccess: () => {
          setIsDeleting(false);
          if (route.params.clubId) {
            navigation.navigate('ClubScreen', { clubId: route.params.clubId });
          } else {
            navigation.goBack();
          }
        },
      });
    }
  };

  const cancelDeleteTeam = () => {
    deleteConfirmationRef.current?.dismiss();
  };

  if (isRetrieveAdminForTeamsLoading || isLoading || isDeleting) {
    return <Loader />;
  }

  let teamImage: string | null = null;
  if (
    team &&
    team.image &&
    typeof team.image === 'string' &&
    team.image.startsWith('https')
  ) {
    teamImage = team.image;
  }

  return (
    <ViewContainer>
      <Header
        title={i18next.t('ADMIN.VIEW_TEAM.TEAM_DETAILS')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      <View style={styles.teamContainer}>
        <DisplayCard
          image={teamImage}
          title={team?.name || ''}
          website={team?.website || ''}
          onEditPress={handleEditTeam}
          onPress={() => {}}
        />
      </View>

      <Button
        title={i18next.t('FOLLOWING.SHARE_TEAM.SHARE')}
        onPress={handleShareTeam}
        secondary
        containerStyle={styles.shareButton}
      />

      <View style={styles.menuContainer}>
        <CustomIconInfoList
          items={[
            {
              id: 'team-agents',
              title: i18next.t('TEAM.TEAM_AGENTS'),
              noIcon: true,
              itemStyle: { backgroundColor: colors.inverse900 },
              textStyle: { color: colors.inverse100 },
              onPress: () => {
                navigation.navigate('TeamAgentsScreen', {
                  teamId: route.params.teamId,
                });
              },
            },
            {
              id: 'team-admins',
              title: i18next.t('TEAM.TEAM_ADMINS'),
              noIcon: true,
              itemStyle: { backgroundColor: colors.inverse900 },
              textStyle: { color: colors.inverse100 },
              onPress: () => {
                navigation.navigate('TeamAdminsScreen', {
                  teamId: route.params.teamId,
                });
              },
            },
            {
              id: 'players',
              title: i18next.t('TEAM.PLAYERS'),
              noIcon: true,
              itemStyle: { backgroundColor: colors.inverse900 },
              textStyle: { color: colors.inverse100 },
              onPress: () => {
                navigation.navigate('PlayersScreen', {
                  teamId: route.params.teamId,
                  teamName: team?.name,
                });
              },
            },
            {
              id: 'matches',
              title: i18next.t('TEAM.MATCHES'),
              noIcon: true,
              itemStyle: { backgroundColor: colors.inverse900 },
              textStyle: { color: colors.inverse100 },
              onPress: () => {
                navigation.navigate('TeamMatchesScreen', {
                  teamId: route.params.teamId,
                  teamUniqueId: team?.teamUniqueId,
                  teamName: team?.name,
                });
              },
            },
            {
              id: 'followers',
              title: i18next.t('TEAM.FOLLOWERS'),
              noIcon: true,
              itemStyle: { backgroundColor: colors.inverse900 },
              textStyle: { color: colors.inverse100 },
              onPress: () => {
                navigation.navigate('TeamFollowersScreen', {
                  teamId: route.params.teamId,
                  teamName: team?.name,
                });
              },
            },
          ]}
        />
      </View>

      <Button
        title={i18next.t('TEAM.DELETE_TEAM')}
        onPress={handleDeleteTeam}
        containerStyle={styles.deleteButton}
        containerTitleStyle={styles.deleteButtonText}
        isLoading={isDeleting}
        disabled={isDeleting}
      />

      <BottomSheet ref={deleteConfirmationRef}>
        <View style={styles.confirmContainer}>
          <Text style={styles.confirmTitle}>
            {i18next.t('TEAM.DELETE_TEAM_CONFIRM_TITLE')}
          </Text>
          <Text style={styles.confirmSubtitle}>
            {i18next.t('TEAM.DELETE_TEAM_CONFIRM_MESSAGE', {
              teamName: team?.name || '',
            })}
          </Text>
          <View style={styles.confirmButtonContainer}>
            <Button
              title={i18next.t('TEAM.CANCEL')}
              onPress={cancelDeleteTeam}
              containerStyle={styles.cancelButton}
            />
            <Button
              title={i18next.t('TEAM.CONFIRM_DELETE')}
              onPress={confirmDeleteTeam}
              containerStyle={styles.confirmDeleteButton}
              containerTitleStyle={styles.confirmDeleteButtonText}
            />
          </View>
        </View>
      </BottomSheet>
    </ViewContainer>
  );
};

export default TeamScreen;
