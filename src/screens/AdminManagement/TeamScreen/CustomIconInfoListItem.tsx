import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { Icon, useTheme } from '@scorescast/design-system';
import { IconName } from '@scorescast/design-system/components/Icon/Icon.types';
import { iconInfoListItemStyles } from '@scorescast/design-system/components/IconInfoListItem/IconInfoListItem.styles';
import { Separator } from '@scorescast/design-system/components/Separator/Separator';

interface CustomIconInfoListItemProps {
  image?: string;
  title: string;
  noIcon: boolean;
  withSeparator?: boolean;
  itemStyle: StyleProp<ViewStyle>;
  textStyle?: { color: string };
  onPress?: () => void;
}

export const CustomIconInfoListItem: React.FC<CustomIconInfoListItemProps> = ({
  image,
  title,
  noIcon,
  itemStyle,
  textStyle,
  withSeparator = false,
  onPress,
}) => {
  const { colors, spacings, typography } = useTheme();
  const styles = iconInfoListItemStyles(colors, spacings, typography);

  const isValidImage =
    image && typeof image === 'string' && image.startsWith('https');

  return (
    <View>
      <View style={[styles.itemContainer, itemStyle]}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={onPress}
          style={styles.contentContainer}
        >
          <View style={styles.row}>
            {!noIcon && (
              <>
                {isValidImage ? (
                  <Image source={{ uri: image }} style={styles.image} />
                ) : (
                  <View style={styles.imagePlaceholder}>
                    <Icon name={IconName.FRAME} isPng width={24} height={24} />
                  </View>
                )}
              </>
            )}
            <Text numberOfLines={1} style={[styles.title, textStyle]}>
              {title}
            </Text>
          </View>
        </TouchableOpacity>
        <Icon
          name={IconName.ARROW_RIGHT_LIGHT}
          isPng
          width={18}
          height={18}
          styles={{ tintColor: colors.inverse100 }}
        />
      </View>
      {withSeparator && <Separator containerStyle={styles.separator} />}
    </View>
  );
};
