import React from 'react';
import { View, StyleProp, ViewStyle, ScrollView } from 'react-native';
import { useTheme } from '@scorescast/design-system';
import { iconInfoListStyles } from '@scorescast/design-system/components/IconInfoList/IconInfoList.styles';
import { CustomIconInfoListItem } from './CustomIconInfoListItem';

interface CustomIconInfoListItemData {
  id: number | string;
  image?: string;
  title: string;
  noIcon: boolean;
  itemStyle: StyleProp<ViewStyle>;
  textStyle?: { color: string };
  onPress?: () => void;
}

interface CustomIconInfoListProps {
  items: CustomIconInfoListItemData[];
  containerStyle?: StyleProp<ViewStyle>;
  scrollable?: boolean;
}

export const CustomIconInfoList: React.FC<CustomIconInfoListProps> = ({
  items,
  containerStyle,
  scrollable = true,
}) => {
  const { colors, spacings } = useTheme();
  const styles = iconInfoListStyles(colors, spacings);

  const content = (
    <View style={[styles.container, containerStyle]}>
      {items.map((item) => (
        <CustomIconInfoListItem
          key={item.id.toString()}
          image={item.image}
          title={item.title}
          noIcon={item.noIcon}
          itemStyle={item.itemStyle}
          textStyle={item.textStyle}
          onPress={item.onPress}
        />
      ))}
    </View>
  );

  if (scrollable) {
    return (
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
      >
        {content}
      </ScrollView>
    );
  }

  return content;
};
