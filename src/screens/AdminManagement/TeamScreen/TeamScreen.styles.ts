import { StyleSheet } from 'react-native';

export const teamScreenStyles = (colors: any, spacings?: any) =>
  StyleSheet.create({
    menuItemText: {
      color: colors.inverse100,
    },
    container: {
      flex: 1,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    title: {
      fontSize: 22,
      fontWeight: 'bold',
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      marginBottom: 24,
    },
    infoCard: {
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderRadius: 8,
      padding: 16,
      marginTop: 16,
    },
    infoText: {
      fontSize: 14,
      lineHeight: 20,
    },
    centerContent: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    loadingText: {
      fontSize: 16,
      textAlign: 'center',
    },
    noTeamsText: {
      fontSize: 18,
      textAlign: 'center',
      marginBottom: 20,
    },
    contactSupportText: {
      fontSize: 14,
      textAlign: 'center',
      marginTop: 10,
    },
    addTeamButton: {
      marginTop: 20,
      width: '80%',
      alignSelf: 'center',
    },
    teamsContainer: {
      flex: 1,
      width: '100%',
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 16,
    },
    teamsList: {
      paddingBottom: 20,
    },
    teamCard: {
      flexDirection: 'row',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderRadius: 8,
      padding: 12,
      marginBottom: 12,
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    teamContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    teamLogo: {
      width: 50,
      height: 50,
      borderRadius: 25,
      marginRight: 12,
    },
    placeholderLogo: {
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
    },
    teamInfo: {
      flex: 1,
    },
    teamName: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    teamWebsite: {
      fontSize: 12,
      marginTop: 4,
    },
    editButton: {
      padding: 8,
    },
    editButtonText: {
      fontSize: 16,
    },
    teamContainer: {
      padding: spacings?.large || 16,
      marginTop: spacings?.xxlarge || 20,
    },
    shareButton: {
      marginTop: 8,
      marginBottom: 16,
      width: '50%',
      alignSelf: 'center',
    },
    menuContainer: {
      flex: 1,
      width: '90%',
      alignSelf: 'center',
      marginTop: spacings?.medium || 12,
    },
    deleteButton: {
      marginTop: 16,
      marginBottom: 24,
      width: '90%',
      alignSelf: 'center',
      backgroundColor: colors.neutral700,
      borderRadius: 0,
    },
    deleteButtonText: {
      color: colors.red,
      fontSize: 16,
    },
    confirmContainer: {
      paddingHorizontal: spacings.large,
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.xxxxlarge,
    },
    confirmTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      textAlign: 'center',
      color: colors.inverse900,
    },
    confirmSubtitle: {
      fontSize: 14,
      color: colors.inverse900,
      paddingVertical: spacings.xxxlarge,
      textAlign: 'center',
    },
    confirmButtonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
    },
    cancelButton: {
      flex: 1,
      marginRight: spacings.medium,
      backgroundColor: 'transparent',

    },
    confirmDeleteButton: {
      flex: 1,
      marginLeft: spacings.medium,
      backgroundColor: 'transparent',
    },
    confirmDeleteButtonText: {
      color: colors.red,
    },
  });
