import { StyleSheet } from 'react-native';

export const playersScreenStyles = (colors: any, spacings?: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    headerContainer: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
      gap: spacings.large,
    },
    controlsPlayer: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: spacings.xxxxxlarge,
      paddingHorizontal: 16,
    },
    playersCount: {
      flex: 1,
      color: colors.inverse850,
      opacity: 0.6,
      fontSize: 14,
      fontWeight: '600',
    },
    addPlayerButton: {
      flex: 1,
      borderRadius: 5,
      height: 40,
    },
    addPlayerButtonText: {
      fontSize: 14,
      fontWeight: '600',
    },
    teamMembers: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
      gap: spacings.xlarge,
      marginTop: spacings.xxxlarge,
      paddingHorizontal: 16,
    },
    playerRow: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      fontSize: 16,
      fontWeight: '600',
    },
    playerRowCircle: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: colors.inverse900,
      justifyContent: 'center',
      alignItems: 'center',
    },
    playerRowCircleText: {
      color: colors.inverse100,
      fontSize: 14,
      fontWeight: '700',
    },
    playerRowNameText: {
      color: colors.inverse900,
      fontSize: 14,
      fontWeight: '700',
      marginLeft: spacings.large,
    },
    playerRowControls: {
      marginLeft: 'auto',
    },
  });
