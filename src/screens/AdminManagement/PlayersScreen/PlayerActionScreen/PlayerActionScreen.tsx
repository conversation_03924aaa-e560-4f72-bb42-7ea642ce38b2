import React, { useState, useMemo, useRef } from 'react';
import { Text, View, TextInput } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { AdminNavigationProp } from '../../../../navigator/AdminNavigator';
import {
  <PERSON><PERSON>,
  ViewContainer,
  useTheme,
  Button,
  BottomSheet,
} from '@scorescast/design-system';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { i18next } from '@scorescast/translations';
import { playerActionScreenStyles } from './PlayerActionScreen.styles';
import { useToasterStore } from '@scorescast/http-clients/src/stores/useToasterStore';
import { ToasterType } from '@scorescast/formatters-constants';
import {
  UseTeamPlayers,
  useTeam,
} from '@scorescast/http-clients/src/hooks/useTeam';

export enum PlayerActions {
  ADD = 'ADD',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
}

type PlayerActionScreenParams = {
  action: PlayerActions;
  player?: {
    id?: number;
    jerseyName: string;
    number: string;
  };
  players?: any[];
  teamId: number;
};

type RootStackParamList = {
  PlayerActionScreen: PlayerActionScreenParams;
};

type PlayerActionScreenRouteProp = RouteProp<
  RootStackParamList,
  'PlayerActionScreen'
>;

const PlayerActionScreen: React.FC = () => {
  const { colors, spacings } = useTheme();
  const styles = playerActionScreenStyles(colors, spacings);
  const navigation = useNavigation<AdminNavigationProp>();
  const route = useRoute<PlayerActionScreenRouteProp>();
  const { addToaster } = useToasterStore();
  const deleteConfirmationRef = useRef<BottomSheetModal>(null);
  const { addPlayerToTeam, deletePlayerFromTeam } = useTeam();

  const teamId = route.params.teamId;

  const { refetchTeamPlayers } = UseTeamPlayers(teamId);

  const action = route.params?.action || PlayerActions.ADD;
  const player = route.params?.player;
  const players = route.params?.players || [];
  const [localPlayer, setLocalPlayer] = useState(
    player || { jerseyName: '', number: '' }
  );

  const isLocalPlayerBasicValid = useMemo(
    () =>
      Object.values(localPlayer).every(
        (prop) => typeof prop === 'string' && prop.trim() !== ''
      ),
    [localPlayer]
  );

  const handleAddPlayer = () => {
    const numberExists = players
      ? players.filter((p) => p.number === localPlayer.number)
      : [];

    if (numberExists.length) {
      addToaster({
        type: ToasterType.ERROR,
        title: i18next.t(
          'ADMIN.PLAYER_ACTION.VALIDATION.EXISTING_NUMBER_TITLE'
        ),
        description: i18next.t(
          'ADMIN.PLAYER_ACTION.VALIDATION.EXISTING_NUMBER_DESCRIPTION',
          {
            jerseyNumber: numberExists[0].number,
            player: numberExists[0].jerseyName,
          }
        ),
      });
      return;
    }

    const numericTeamId = Number(teamId);

    addPlayerToTeam({
      ...localPlayer,
      teamId: numericTeamId,
    } as any)
      .then(() => {
        refetchTeamPlayers().then(() => {
          navigation.goBack();
        });
      })
      .catch((error) => {
        console.error('Error adding player:', error);
        addToaster({
          type: ToasterType.ERROR,
          title: i18next.t('API_ERRORS.TITLE'),
          description: i18next.t('API_ERRORS.GENERIC'),
        });
      });
  };

  const handleDeletePlayer = () => {
    deleteConfirmationRef.current?.present();
  };

  const confirmDeletePlayer = () => {
    deleteConfirmationRef.current?.dismiss();
    if (localPlayer.id) {
      deletePlayerFromTeam({ id: localPlayer.id })
        .then(() => {
          refetchTeamPlayers().then(() => {
            navigation.goBack();
          });
        })
        .catch((error) => {
          console.error('Error deleting player:', error);
          addToaster({
            type: ToasterType.ERROR,
            title: i18next.t('API_ERRORS.TITLE'),
            description: i18next.t('API_ERRORS.GENERIC'),
          });
        });
    }
  };

  const cancelDeletePlayer = () => {
    deleteConfirmationRef.current?.dismiss();
  };

  return (
    <ViewContainer>
      <Header
        title={
          action === PlayerActions.ADD
            ? i18next.t('ADMIN.PLAYER_ACTION.ADD.TITLE')
            : i18next.t('ADMIN.PLAYER_ACTION.EDIT.TITLE')
        }
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      {action === PlayerActions.EDIT && localPlayer && (
        <View style={styles.playerHeader}>
          <View style={styles.playerCircleNumber}>
            <Text style={styles.playerCircleText}>{localPlayer.number}</Text>
          </View>
          <Text style={styles.playerRowNameText}>{localPlayer.jerseyName}</Text>
        </View>
      )}

      <View style={styles.playerForm}>
        <View style={styles.formRow}>
          <Text style={styles.rowLabel}>
            {i18next.t('ADMIN.PLAYER_ACTION.FORM.JERSEY_NAME')}
          </Text>
          <TextInput
            editable={action === PlayerActions.ADD}
            style={styles.rowInput}
            value={localPlayer.jerseyName}
            onChangeText={(text) => {
              if (/^[a-zA-Z\s]*$/.test(text)) {
                setLocalPlayer({ ...localPlayer, jerseyName: text });
              }
            }}
          />
        </View>
        <View style={styles.formRow}>
          <Text style={styles.rowLabel}>
            {i18next.t('ADMIN.PLAYER_ACTION.FORM.JERSEY_NUMBER')}
          </Text>
          <TextInput
            editable={action === PlayerActions.ADD}
            style={styles.rowInput}
            value={localPlayer.number}
            onChangeText={(text) => {
              if (/^\d*$/.test(text)) {
                setLocalPlayer({ ...localPlayer, number: text });
              }
            }}
          />
        </View>

        {action === PlayerActions.EDIT && (
          <Button
            containerStyle={styles.deleteButton}
            containerTitleStyle={styles.deleteButtonTitle}
            title={i18next.t('ADMIN.PLAYER_ACTION.FORM.DELETE_PLAYER')}
            onPress={handleDeletePlayer}
          />
        )}

        {action === PlayerActions.ADD && (
          <Button
            disabled={!isLocalPlayerBasicValid}
            title={i18next.t('ADMIN.PLAYER_ACTION.FORM.ADD_PLAYER')}
            onPress={handleAddPlayer}
            containerStyle={styles.addButton}
          />
        )}
      </View>

      <BottomSheet ref={deleteConfirmationRef}>
        <View style={styles.confirmContainer}>
          <Text style={styles.confirmTitle}>
            {i18next.t('ADMIN.PLAYER_ACTION.DELETE_CONFIRM_TITLE')}
          </Text>
          <Text style={styles.confirmSubtitle}>
            {i18next.t('ADMIN.PLAYER_ACTION.DELETE_CONFIRM_MESSAGE', {
              playerName: localPlayer.jerseyName,
            })}
          </Text>
          <View style={styles.confirmButtonContainer}>
            <Button
              title={i18next.t('ADMIN.PLAYER_ACTION.CANCEL')}
              onPress={cancelDeletePlayer}
              containerStyle={styles.cancelButton}
            />
            <Button
              title={i18next.t('ADMIN.PLAYER_ACTION.CONFIRM_DELETE')}
              onPress={confirmDeletePlayer}
              containerStyle={styles.confirmDeleteButton}
              containerTitleStyle={styles.confirmDeleteButtonText}
            />
          </View>
        </View>
      </BottomSheet>
    </ViewContainer>
  );
};

export default PlayerActionScreen;
