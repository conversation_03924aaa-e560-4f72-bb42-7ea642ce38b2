import { StyleSheet } from 'react-native';

export const playerActionScreenStyles = (colors: any, spacings?: any) =>
  StyleSheet.create({
    playerHeader: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      gap: spacings.large,
      marginVertical: spacings.xxlarge,
      fontSize: 16,
      fontWeight: '600',
    },
    playerCircleNumber: {
      width: 70,
      height: 70,
      borderRadius: 35,
      backgroundColor: colors.inverse900,
      justifyContent: 'center',
      alignItems: 'center',
    },
    playerCircleText: {
      color: colors.inverse100,
      fontSize: 14,
      fontWeight: '700',
    },
    playerRowNameText: {
      color: colors.inverse900,
      fontSize: 14,
      fontWeight: '700',
    },
    playerForm: {
      display: 'flex',
      flexDirection: 'column',
      paddingHorizontal: 16,
    },
    formRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'baseline',
      height: 40,
      marginBottom: spacings.xxlarge,
    },
    rowLabel: {
      flexBasis: '40%',
      color: colors.inverse900,
      fontSize: 14,
      fontWeight: '600',
      paddingBottom: spacings.medium,
    },
    rowInput: {
      flex: 1,
      color: colors.inverse900,
      fontSize: 14,
      fontWeight: '600',
      borderBottomWidth: 1,
      borderBottomColor: colors.neutral700,
      height: 40,
    },
    deleteButton: {
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 20,
    },
    deleteButtonTitle: {
      color: colors.red,
    },
    addButton: {
      marginTop: 20,
    },
    confirmContainer: {
      paddingHorizontal: spacings.large,
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.xxxxlarge,
    },
    confirmTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      textAlign: 'center',
      color: colors.inverse900,
    },
    confirmSubtitle: {
      fontSize: 14,
      color: colors.inverse900,
      paddingVertical: spacings.xxxlarge,
      textAlign: 'center',
    },
    confirmButtonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
    },
    cancelButton: {
      flex: 1,
      marginRight: spacings.medium,
    },
    confirmDeleteButton: {
      flex: 1,
      marginLeft: spacings.medium,
      backgroundColor: colors.neutral700,
      borderRadius: 0,
    },
    confirmDeleteButtonText: {
      color: colors.red,
    },
  });
