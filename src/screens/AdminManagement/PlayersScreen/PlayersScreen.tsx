import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { AdminNavigationProp } from '../../../navigator/AdminNavigator';
import {
  <PERSON><PERSON>,
  ViewContainer,
  useTheme,
  Button,
  Icon,
  Loader,
} from '@scorescast/design-system';
import { UseTeamPlayers } from '@scorescast/http-clients/src/hooks/useTeam';
import { i18next } from '@scorescast/translations';
import { playersScreenStyles } from './PlayersScreen.styles';
import { IconName } from '@scorescast/design-system/components/Icon/Icon.types';
import { PlayerActions } from './PlayerActionScreen/PlayerActionScreen';

type PlayersScreenParams = {
  teamId: number;
  teamName: string;
};

type RootStackParamList = {
  PlayersScreen: PlayersScreenParams;
  PlayerActionScreen: {
    action: PlayerActions;
    player?: {
      id?: number;
      jerseyName: string;
      number: string;
    };
    players?: any[];
    teamId: number;
  };
};

type PlayersScreenRouteProp = RouteProp<RootStackParamList, 'PlayersScreen'>;

const PlayersScreen = () => {
  const { colors, spacings } = useTheme();
  const navigation = useNavigation<AdminNavigationProp>();
  const route = useRoute<PlayersScreenRouteProp>();
  const styles = playersScreenStyles(colors, spacings);

  const teamId = route.params.teamId;

  const { teamPlayersRetrieve, isTeamPlayersLoading } = UseTeamPlayers(teamId);

  const players =
    teamPlayersRetrieve?.data?.map((player: any) => ({
      id: player.playerId,
      jerseyName: player.jerseyName,
      number: player.number,
    })) || [];

  const handleAddPlayer = () => {
    navigation.navigate('PlayerActionScreen', {
      action: PlayerActions.ADD,
      players: players,
      teamId: teamId,
    });
  };

  const handleEditPlayer = (player: any) => {
    navigation.navigate('PlayerActionScreen', {
      action: PlayerActions.EDIT,
      player: player,
      players: players,
      teamId: teamId,
    });
  };

  if (isTeamPlayersLoading) {
    return (
      <ViewContainer>
        <Header
          title={i18next.t('TEAM.PLAYERS')}
          withBackButton
          onBackButtonPress={() => navigation.goBack()}
        />
        <Loader />
      </ViewContainer>
    );
  }

  return (
    <ViewContainer>
      <Header
        title={i18next.t('TEAM.PLAYERS')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      <View style={styles.controlsPlayer}>
        <Text style={styles.playersCount}>
          {`${players.length} ${i18next.t('ADMIN.PLAYER_ACTION.PLAYERS')}`}
        </Text>
        <Button
          title={i18next.t('ADMIN.PLAYER_ACTION.ADD_PLAYER')}
          containerStyle={styles.addPlayerButton}
          containerTitleStyle={styles.addPlayerButtonText}
          onPress={handleAddPlayer}
        />
      </View>

      <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
        <View style={styles.teamMembers}>
          {players.map((player: any, index: number) => (
            <View key={`player-${player.id}-${index}`} style={styles.playerRow}>
              <View style={styles.playerRowCircle}>
                <Text style={styles.playerRowCircleText}>{player.number}</Text>
              </View>
              <Text style={styles.playerRowNameText}>{player.jerseyName}</Text>
              <TouchableOpacity
                style={styles.playerRowControls}
                onPress={() => handleEditPlayer(player)}
              >
                <Icon
                  name={IconName.ARROW_RIGHT_LIGHT}
                  isPng={true}
                  width={17}
                  height={28}
                />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </ScrollView>
    </ViewContainer>
  );
};

export default PlayersScreen;
