import React, { useMemo } from 'react';
import { View, Text, FlatList } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import {
  Header,
  ViewContainer,
  useTheme,
  But<PERSON>,
  MatchCard,
  Loader,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { teamMatchesScreenStyles } from './TeamMatchesScreen.styles';
import { useRetrieveTeamMatches, useRetrieveMatchDetails } from '@scorescast/http-client';
import { IconName } from '@scorescast/design-system/components/Icon/Icon.types';
import { MatchPossibleStatus } from '@scorescast/formatters-constants';
import type { Match as MatchType } from '@scorescast/formatters-constants/src/models/match';
import { AdminNavigationProp } from '../../../navigator/AdminNavigator';

// Define the route prop type
type TeamMatchesScreenRouteProp = RouteProp<
  {
    TeamMatchesScreen: {
      teamId: number;
      teamUniqueId?: string;
      teamName?: string;
    };
  },
  'TeamMatchesScreen'
>;

const TeamMatchesScreen = () => {
  const { colors, spacings } = useTheme();
  const navigation = useNavigation<AdminNavigationProp>();
  const route = useRoute<TeamMatchesScreenRouteProp>();
  const styles = teamMatchesScreenStyles(colors, spacings);

  // Get teamId and teamUniqueId from route params
  const { teamId, teamUniqueId, teamName } = route.params;

  // Use the useMatch hook to get team matches
  const { matches, isTeamMatchesLoading } = useMatch(teamUniqueId, undefined);

  // Filter matches to show only upcoming ones (matches with status NS - Not Started)
  const upcomingMatches = useMemo(() => {
    if (!matches?.data) return [];

    return matches.data
      .filter((match: MatchType) => {
        return match.status === MatchPossibleStatus.NS;
      })
      .sort((a: MatchType, b: MatchType) => {
        const timeA = new Date(a.matchTime).getTime();
        const timeB = new Date(b.matchTime).getTime();
        return timeA - timeB; // Sort by proximity to current time
      });
  }, [matches]);

  const handleAddMatch = () => {
    // Navigate to add match screen
    navigation.navigate('EditMatchScreen', {
      teamId,
      teamName: teamName || '',
      teamUniqueId,
    });
  };

  const handleMatchPress = (match: MatchType) => {
    // Navigate to edit match screen in edit mode
    navigation.navigate('EditMatchScreen', {
      teamId,
      teamName: teamName || '',
      teamUniqueId,
      matchId: match.id,
      isEditMode: true,
      matchDetails: match,
    });
  };

  return (
    <ViewContainer>
      <Header
        title={i18next.t('TEAM.MATCHES')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      {/* Add Match Button */}
      <Button
        title={i18next.t('ADMIN.TEAM_MATCHES.ADD_MATCH')}
        onPress={handleAddMatch}
        containerStyle={styles.addMatchButton}
        secondary
      />

      {/* Upcoming Matches Section */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>
          {i18next.t('ADMIN.TEAM_MATCHES.UPCOMING_MATCHES')}
        </Text>

        {isTeamMatchesLoading ? (
          <Loader />
        ) : upcomingMatches.length > 0 ? (
          <FlatList
            data={upcomingMatches}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <MatchCard match={item} onPress={() => handleMatchPress(item)} />
            )}
            contentContainerStyle={styles.matchesList}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <Text style={styles.noMatchesText}>
            {i18next.t('ADMIN.TEAM_MATCHES.NO_UPCOMING_MATCHES')}
          </Text>
        )}
      </View>
    </ViewContainer>
  );
};

export default TeamMatchesScreen;
