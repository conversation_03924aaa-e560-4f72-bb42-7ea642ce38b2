import { StyleSheet } from 'react-native';
import {
  ColorStyles,
  SpacingStyles,
  TypographyStyles,
} from '@scorescast/design-system';

export const teamMatchesScreenStyles = (
  colors: typeof ColorStyles,
  spacings: typeof SpacingStyles,
  typography?: typeof TypographyStyles
) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    addMatchButton: {
      marginTop: spacings.large,
      marginBottom: spacings.large,
      width: '50%',
      alignSelf: 'center',
      borderRadius: 50,
    },
    sectionContainer: {
      flex: 1,
      paddingHorizontal: spacings.large,
      marginBottom: spacings.large,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: colors.inverse900,
      marginBottom: spacings.large,
    },
    matchesList: {
      paddingBottom: spacings.xxlarge,
      gap: spacings.large,
    },
    noMatchesText: {
      fontSize: 16,
      color: colors.inverse700,
      textAlign: 'center',
      marginTop: spacings.xxlarge,
    },
  });
