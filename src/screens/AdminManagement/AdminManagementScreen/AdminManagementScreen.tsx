import { Loader, useTheme } from '@scorescast/design-system';
import { useUserRoles, useUser } from '@scorescast/http-clients';
import React, { useEffect } from 'react';
import { Text, View } from 'react-native';
import { i18next } from '@scorescast/translations';
import AllClubsScreen from '../AllClubsScreen/AllClubsScreen';
import { adminManagementScreenStyles } from './AdminManagementScreen.styles';

const AdminManagementScreen = () => {
  const { colors } = useTheme();
  const { user, isSuperAdmin, isClubAdmin, isTeamAdmin } = useUserRoles();
  const { fetchUser, isFetchUserLoading } = useUser();
  const styles = adminManagementScreenStyles(colors);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  const renderAdminScreen = () => {
    if (!user || !user.roles || user.roles.length === 0) {
      return (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.red }]}>
            {i18next.t('ADMIN.NO_ADMIN_ROLE')}
          </Text>
        </View>
      );
    }

    // All admin roles should see AllClubsScreen
    if (isSuperAdmin || isClubAdmin || isTeamAdmin) {
      return <AllClubsScreen user={user} />;
    } else {
      return (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.red }]}>
            {i18next.t('ADMIN.UNKNOWN_ADMIN_ROLE')}
          </Text>
        </View>
      );
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.neutral900 }]}>
      {isFetchUserLoading ? <Loader /> : renderAdminScreen()}
    </View>
  );
};

export default AdminManagementScreen;
