import { StyleSheet } from 'react-native';
import {
  ColorStyles,
  SpacingStyles,
  TypographyStyles,
} from '@scorescast/design-system';

export const editMatchScreenStyles = (
  colors: typeof ColorStyles,
  spacings: typeof SpacingStyles
) =>
  StyleSheet.create({
    scrollView: {
      flex: 1,
    },
    contentContainer: {
      padding: spacings.large,
      paddingBottom: spacings.xxxlarge,
    },
    inputContainer: {
      marginBottom: spacings.large,
    },
    label: {
      fontSize: 17,
      fontWeight: 'bold',
      color: colors.inverse900,
      marginBottom: spacings.medium,
    },
    dateTimeContainer: {
      flexDirection: 'row',
      marginBottom: spacings.large,
    },
    datePickerButton: {
      flex: 1,
      height: 65,
      backgroundColor: colors.neutral700,
      paddingLeft: 30,
      borderRadius: 10,
      marginRight: spacings.small,
      justifyContent: 'center',
      borderWidth: 0,
    },
    datePickerButtonDisabled: {
      backgroundColor: colors.inverse400,
    },
    timePickerButton: {
      flex: 1,
      height: 65,
      backgroundColor: colors.neutral700,
      paddingLeft: 30,
      borderRadius: 10,
      marginLeft: spacings.small,
      justifyContent: 'center',
      borderWidth: 0,
    },
    timePickerButtonDisabled: {
      backgroundColor: colors.inverse400,
    },
    dateTimeText: {
      fontSize: 17,
      fontWeight: 'bold',
      color: colors.inverse900,
    },
    dateTimeTextDisabled: {
      color: colors.inverse500,
    },
    textArea: {
      height: 100,
      textAlignVertical: 'top',
    },
    saveButton: {
      marginTop: spacings.large,
    },
    deleteButton: {
      marginTop: spacings.medium,
      backgroundColor: colors.red,
      width: '90%',
      alignSelf: 'center',
      borderRadius: 0,
    },
    deleteButtonText: {
      color: colors.inverse100,
    },
    confirmContainer: {
      padding: spacings.large,
    },
    confirmTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: colors.inverse900,
      marginBottom: spacings.medium,
      textAlign: 'center',
    },
    confirmSubtitle: {
      fontSize: 16,
      color: colors.inverse700,
      marginBottom: spacings.large,
      textAlign: 'center',
    },
    confirmButtonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    cancelButton: {
      flex: 1,
      marginRight: spacings.small,
    },
    confirmDeleteButton: {
      flex: 1,
      marginLeft: spacings.small,
      backgroundColor: colors.red,
    },
    confirmDeleteButtonText: {
      color: colors.inverse100,
    },
  });
