import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, Text, TouchableOpacity } from 'react-native';
import DatePicker from 'react-native-date-picker';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import {
  Header,
  ViewContainer,
  useTheme,
  Button,
  Input,
  Select,
  BottomSheet,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { editMatchScreenStyles } from './EditMatchScreen.styles';
import { useTeam, useMatch } from '@scorescast/http-clients';
import { AdminNavigationProp } from '../../../navigator/AdminNavigator';

// Define the route prop type
type EditMatchScreenRouteProp = RouteProp<
  {
    EditMatchScreen: {
      teamId: number;
      teamName: string;
      teamUniqueId?: string;
      matchId?: string;
      isEditMode?: boolean;
      matchDetails?: any;
    };
  },
  'EditMatchScreen'
>;

// Match type options
const matchTypeOptions = [
  { label: i18next.t('ADMIN.EDIT_MATCH.MATCH_TYPE_OPTIONS.FVF'), value: 'FVF' },
  { label: i18next.t('ADMIN.EDIT_MATCH.MATCH_TYPE_OPTIONS.SVS'), value: 'SVS' },
  { label: i18next.t('ADMIN.EDIT_MATCH.MATCH_TYPE_OPTIONS.EVE'), value: 'EVE' },
];

// Match setup options
const matchSetupOptions = [
  { label: i18next.t('ADMIN.EDIT_MATCH.MATCH_SETUP_OPTIONS.P1'), value: 'P1' },
  { label: i18next.t('ADMIN.EDIT_MATCH.MATCH_SETUP_OPTIONS.P2'), value: 'P2' },
  { label: i18next.t('ADMIN.EDIT_MATCH.MATCH_SETUP_OPTIONS.P4'), value: 'P4' },
];

// Location options
const locationOptions = [
  { label: i18next.t('ADMIN.EDIT_MATCH.LOCATION_OPTIONS.HOME'), value: 'HOME' },
  { label: i18next.t('ADMIN.EDIT_MATCH.LOCATION_OPTIONS.AWAY'), value: 'AWAY' },
];

const EditMatchScreen = () => {
  const { colors, spacings } = useTheme();
  const navigation = useNavigation<AdminNavigationProp>();
  const route = useRoute<EditMatchScreenRouteProp>();
  const styles = editMatchScreenStyles(colors, spacings);

  // Get parameters from route
  const { teamId, teamName, teamUniqueId, matchId, isEditMode, matchDetails } =
    route.params;

  if (isEditMode && matchDetails) {
    const originalDateString = matchDetails.matchTime;
    const parsedDate = new Date(originalDateString);
  }

  // Use the useTeam hook to get match functions
  const { createMatch, isCreateMatchLoading } = useTeam();

  // Use the useMatch hook to get team matches and refetch function
  const {
    refetchTeamMatches,
    deleteMatch,
    isDeleteMatchLoading,
    updateMatch,
    isUpdateMatchLoading,
  } = useMatch(
    teamUniqueId, // Use teamUniqueId instead of teamId
    undefined
  );

  // Reference for delete confirmation bottom sheet
  const deleteConfirmationRef = useRef<BottomSheetModal>(null);

  // References for bottom sheets
  const locationSelection = useRef<BottomSheetModal>(null);
  const matchTypeSelection = useRef<BottomSheetModal>(null);
  const matchSetupSelection = useRef<BottomSheetModal>(null);

  // Form state
  const [usTeamName] = useState(teamName);
  const [themTeamName, setThemTeamName] = useState(
    isEditMode && matchDetails ? matchDetails.themTeamName : ''
  );
  const [location, setLocation] = useState(
    isEditMode && matchDetails ? matchDetails.location : 'HOME'
  );
  const [matchType, setMatchType] = useState(
    isEditMode && matchDetails && matchDetails.matchType
      ? matchDetails.matchType
      : 'FVF'
  );
  const [matchSetup, setMatchSetup] = useState(
    isEditMode && matchDetails ? matchDetails.matchSetup : 'P2'
  );
  const [description, setDescription] = useState(
    isEditMode && matchDetails ? matchDetails.description : ''
  );
  const [matchTime, setMatchTime] = useState(
    isEditMode && matchDetails ? new Date(matchDetails.matchTime) : new Date()
  );

  // Date picker state
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState<'date' | 'time'>('date');

  // Form validation
  const [isFormValid, setIsFormValid] = useState(false);

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString();
  };

  // Format time for display
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Removed date and time change handlers

  // Check if form is valid
  useEffect(() => {
    setIsFormValid(
      !!usTeamName &&
        usTeamName.trim().length > 0 &&
        !!themTeamName &&
        themTeamName.trim().length > 0 &&
        !!location &&
        location.trim().length > 0 &&
        !!matchType &&
        matchType.trim().length > 0 &&
        !!matchSetup &&
        matchSetup.trim().length > 0
    );
  }, [usTeamName, themTeamName, location, matchType, matchSetup]);

  // Handle save
  const handleSave = () => {
    const matchData = {
      usTeamName,
      themTeamName,
      location,
      matchType,
      matchSetup,
      description,
      matchTime: matchTime.getTime(),
      teamId,
    };

    if (isEditMode && matchId) {
      // Update existing match
      updateMatch({
        matchId,
        matchData,
        options: {
          onSuccess: () => {
            refetchTeamMatches().then(() => {
              navigation.goBack();
            });
          },
        },
      });
    } else {
      // Create new match
      createMatch({
        matchData,
        options: {
          onSuccess: () => {
            refetchTeamMatches().then(() => {
              navigation.goBack();
            });
          },
        },
      });
    }
  };

  // Handle delete button press
  const handleDeletePress = () => {
    deleteConfirmationRef.current?.present();
  };

  // Handle confirm delete
  const handleConfirmDelete = () => {
    if (!matchId) return;

    deleteConfirmationRef.current?.dismiss();

    // Delete the match with callback
    deleteMatch({
      matchId,
      options: {
        onSuccess: () => {
          // Refetch team matches and navigate back after successful refetch
          refetchTeamMatches().then(() => {
            navigation.goBack();
          });
        },
      },
    });
  };

  // Handle cancel delete
  const handleCancelDelete = () => {
    deleteConfirmationRef.current?.dismiss();
  };

  return (
    <ViewContainer>
      <Header
        title={
          isEditMode
            ? i18next.t('ADMIN.EDIT_MATCH.EDIT_TITLE')
            : i18next.t('ADMIN.EDIT_MATCH.ADD_TITLE')
        }
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Team Name (Read-only) */}
        <Input
          label={i18next.t('ADMIN.EDIT_MATCH.TEAM_NAME')}
          value={usTeamName}
          editable={false}
          containerStyle={styles.inputContainer}
        />
        {/* Opponent Name */}
        <Input
          label={i18next.t('ADMIN.EDIT_MATCH.OPPONENT_NAME')}
          value={themTeamName}
          onChangeText={setThemTeamName}
          placeholder={i18next.t('ADMIN.EDIT_MATCH.ENTER_OPPONENT_NAME')}
          containerStyle={styles.inputContainer}
        />
        {/* Date and Time */}
        <Text style={styles.label}>
          {i18next.t('ADMIN.EDIT_MATCH.DATE_TIME')}
        </Text>
        <View style={styles.dateTimeContainer}>
          <TouchableOpacity
            style={[
              styles.datePickerButton,
              (isCreateMatchLoading || isUpdateMatchLoading) &&
                styles.datePickerButtonDisabled,
            ]}
            onPress={() => {
              setDatePickerMode('date');
              setOpenDatePicker(true);
            }}
            disabled={isCreateMatchLoading || isUpdateMatchLoading}
          >
            <Text
              style={[
                styles.dateTimeText,
                (isCreateMatchLoading || isUpdateMatchLoading) &&
                  styles.dateTimeTextDisabled,
              ]}
            >
              {formatDate(matchTime)}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.timePickerButton,
              (isCreateMatchLoading || isUpdateMatchLoading) &&
                styles.timePickerButtonDisabled,
            ]}
            onPress={() => {
              setDatePickerMode('time');
              setOpenDatePicker(true);
            }}
            disabled={isCreateMatchLoading || isUpdateMatchLoading}
          >
            <Text
              style={[
                styles.dateTimeText,
                (isCreateMatchLoading || isUpdateMatchLoading) &&
                  styles.dateTimeTextDisabled,
              ]}
            >
              {formatTime(matchTime)}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Date Picker Modal */}
        {openDatePicker && (
          <DatePicker
            modal
            open={openDatePicker}
            date={matchTime}
            mode={datePickerMode}
            theme="light"
            confirmText={i18next.t('COMMON.CONFIRM')}
            cancelText={i18next.t('COMMON.CANCEL')}
            buttonColor={colors.neutral700}
            dividerColor={colors.neutral700}
            onConfirm={(date) => {
              setOpenDatePicker(false);
              setMatchTime(date);
            }}
            onCancel={() => {
              setOpenDatePicker(false);
            }}
            title={
              datePickerMode === 'date'
                ? i18next.t('ADMIN.EDIT_MATCH.SELECT_DATE')
                : i18next.t('ADMIN.EDIT_MATCH.SELECT_TIME')
            }
          />
        )}

        {/* Location */}
        <Select
          ref={locationSelection}
          selectLabel={i18next.t('ADMIN.EDIT_MATCH.LOCATION')}
          defaultValue={location}
          list={locationOptions.map((option, index) => ({
            id: option.value,
            name: option.label,
            number: `${index + 1}`,
          }))}
          onOpen={() => {
            locationSelection.current?.present();
          }}
          onItemSelect={(item: {
            id: string;
            name: string;
            number: string;
          }) => {
            locationSelection.current?.dismiss();
            setLocation(item.id);
          }}
        />
        {/* Match Type */}
        <Select
          ref={matchTypeSelection}
          selectLabel={i18next.t('ADMIN.EDIT_MATCH.MATCH_TYPE')}
          defaultValue={matchType}
          list={matchTypeOptions.map((option, index) => ({
            id: option.value,
            name: option.label,
            number: `${index + 1}`,
          }))}
          onOpen={() => {
            matchTypeSelection.current?.present();
          }}
          onItemSelect={(item: {
            id: string;
            name: string;
            number: string;
          }) => {
            matchTypeSelection.current?.dismiss();
            setMatchType(item.id);
          }}
        />
        {/* Match Setup */}
        <Select
          ref={matchSetupSelection}
          selectLabel={i18next.t('ADMIN.EDIT_MATCH.MATCH_SETUP')}
          defaultValue={matchSetup}
          list={matchSetupOptions.map((option, index) => ({
            id: option.value,
            name: option.label,
            number: `${index + 1}`,
          }))}
          onOpen={() => {
            matchSetupSelection.current?.present();
          }}
          onItemSelect={(item: {
            id: string;
            name: string;
            number: string;
          }) => {
            matchSetupSelection.current?.dismiss();
            setMatchSetup(item.id);
          }}
        />
        {/* Description */}
        <Input
          label={i18next.t('ADMIN.EDIT_MATCH.DESCRIPTION')}
          value={description}
          onChangeText={setDescription}
          placeholder={i18next.t('ADMIN.EDIT_MATCH.ENTER_DESCRIPTION')}
          multiline
          numberOfLines={4}
          containerStyle={styles.inputContainer}
          inputStyle={styles.textArea}
        />
        {/* Save Button */}
        <Button
          title={i18next.t('ADMIN.EDIT_MATCH.SAVE')}
          onPress={handleSave}
          disabled={
            !isFormValid || isCreateMatchLoading || isUpdateMatchLoading
          }
          loading={isCreateMatchLoading || isUpdateMatchLoading}
          containerStyle={styles.saveButton}
        />

        {/* Delete Button - Only show in edit mode */}
        {isEditMode && (
          <Button
            title={i18next.t('ADMIN.EDIT_MATCH.DELETE')}
            onPress={handleDeletePress}
            disabled={isDeleteMatchLoading}
            loading={isDeleteMatchLoading}
            containerStyle={styles.deleteButton}
            containerTitleStyle={styles.deleteButtonText}
          />
        )}
      </ScrollView>

      {/* Delete Confirmation Bottom Sheet */}
      <BottomSheet ref={deleteConfirmationRef}>
        <View style={styles.confirmContainer}>
          <Text style={styles.confirmTitle}>
            {i18next.t('ADMIN.EDIT_MATCH.DELETE_CONFIRM_TITLE')}
          </Text>
          <Text style={styles.confirmSubtitle}>
            {i18next.t('ADMIN.EDIT_MATCH.DELETE_CONFIRM_MESSAGE')}
          </Text>
          <View style={styles.confirmButtonContainer}>
            <Button
              title={i18next.t('TEAM.CANCEL')}
              onPress={handleCancelDelete}
              containerStyle={styles.cancelButton}
            />
            <Button
              title={i18next.t('ADMIN.EDIT_MATCH.CONFIRM_DELETE')}
              onPress={handleConfirmDelete}
              containerStyle={styles.confirmDeleteButton}
              containerTitleStyle={styles.confirmDeleteButtonText}
            />
          </View>
        </View>
      </BottomSheet>
    </ViewContainer>
  );
};

export default EditMatchScreen;
