import React, { useEffect, useState, useRef } from 'react';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { View, Text, FlatList } from 'react-native';
import {
  <PERSON><PERSON>,
  ViewContainer,
  useTheme,
  Loader,
  IconInfoList,
  BottomSheet,
  Input,
  Button,
} from '@scorescast/design-system';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { i18next } from '@scorescast/translations';
import { useClub } from '@scorescast/http-clients';
import { teamAgentsScreenStyles } from './TeamAgentsScreen.styles';

interface Agent {
  firstName: string;
  lastName: string;
  email: string;
}

type TeamAgentsScreenRouteProp = RouteProp<
  {
    TeamAgentsScreen: {
      teamId: number;
    };
  },
  'TeamAgentsScreen'
>;

const TeamAgentsScreen = () => {
  const { colors, spacings } = useTheme();
  const navigation = useNavigation();
  const route = useRoute<TeamAgentsScreenRouteProp>();
  const { retrieveTeamAgents } = useClub();
  const styles = teamAgentsScreenStyles(colors, spacings);

  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const agentDetailsRef = useRef<BottomSheetModal>(null);

  useEffect(() => {
    const fetchTeamAgents = async () => {
      try {
        const teamId = route.params.teamId;
        const response = await retrieveTeamAgents(teamId);

        if (response?.status === 'SUCCESS' && response?.data) {
          setAgents(response.data);
        }
      } catch (error) {
        console.error('Error fetching team agents:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (route.params.teamId) {
      fetchTeamAgents();
    }
  }, [route.params.teamId]);

  const handleAgentPress = (agent: Agent) => {
    setSelectedAgent(agent);
    agentDetailsRef.current?.present();
  };

  const handleRemoveAccess = () => {
    // TODO: Implement remove access functionality
    agentDetailsRef.current?.dismiss();
  };

  const closeAgentDetails = () => {
    agentDetailsRef.current?.dismiss();
    setSelectedAgent(null);
  };

  const handleAddAgent = () => {
    // TODO: Implement add agent functionality
    console.log('Add agent clicked for teamId:', route.params.teamId);
  };

  if (isLoading) {
    return <Loader />;
  }

  return (
    <ViewContainer>
      <Header
        title={i18next.t('TEAM.TEAM_AGENTS')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      <Button
        title={i18next.t('TEAM.ADD_AGENT')}
        onPress={handleAddAgent}
        secondary
        containerStyle={styles.addAgentButton}
      />

      {agents.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: colors.inverse400 }]}>
            {i18next.t('TEAM.NO_AGENTS')}
          </Text>
        </View>
      ) : (
        <View style={styles.agentsContainer}>
          <IconInfoList
            items={agents.map((agent, index) => ({
              id: `agent-${agent.email}-${index}`,
              title: `${agent.firstName} ${agent.lastName}`,
              subtitle: agent.email,
              noIcon: true,
              onPress: () => handleAgentPress(agent),
            }))}
          />
        </View>
      )}

      <BottomSheet ref={agentDetailsRef}>
        <View style={styles.bottomSheetContainer}>
          <Text style={[styles.bottomSheetTitle, { color: colors.inverse900 }]}>
            {i18next.t('TEAM.AGENT_DETAILS')}
          </Text>

          {selectedAgent && (
            <View style={styles.agentDetailsContainer}>
              <Input
                label={i18next.t('TEAM.FIRST_NAME')}
                value={selectedAgent.firstName}
                editable={false}
                containerStyle={styles.inputContainer}
              />

              <Input
                label={i18next.t('TEAM.LAST_NAME')}
                value={selectedAgent.lastName}
                editable={false}
                containerStyle={styles.inputContainer}
              />

              <Input
                label={i18next.t('TEAM.EMAIL')}
                value={selectedAgent.email}
                editable={false}
                containerStyle={styles.inputContainer}
              />

              <Button
                title={i18next.t('TEAM.REMOVE_ACCESS')}
                onPress={handleRemoveAccess}
                containerStyle={styles.removeAccessButton}
                containerTitleStyle={styles.removeAccessButtonText}
              />
            </View>
          )}
        </View>
      </BottomSheet>
    </ViewContainer>
  );
};

export default TeamAgentsScreen;
