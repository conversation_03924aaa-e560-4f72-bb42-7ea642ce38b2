import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import {
  useTheme,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  ImageUploader,
  ViewContainer,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { Input } from '@scorescast/design-system';
import { useUser, useClub } from '@scorescast/http-clients';
import { editClubScreenStyles } from './EditClubScreen.styles';

interface Club {
  id: number;
  name: string;
  crest: string;
  website?: string;
}

type EditClubScreenParams = {
  clubId?: number;
  isAddMode?: boolean;
};

type EditClubScreenRouteProp = RouteProp<
  { EditClubScreen: EditClubScreenParams },
  'EditClubScreen'
>;

const EditClubScreen = () => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const route = useRoute<EditClubScreenRouteProp>();
  const { adminForClubs, refetchRetrieveAdminForClubs } = useUser();
  const { updateClub, createClub, isUpdateClubLoading, isCreateClubLoading } =
    useClub();
  const styles = editClubScreenStyles(colors);

  const isAddMode = route.params?.isAddMode || false;
  const isLoading = isAddMode ? isCreateClubLoading : isUpdateClubLoading;

  const [clubName, setClubName] = useState('');
  const [clubLogo, setClubLogo] = useState('');
  const [clubEmail, setClubEmail] = useState('');
  const [club, setClub] = useState<Club | null>(null);
  const [isNameValid, setIsNameValid] = useState(false);
  const [isLogoValid, setIsLogoValid] = useState(false);
  const [isEmailValid, setIsEmailValid] = useState(false);
  const [originalName, setOriginalName] = useState('');
  const [originalLogo, setOriginalLogo] = useState('');
  const [localLoading, setLocalLoading] = useState(false);

  const validateCrestUrl = (url: string): boolean => {
    return !!url && typeof url === 'string' && url.startsWith('https');
  };

  const hasDataChanged = (): boolean => {
    return clubName !== originalName || clubLogo !== originalLogo;
  };

  useEffect(() => {
    if (isAddMode) {
      setClub(null);
      setClubName('');
      setClubLogo('');
      setClubEmail('');
      setIsNameValid(false);
      setIsLogoValid(false);
      setIsEmailValid(false);
      setOriginalName('');
      setOriginalLogo('');

      navigation.setOptions({
        title: i18next.t('ADMIN.EDIT_CLUB.ADD_CLUB'),
      });
    } else if (adminForClubs?.data && route.params?.clubId) {
      const foundClub = adminForClubs.data.find(
        (club) => club.id === route.params.clubId
      );

      if (foundClub) {
        setClub(foundClub);

        setClubName(foundClub.name);
        setIsNameValid(validateClubName(foundClub.name));
        setOriginalName(foundClub.name);

        const isValidCrestUrl = validateCrestUrl(foundClub.crest);

        setClubLogo(foundClub.crest || '');
        setIsLogoValid(isValidCrestUrl);
        setOriginalLogo(isValidCrestUrl ? foundClub.crest : '');

        navigation.setOptions({
          title: i18next.t('ADMIN.EDIT_CLUB.EDIT_CLUB'),
        });
      }
    }
  }, [adminForClubs?.data, route.params?.clubId, isAddMode, navigation]);

  const validateClubName = (value: string) => {
    const isValid = value.trim().length >= 3;
    setIsNameValid(isValid);
    return isValid;
  };

  const validateEmail = (value: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(value);
    setIsEmailValid(isValid);
    return isValid;
  };

  const handleImageUploaded = (imageUrl: string) => {
    setClubLogo(imageUrl);
    setIsLogoValid(validateCrestUrl(imageUrl));
  };

  const handleSaveChanges = () => {
    if (!validateClubName(clubName) || !validateCrestUrl(clubLogo)) {
      return;
    }

    setLocalLoading(true);

    if (isAddMode) {
      if (!validateEmail(clubEmail)) {
        setLocalLoading(false);
        return;
      }

      const newClubData = {
        name: clubName,
        crest: clubLogo,
        adminEmail: clubEmail,
      };

      createClub(newClubData, {
        onSuccess: () => {
          refetchRetrieveAdminForClubs()
            .then(() => navigation.goBack())
            .catch(() => navigation.goBack())
            .finally(() => {
              setLocalLoading(false);
            });
        },
      });
    } else {
      if (!hasDataChanged()) {
        setLocalLoading(false);
        return;
      }

      if (!club?.id) {
        setLocalLoading(false);
        return;
      }

      const updatedClubData = {
        id: club.id,
        name: clubName,
        crest: clubLogo,
      };

      updateClub(updatedClubData, {
        onSuccess: () => {
          refetchRetrieveAdminForClubs()
            .then(() => navigation.goBack())
            .catch(() => navigation.goBack())
            .finally(() => {
              setLocalLoading(false);
            });
        },
      });
    }
  };

  return (
    <ViewContainer>
      <Header
        title={
          isAddMode
            ? i18next.t('ADMIN.EDIT_CLUB.ADD_CLUB')
            : i18next.t('ADMIN.EDIT_CLUB.EDIT_CLUB')
        }
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.logoContainer}>
            <ImageUploader
              initialImage={clubLogo}
              onImageUploaded={handleImageUploaded}
              isEditAllowed={true}
            />
            {!isLogoValid && (
              <Text style={[styles.helperText, { color: colors.red }]}>
                {i18next.t('ADMIN.EDIT_CLUB.CREST_REQUIRED')}
              </Text>
            )}
          </View>

          <Input
            label={i18next.t('ADMIN.EDIT_CLUB.CLUB_NAME')}
            value={clubName}
            onChangeText={(text) => {
              setClubName(text);
              validateClubName(text);
            }}
            placeholder={i18next.t('ADMIN.EDIT_CLUB.ENTER_CLUB_NAME')}
          />

          {isAddMode && (
            <Input
              label={i18next.t('ADMIN.EDIT_CLUB.CLUB_EMAIL')}
              value={clubEmail}
              onChangeText={(text) => {
                setClubEmail(text);
                validateEmail(text);
              }}
              placeholder={i18next.t('ADMIN.EDIT_CLUB.ENTER_CLUB_EMAIL')}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          )}

          <Button
            title={
              isAddMode
                ? i18next.t('ADMIN.EDIT_CLUB.CREATE_CLUB')
                : i18next.t('ADMIN.EDIT_CLUB.SAVE_CHANGES')
            }
            onPress={handleSaveChanges}
            loading={isLoading || localLoading}
            disabled={
              !isNameValid ||
              !isLogoValid ||
              (isAddMode && !isEmailValid) ||
              (!isAddMode && !hasDataChanged()) ||
              isLoading ||
              localLoading
            }
            containerStyle={styles.saveButton}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </ViewContainer>
  );
};

export default EditClubScreen;
