import { StyleSheet } from 'react-native';
import { ColorStyles } from '@scorescast/design-system';

export const editClubScreenStyles = (colors: typeof ColorStyles) =>
  StyleSheet.create({
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollContent: {
      padding: 16,
      flexGrow: 1,
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 24,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: 16,
      textAlign: 'center',
    },
    saveButton: {
      marginTop: 32,
      marginBottom: 16,
    },
    helperText: {
      fontSize: 12,
      marginTop: 8,
      textAlign: 'center',
    },
  });
