import React from 'react';
import { Text, View, FlatList } from 'react-native';
import {
  useThem<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Lo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ViewContainer,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { useUser, useUserRoles } from '@scorescast/http-clients';
import { allClubsScreenStyles } from './AllClubsScreen.styles';

interface AllClubsScreenProps {
  user: any;
}

type RootStackParamList = {
  EditClubScreen: { clubId?: number; isAddMode?: boolean };
  ClubScreen: { clubId: number; clubName?: string };
};

type AllClubsScreenNavigationProp = NavigationProp<RootStackParamList>;

const AllClubsScreen: React.FC<AllClubsScreenProps> = ({ user }) => {
  const { colors, spacings } = useTheme();
  const navigation = useNavigation<AllClubsScreenNavigationProp>();
  const {
    adminForClubs,
    isRetrieveAdminForClubsLoading,
    adminForTeams,
    isRetrieveAdminForTeamsLoading,
  } = useUser();
  const styles = allClubsScreenStyles(colors, spacings);

  // Use centralized role hierarchy logic
  const { isSuperAdmin, isClubAdmin, isTeamAdmin, canEditClubs } =
    useUserRoles();

  const editClub = (clubId: number) => () => {
    navigation.navigate('EditClubScreen', { clubId });
  };

  const viewClub = (clubId: number, clubName?: string) => () => {
    navigation.navigate('ClubScreen', { clubId, clubName });
  };

  const handleAddClub = () => {
    navigation.navigate('EditClubScreen', { isAddMode: true });
  };

  // Process data based on user role hierarchy
  const getClubsData = () => {
    // SUPER_ADMIN and CLUB_ADMIN use adminForClubs data
    if (isSuperAdmin || isClubAdmin) {
      return adminForClubs?.data || [];
    }

    // TEAM_ADMIN uses adminForTeams data
    if (isTeamAdmin && adminForTeams) {
      const clubsFromTeams: any[] = [];
      try {
        const adminTeamsData = adminForTeams as any;
        if (
          adminTeamsData &&
          typeof adminTeamsData === 'object' &&
          adminTeamsData.data &&
          typeof adminTeamsData.data === 'object'
        ) {
          // Create a club object for each club name in the data
          Object.keys(adminTeamsData.data).forEach((clubName, index) => {
            const teams = adminTeamsData.data[clubName];
            if (Array.isArray(teams) && teams.length > 0) {
              // Use the first team's club information if available
              const firstTeam = teams[0];
              clubsFromTeams.push({
                id: firstTeam.clubId || index + 1, // Use clubId if available, otherwise use index
                name: clubName,
                crest: firstTeam.clubCrest || null,
                website: firstTeam.clubWebsite || '',
              });
            }
          });
        }
      } catch (error) {
        console.error('Error processing adminForTeams data:', error);
      }
      return clubsFromTeams;
    }

    // Fallback to empty array
    return [];
  };

  const clubsData = getClubsData();

  if (isRetrieveAdminForClubsLoading || isRetrieveAdminForTeamsLoading) {
    return <Loader />;
  }
  return (
    <ViewContainer>
      <Header
        title={i18next.t('ADMIN.CLUB_ADMIN.CLUB_ADMIN_PANEL')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      {isSuperAdmin && (
        <Button
          title={i18next.t('ADMIN.CLUB_ADMIN.ADD_CLUB')}
          onPress={handleAddClub}
          secondary
          containerStyle={styles.addClubButton}
        />
      )}
      {!isSuperAdmin && clubsData?.length === 0 && (
        <Text style={[styles.contactSupportText, { color: colors.inverse400 }]}>
          {isTeamAdmin
            ? i18next.t('ADMIN.CLUB_ADMIN.CONTACT_SUPPORT_FOR_TEAM')
            : i18next.t('ADMIN.CLUB_ADMIN.CONTACT_SUPPORT_FOR_CLUB')}
        </Text>
      )}
      <View style={styles.clubsContainer}>
        <FlatList
          data={clubsData}
          renderItem={({ item }) => {
            const isValidCrestUrl =
              item.crest &&
              typeof item.crest === 'string' &&
              item.crest.startsWith('https');

            return (
              <DisplayCard
                image={isValidCrestUrl ? item.crest : null}
                title={item.name}
                website={item.website}
                onEditPress={canEditClubs ? editClub(item.id) : undefined}
                onPress={viewClub(item.id, item.name)}
              />
            );
          }}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.clubsList}
        />
      </View>
    </ViewContainer>
  );
};

export default AllClubsScreen;
