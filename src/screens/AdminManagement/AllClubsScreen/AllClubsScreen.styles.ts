import { StyleSheet } from 'react-native';
import { ColorStyles } from '@scorescast/design-system';
import { SpacingStyles } from '@scorescast/design-system/theme/spacing';

export const allClubsScreenStyles = (
  colors: typeof ColorStyles,
  spacings: SpacingStyles
) =>
  StyleSheet.create({
    title: {
      fontSize: 22,
      fontWeight: 'bold',
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      marginBottom: 24,
    },
    infoCard: {
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderRadius: 8,
      padding: 16,
      marginTop: 16,
    },
    infoText: {
      fontSize: 14,
      lineHeight: 20,
    },
    centerContent: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    loadingText: {
      fontSize: 16,
      textAlign: 'center',
    },
    noClubsText: {
      fontSize: 18,
      textAlign: 'center',
      marginBottom: 20,
    },
    contactSupportText: {
      fontSize: 14,
      textAlign: 'center',
      marginTop: 10,
    },
    addClubButton: {
      marginTop: 20,
      width: '80%',
      alignSelf: 'center',
    },
    clubsContainer: {
      flex: 1,
      width: '80%',
      alignSelf: 'center',
      marginTop: spacings.xxxxlarge,
    },
    clubsList: {
      paddingBottom: 20,
      gap: spacings.xxxlarge,
    },
    clubCard: {
      flexDirection: 'row',
      backgroundColor: colors.neutral700,
      borderRadius: 8,
      padding: spacings.xlarge,
      marginBottom: 12,
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    clubContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    placeholderLogo: {
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
    },
    clubInfo: {
      flex: 1,
    },
    clubName: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    clubWebsite: {
      fontSize: 12,
      marginTop: 4,
    },
    editButton: {
      padding: 8,
    },
    editButtonText: {
      fontSize: 16,
    },
  });
