import React from 'react';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { View, Text } from 'react-native';
import {
  Header,
  ViewContainer,
  useTheme,
  Loader,
} from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { UseTeamFollowers } from '@scorescast/http-clients/src/hooks/useTeam';
import { teamFollowersScreenStyles } from './TeamFollowersScreen.styles';

interface Follower {
  firstName: string;
  lastName: string;
  email: string;
}

type TeamFollowersScreenRouteProp = RouteProp<
  {
    TeamFollowersScreen: {
      teamId: number;
      teamName?: string;
    };
  },
  'TeamFollowersScreen'
>;

const TeamFollowersScreen = () => {
  const { colors, spacings } = useTheme();
  const navigation = useNavigation();
  const route = useRoute<TeamFollowersScreenRouteProp>();
  const styles = teamFollowersScreenStyles(colors, spacings);

  const teamId = route.params.teamId;
  const { teamFollowers, isTeamFollowersLoading } = UseTeamFollowers(teamId);

  const followers: Follower[] =
    teamFollowers?.data?.map((follower: any) => ({
      firstName: follower.firstName,
      lastName: follower.lastName,
      email: follower.email,
    })) || [];

  if (isTeamFollowersLoading) {
    return (
      <ViewContainer>
        <Header
          title={i18next.t('TEAM.FOLLOWERS')}
          withBackButton
          onBackButtonPress={() => navigation.goBack()}
        />
        <Loader />
      </ViewContainer>
    );
  }

  return (
    <ViewContainer>
      <Header
        title={i18next.t('TEAM.FOLLOWERS')}
        withBackButton
        onBackButtonPress={() => navigation.goBack()}
      />

      {followers.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: colors.inverse400 }]}>
            {i18next.t('TEAM.NO_FOLLOWERS')}
          </Text>
        </View>
      ) : (
        <View style={styles.followersContainer}>
          {followers.map((follower, index) => (
            <View
              key={`follower-${follower.email}-${index}`}
              style={styles.followerItem}
            >
              <View style={styles.followerInfo}>
                <Text
                  style={[styles.followerName, { color: colors.inverse100 }]}
                >
                  {`${follower.firstName} ${follower.lastName}`}
                </Text>
                <Text
                  style={[styles.followerEmail, { color: colors.inverse400 }]}
                >
                  {follower.email}
                </Text>
              </View>
            </View>
          ))}
        </View>
      )}
    </ViewContainer>
  );
};

export default TeamFollowersScreen;
