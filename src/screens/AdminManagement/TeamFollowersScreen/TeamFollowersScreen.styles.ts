import { StyleSheet } from 'react-native';
import { ColorStyles, SpacingStyles } from '@scorescast/design-system';

export const teamFollowersScreenStyles = (
  colors: typeof ColorStyles,
  spacings?: typeof SpacingStyles
) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: spacings?.large || 16,
    },
    emptyText: {
      fontSize: 16,
      textAlign: 'center',
    },
    followersContainer: {
      flex: 1,
      paddingHorizontal: spacings?.medium || 12,
    },
    followerItem: {
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      borderRadius: spacings?.medium || 12,
      padding: spacings?.medium || 12,
      marginVertical: spacings?.small || 8,
    },
    followerInfo: {
      flex: 1,
    },
    followerName: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: spacings?.xsmall || 4,
    },
    followerEmail: {
      fontSize: 14,
      fontWeight: '400',
    },
  });
