import {
  useAppStore,
  useAppConfig,
  refreshNetworkConnectivity,
} from '@scorescast/http-clients';
import React, { useEffect } from 'react';
import DeviceInfo from 'react-native-device-info';
import {
  Loader,
  ApplicationErrorModal,
  Lotties,
  useTheme,
} from '@scorescast/design-system';
import { useAppStateChange } from '../../hooks/useAppStateChange';
import { i18next } from '@scorescast/translations';
import { appLoaderStyles } from './AppLoader.styles';
import { openAppInBrowser } from '../../helpers/open-link';

const AppLoader = ({ children }: { children: React.ReactNode }) => {
  const config = useAppStore((state) => state.config);
  const isConnected = useAppStore((state) => state.isConnected);
  const { fetchConfig } = useAppConfig();
  const { spacings } = useTheme();
  const styles = appLoaderStyles(spacings);

  const buildNumber = DeviceInfo.getBuildNumber();
  const isNetworkError = !isConnected && isConnected !== null;

  useEffect(() => {
    fetchConfig();
  }, []);

  useAppStateChange(fetchConfig);

  if (!config) return <Loader />;

  if (config?.maintenance) {
    return (
      <ApplicationErrorModal
        visible={true}
        lottie={Lotties.maintenance}
        title={i18next.t('ERRORS.MAINTENANCE.TITLE')}
        description={i18next.t('ERRORS.MAINTENANCE.DESCRIPTION')}
        buttonTitle={i18next.t('ERRORS.MAINTENANCE.RETRY')}
        callbackFc={fetchConfig}
        customLottieStyle={styles.lottie}
      />
    );
  }

  if (config?.minAppVersion && buildNumber < config?.minAppVersion) {
    return (
      <ApplicationErrorModal
        visible={true}
        lottie={Lotties.appUpdate}
        title={i18next.t('ERRORS.APP_UPDATE.TITLE')}
        description={i18next.t('ERRORS.APP_UPDATE.DESCRIPTION')}
        buttonTitle={i18next.t('ERRORS.APP_UPDATE.RETRY')}
        callbackFc={openAppInBrowser}
        customLottieStyle={styles.lottie}
      />
    );
  }

  return (
    <>
      <ApplicationErrorModal
        visible={isNetworkError}
        title={i18next.t('ERRORS.NO_INTERNET.TITLE')}
        description={i18next.t('ERRORS.NO_INTERNET.DESCRIPTION')}
        buttonTitle={i18next.t('ERRORS.NO_INTERNET.RETRY')}
        lottie={Lotties.noInternet}
        callbackFc={refreshNetworkConnectivity}
      />
      {children}
    </>
  );
};

export default AppLoader;
