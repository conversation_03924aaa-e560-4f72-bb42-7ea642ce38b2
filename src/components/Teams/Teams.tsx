import {
  Match,
  Team,
  filterMatchesByStatus,
  TeamMatchesTabsConfig,
  MatchPossibleStatus,
} from '@scorescast/formatters-constants';
import { useMatch, storage, STORAGE_KEYS } from '@scorescast/http-clients';
import {
  useTheme,
  TeamAvatar,
  Tabs,
  MatchCard,
  AddTeamButton,
  Error,
  Lotties,
  Loader,
} from '@scorescast/design-system';
import { useNavigation } from '@react-navigation/native';
import { i18next } from '@scorescast/translations';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View, FlatList, RefreshControl } from 'react-native';
import { teamsStyles } from './Teams.styles';
import { Screens } from '../../navigator/navigator';
import { teamTabs } from '../../constants/tabs';
import { useAgentStore } from '@scorescast/http-clients/src/stores/useAgentStore';

interface TeamsProps {
  teams: Team[];
  onPressAddTeam: () => void;
}

export const Teams = ({ teams, onPressAddTeam }: TeamsProps) => {
  const { setUsTeamImage } = useAgentStore();
  const { colors, spacings } = useTheme();
  const [selectedTeam, setSelectedTeam] = useState<Team>(
    (teams.find((team) => {
      if (
        team?.teamUniqueId === storage.getString(STORAGE_KEYS.SELECTED_TEAM_ID)
      ) {
        return true;
      }
    }) as Team) ?? teams[0]
  );

  const [selectedTab, setSelectedTab] = useState<TeamMatchesTabsConfig>(
    teamTabs[0]
  );
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { matches, refetchTeamMatches, isTeamMatchesLoading } = useMatch(
    selectedTeam?.teamUniqueId,
    selectedTeam?.image
  );

  useEffect(() => {
    setUsTeamImage(selectedTeam?.image ?? '');
  }, [selectedTeam]);

  const filteredMatches = useMemo(() => {
    if (!matches?.data) return [];
    return filterMatchesByStatus(matches.data, selectedTab.key);
  }, [matches?.data, selectedTab.key]);

  const styles = teamsStyles(spacings);
  const navigation = useNavigation<any>();

  const onMatchPress = useCallback(
    (match: Match) => {
      if (
        ['CAN_CLAIM', 'CLAIMED_BY_YOU'].includes(match?.claimStatus) &&
        match.status !== MatchPossibleStatus.END
      ) {
        navigation.navigate(Screens.AgentScreen, {
          matchId: match.id,
          status: match.status,
          teamIds: {
            teamUniqueId: selectedTeam.teamUniqueId,
            id: selectedTeam?.id,
          },
        });
      } else {
        navigation.navigate(Screens.MatchScreen, {
          matchId: match.id,
          status: match.status,
        });
      }
    },
    [navigation, selectedTeam]
  );

  const onTabPress = useCallback((tab: string) => {
    setSelectedTab(
      teamTabs.find((t) => t.label === tab) as TeamMatchesTabsConfig
    );
  }, []);

  const onTeamPress = useCallback(
    (teamUniqueId?: string) => {
      if (teamUniqueId)
        storage.set(STORAGE_KEYS.SELECTED_TEAM_ID, teamUniqueId);
      setSelectedTeam(
        teams.find((team) => team?.teamUniqueId === teamUniqueId) as Team
      );
    },
    [teams]
  );

  const onRefresh = useCallback(() => {
    setIsRefreshing(true);
    refetchTeamMatches().then(() => {
      setIsRefreshing(false);
    });
  }, [refetchTeamMatches]);

  if (isTeamMatchesLoading || !filteredMatches) {
    return <Loader />;
  }
  return (
    <View>
      {teams && (
        <View style={styles.teamsContainer}>
          <FlatList
            data={teams}
            showsHorizontalScrollIndicator={false}
            horizontal
            bounces={false}
            ListFooterComponent={<AddTeamButton onPress={onPressAddTeam} />}
            keyExtractor={(item) => item?.teamUniqueId ?? ''}
            renderItem={({ item }) => (
              <TeamAvatar
                key={item.teamUniqueId}
                team={item}
                onPress={onTeamPress}
                containerStyle={styles.teamAvatar}
                isSelected={selectedTeam?.teamUniqueId === item.teamUniqueId}
              />
            )}
          />
        </View>
      )}
      <Tabs
        tabs={teamTabs.map((t) => t.label)}
        containerStyle={styles.tabsContainer}
        onTabPress={onTabPress}
      />
      <FlatList
        contentContainerStyle={{ paddingBottom: 150 }}
        data={filteredMatches}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[colors.inverse900]}
            tintColor={colors.inverse900}
          />
        }
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <MatchCard
            key={item.id}
            match={item}
            onPress={onMatchPress}
            teamId={selectedTeam?.teamUniqueId}
          />
        )}
        ListEmptyComponent={
          <Error
            lottie={Lotties.noMatches}
            title={i18next.t('ERRORS.NO_MATCHES.TITLE')}
            description={i18next.t('ERRORS.NO_MATCHES.DESCRIPTION')}
          />
        }
      />
    </View>
  );
};
