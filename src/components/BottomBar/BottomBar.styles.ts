import { StyleSheet } from 'react-native';
import {
  ColorStyles,
  SpacingStyles,
  TypographyStyles,
} from '@scorescast/design-system';

export const bottomBarStyles = (
  colors: typeof ColorStyles,
  spacings: typeof SpacingStyles,
  typography: typeof TypographyStyles
) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.primary,
      height: 90,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: spacings.ultralarge,
      borderTopWidth: 2,
      borderTopColor: colors.inverse400,
    },
    bigIconContainer: {
      position: 'absolute',
      top: -70,
    },
    bigIconLabelPosition: {
      marginTop: 25,
    },
    bottomBarItem: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    label: {
      ...typography['xSmall-bold'],
      color: colors.inverse900,
      paddingTop: spacings.medium,
    },
    inactiveItem: {
      opacity: 0.3,
    },
  });
