import React, { useCallback } from 'react';
import { TouchableOpacity, Text, View } from 'react-native';
import { useTheme, IconName } from '@scorescast/design-system/';
import { bottomBarStyles } from './BottomBar.styles';
import { Icon } from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';
import { Navigators, Screens } from '../../navigator/navigator';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';

const BOTTOM_BAR_ITEMS = [
  {
    iconNameInactive: IconName.HOME_INACTIVE,
    iconNameActive: IconName.HOME_ACTIVE,
    label: i18next.t('BOTTOM_BAR.HOME'),
    screenName: Navigators.HomeNavigator,
  },
  // to be added later
  // {
  //   iconNameInactive: IconName.ONGOING_INACTIVE,
  //   iconNameActive: IconName.ONGOING_ACTIVE,
  //   label: i18next.t('BOTTOM_BAR.ONGOING'),
  //   screenName: Screens.OngoingScreen,
  // },
  {
    iconNameInactive: IconName.PROFILE_INACTIVE,
    iconNameActive: IconName.PROFILE_ACTIVE,
    label: i18next.t('BOTTOM_BAR.PROFILE'),
    screenName: Navigators.ProfileNavigator,
  },
];

interface BottomBarTileProps {
  iconNameInactive: IconName;
  iconNameActive: IconName;
  label: string;
  screenName: Screens | Navigators;
  navigation: BottomTabBarProps['navigation'];
  isActive: boolean;
}

const BottomBarTile: React.FC<BottomBarTileProps> = ({
  iconNameInactive,
  iconNameActive,
  screenName,
  label,
  isActive,
  navigation,
}) => {
  const { colors, spacings, typography } = useTheme();
  const styles = bottomBarStyles(colors, spacings, typography);

  const isOngoing = screenName === Screens.OngoingScreen;

  const onPress = useCallback(() => {
    const event = navigation.emit({
      type: 'tabPress',
      canPreventDefault: true,
    });
    if (!event.defaultPrevented) {
      navigation.navigate(screenName as never);
    }
  }, []);

  return (
    <TouchableOpacity
      activeOpacity={0.9}
      style={[styles.bottomBarItem, !isActive && styles.inactiveItem]}
      onPress={onPress}
    >
      <View style={isOngoing && styles.bigIconContainer}>
        <Icon
          name={isActive ? iconNameActive : iconNameInactive}
          isPng
          width={isOngoing ? 100 : 26}
          height={isOngoing ? 100 : 26}
        />
      </View>
      <Text style={[styles.label, isOngoing && styles.bigIconLabelPosition]}>
        {label}
      </Text>
    </TouchableOpacity>
  );
};

interface BottomBarProps {
  selectedIndex: number;
  navigation: BottomTabBarProps['navigation'];
}

const BottomBar: React.FC<BottomBarProps> = ({ selectedIndex, navigation }) => {
  const { colors, spacings, typography } = useTheme();
  const styles = bottomBarStyles(colors, spacings, typography);
  return (
    <View style={styles.container}>
      {BOTTOM_BAR_ITEMS.map((item, index) => {
        return (
          <BottomBarTile
            key={index}
            {...item}
            isActive={index === selectedIndex}
            navigation={navigation}
          />
        );
      })}
    </View>
  );
};

export default BottomBar;
