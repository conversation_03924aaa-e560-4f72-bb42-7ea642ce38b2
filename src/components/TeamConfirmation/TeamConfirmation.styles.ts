import { StyleSheet } from 'react-native';
import { SpacingStyles } from '@scorescast/design-system/theme/spacing';
import { TypographyStyles } from '@scorescast/design-system/theme/typography';
import { ColorStyles } from '@scorescast/design-system/theme/colors.light';

export const teamConfirmationStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.xxxxlarge,
      paddingHorizontal: spacings.xxlarge,
    },
    title: {
      ...typography['large-bold'],
      color: colors.inverse900,
      alignSelf: 'center',
    },
    subtitle: {
      ...typography['small'],
      color: colors.inverse900,
      marginVertical: spacings.xlarge,
      alignSelf: 'center',
    },
    image: {
      width: 220,
      height: 220,
      borderRadius: 10,
      resizeMode: 'contain',
      alignSelf: 'center',
      overflow: 'hidden',
    },
    teamName: {
      ...typography['medium-bold'],
      color: colors.inverse900,
      marginVertical: spacings.xlarge,
      alignSelf: 'center',
    },
    button: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    discard: {
      ...typography['small'],
      color: colors.inverse900,
      textDecorationLine: 'underline',
      marginTop: spacings.xlarge,
      textDecorationColor: colors.inverse900,
      alignSelf: 'center',
    },
  });
