import { i18next } from '@scorescast/translations';
import {
  useNotificationsStore,
  useRetrieveTeamDetailsByUniqueId,
  useRetrieveFollowedTeams,
  useFollowTeam,
  useUnfollowTeam,
  useAuthStore,
} from '@scorescast/http-client';
import {
  eventSubscriber,
  Events,
  NOTIFICATION_KEYS,
} from '@scorescast/formatters-constants';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Text, Image, View } from 'react-native';
import {
  BottomSheet,
  Button,
  useTheme,
  NotificationBottomSheet,
} from '@scorescast/design-system';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { teamConfirmationStyles } from './TeamConfirmation.styles';
import { useNotifications } from '../../hooks/useNotifications';

type BottomSheetModalWithTeam = BottomSheetModal & { teamUniqueId?: string };

export const TeamConfirmation = forwardRef(() => {
  const ref = useRef<BottomSheetModal>(null);
  const notificationBottomSheetRef = useRef<BottomSheetModalWithTeam>(null);
  const { isAuthenticated } = useAuthStore();
  // Use our new controller hooks
  const [teamUniqueId, setTeamUniqueId] = useState<string | null>(null);

  const { data: team, refetch: retrieveTeamById } =
    useRetrieveTeamDetailsByUniqueId(
      {
        uniqueId: teamUniqueId || undefined,
      },
      {
        enabled: !!teamUniqueId,
      }
    );

  const { refetch: refetchFollowedTeams } = useRetrieveFollowedTeams({
    enabled: isAuthenticated,
  });

  const followTeamMutation = useFollowTeam({});
  const { colors, spacings, typography } = useTheme();
  const { requestPermission, addPropertyValue } = useNotifications();
  const { notifications } = useNotificationsStore();
  const styles = teamConfirmationStyles(colors, spacings, typography);

  eventSubscriber(Events.SEARCH_TEAM_BY_ID, ({ teamUniqueId }) => {
    setTeamUniqueId(teamUniqueId);
  });

  useEffect(() => {
    if (teamUniqueId && isAuthenticated) {
      // The team data will be automatically fetched when teamUniqueId changes
      // due to the enabled condition in the query
      ref?.current?.present();
    }
  }, [teamUniqueId, isAuthenticated, team]);

  const checkNotificationPermission = useCallback(async () => {
    if (!notifications) {
      notificationBottomSheetRef.current?.present();
    } else {
      await addPropertyValue(
        NOTIFICATION_KEYS.FOLLOWED_TEAMS,
        team?.data?.teamUniqueId
      );
    }
  }, [notifications, addPropertyValue, team?.data?.teamUniqueId]);

  const onAllowNotifications = useCallback(async () => {
    if (await requestPermission()) {
      await addPropertyValue(
        NOTIFICATION_KEYS.FOLLOWED_TEAMS,
        team?.data?.teamUniqueId
      );
    }
    notificationBottomSheetRef.current?.dismiss();
  }, [requestPermission, addPropertyValue, team?.data?.teamUniqueId]);

  const onPressButton = useCallback(() => {
    if (team?.data?.teamUniqueId) {
      followTeamMutation.mutate(
        {
          queryParams: { teamUniqueId: team.data.teamUniqueId },
        },
        {
          onSuccess: async () => {
            refetchFollowedTeams();
            ref?.current?.dismiss();
            checkNotificationPermission();
          },
        }
      );
    }
  }, [
    team?.data?.teamUniqueId,
    followTeamMutation,
    refetchFollowedTeams,
    ref,
    checkNotificationPermission,
  ]);

  return (
    <>
      <BottomSheet ref={ref}>
        <View style={styles.container}>
          <Text style={styles.title}>
            {i18next.t('TEAM_CONFIRMATION.TITLE')}
          </Text>
          <Text style={styles.subtitle}>
            {i18next.t('TEAM_CONFIRMATION.MESSAGE')}
          </Text>
          <Image
            source={{
              uri: team?.data?.image,
            }}
            style={styles.image}
          />
          <Text numberOfLines={1} style={styles.teamName}>
            {team?.data?.name}
          </Text>
          <Button
            secondary
            title={i18next.t('TEAM_CONFIRMATION.BUTTON')}
            onPress={onPressButton}
            containerStyle={styles.button}
          />
          <Text
            numberOfLines={1}
            style={styles.discard}
            onPress={() => ref?.current?.dismiss()}
          >
            {i18next.t('TEAM_CONFIRMATION.DISCARD')}
          </Text>
        </View>
      </BottomSheet>
      <NotificationBottomSheet
        ref={notificationBottomSheetRef}
        onPressAllow={onAllowNotifications}
        onPressDeny={() => notificationBottomSheetRef.current?.dismiss()}
      />
    </>
  );
});

TeamConfirmation.displayName = 'TeamConfirmation';
