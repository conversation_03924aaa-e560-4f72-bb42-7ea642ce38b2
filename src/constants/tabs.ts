import {
  TeamMatchesTabsConfig,
  TeamMatches<PERSON>abs<PERSON><PERSON>,
  MatchTabsConfig,
  MatchTabsKey,
} from '@scorescast/formatters-constants';
import { i18next } from '@scorescast/translations';

export const teamTabs: (typeof TeamMatchesTabsConfig)[] = [
  { key: TeamMatchesTabsKey.ALL, label: i18next.t('TEAM_MATCHES_TABS.ALL') },
  { key: TeamMatchesTabsKey.LIVE, label: i18next.t('TEAM_MATCHES_TABS.LIVE') },
  {
    key: TeamMatchesTabsKey.UPCOMING,
    label: i18next.t('TEAM_MATCHES_TABS.UPCOMING'),
  },
  { key: TeamMatchesTabsKey.PAST, label: i18next.t('TEAM_MATCHES_TABS.PAST') },
];

export const matchTabs: (typeof MatchTabsConfig)[] = [
  { key: MatchTabsKey.FEED, label: i18next.t('MATCH_TABS.FEED') },
  { key: MatchTabsKey.STATS, label: i18next.t('MATCH_TABS.STATS') },
];
