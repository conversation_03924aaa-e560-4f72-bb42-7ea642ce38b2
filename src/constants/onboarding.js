import { IconName } from '@scorescast/design-system';
import { i18next } from '@scorescast/translations';

const randomBackgroundImage = () => {
  const currentDay = new Date().getDate();
  if (currentDay % 5 === 0) {
    return IconName.BACKGROUND_IMAGE_5;
  } else if (currentDay % 5 === 1) {
    return IconName.BACKGROUND_IMAGE_4;
  } else if (currentDay % 5 === 2) {
    return IconName.BACKGROUND_IMAGE_3;
  } else if (currentDay % 5 === 3) {
    return IconName.BACKGROUND_IMAGE_2;
  } else if (currentDay % 5 === 4) {
    return IconName.BACKGROUND_IMAGE_1;
  }
  return IconName.BACKGROUND_IMAGE_5;
};

export const ONBOARDING_STEPS = 3;

export const OnboardingSteps = {
  FIRST_STEP: {
    title: i18next.t('ONBOARDING.FIRST_STEP.TITLE'),
    buttonTitle: i18next.t('ONBOARDING.FIRST_STEP.BUTTON_TITLE'),
    withImage: false,
    withStepper: false,
    withOverlay: true,
    backgroundImage: IconName.BACKGROUND_IMAGE_5,
  },
  SECOND_STEP: {
    title: i18next.t('ONBOARDING.SECOND_STEP.TITLE'),
    buttonTitle: i18next.t('ONBOARDING.SECOND_STEP.BUTTON_TITLE'),
    withImage: true,
    image: IconName.ONBOARDING_2,
    withStepper: true,
    backgroundImage: IconName.BACKGROUND_IMAGE_5,
  },
  THIRD_STEP: {
    title: i18next.t('ONBOARDING.THIRD_STEP.TITLE'),
    buttonTitle: i18next.t('ONBOARDING.THIRD_STEP.BUTTON_TITLE'),
    withImage: true,
    image: IconName.ONBOARDING_1,
    withStepper: true,
    backgroundImage: IconName.BACKGROUND_IMAGE_5,
  },
  FOURTH_STEP: {
    title: i18next.t('ONBOARDING.FOURTH_STEP.TITLE'),
    buttonTitle: i18next.t('ONBOARDING.FOURTH_STEP.BUTTON_TITLE'),
    withImage: true,
    image: IconName.ONBOARDING_3,
    withStepper: true,
    backgroundImage: IconName.BACKGROUND_IMAGE_5,
  },
  FIFTH_STEP: {
    title: i18next.t('ONBOARDING.FIFTH_STEP.TITLE'),
    buttonTitle: i18next.t('ONBOARDING.FIFTH_STEP.BUTTON_TITLE'),
    withImage: false,
    withStepper: false,
    backgroundImage: randomBackgroundImage(),
  },
};
