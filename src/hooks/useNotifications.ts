import WonderPush from 'react-native-wonderpush';
import { NavigationContainerRef } from '@react-navigation/native';
import { useCallback } from 'react';
import { useNotificationsStore } from '@scorescast/http-clients';
import { Navigators, Screens } from '../navigator/navigator';
import { useNewRelic } from '@scorescast/analytics';
import { NOTIFICATION_KEYS } from '@scorescast/formatters-constants';
export const useNotifications = () => {
  const { initializeNotificationsStore, setNotifications } =
    useNotificationsStore();
  const { trackError } = useNewRelic();

  const initWonderPush = useCallback(
    async (
      navigationRef: React.RefObject<NavigationContainerRef<any> | null>
    ) => {
      try {
        initializeNotificationsStore();
        WonderPush.setDelegate({
          onNotificationReceived: (notification: any) => {
            if (
              notification &&
              notification?.matchId &&
              navigationRef?.current
            ) {
              navigationRef.current?.navigate(Navigators.HomeNavigator, {
                screen: Screens.MatchScreen,
                params: {
                  matchId: notification.matchId,
                },
              });
            }
          },
          onNotificationOpened: (notification: any) => {
            if (
              notification &&
              notification?.matchId &&
              navigationRef?.current
            ) {
              navigationRef.current?.navigate(Navigators.HomeNavigator, {
                screen: Screens.MatchScreen,
                params: {
                  matchId: notification.matchId,
                },
              });
            }
          },
        });
      } catch (error) {
        trackError(error);
      }
    },
    []
  );

  const requestPermission = useCallback(async () => {
    await WonderPush.subscribeToNotifications();
    const status = await WonderPush.isSubscribedToNotifications();
    setNotifications(status);
    return status;
  }, []);

  const addPropertyValue = useCallback(
    async (key: string, value: string) => {
      try {
        const permission = await requestPermission();
        if (permission) {
          await WonderPush.addProperty(key, value);
        }
      } catch (error) {
        trackError(error);
      }
    },
    [requestPermission]
  );

  const removePropertyValue = useCallback(
    async (key: string, value: string) => {
      try {
        await WonderPush.removeProperty(key, value);
      } catch (error) {
        trackError(error);
      }
    },
    []
  );

  const clearProperty = useCallback(async (key: string) => {
    try {
      await WonderPush.unsetProperty(key);
    } catch (error) {
      if (__DEV__) {
        trackError(error);
      }
    }
  }, []);
  const isTeamSubscribedForNotificaitons = useCallback(
    async (teamId: string) => {
      try {
        const followedTeams =
          (await WonderPush.getPropertyValues(
            NOTIFICATION_KEYS.FOLLOWED_TEAMS
          )) || [];
        return followedTeams.includes(teamId);
      } catch (error) {
        trackError(error);
        return false;
      }
    },
    []
  );

  return {
    initWonderPush,
    requestPermission,
    addPropertyValue,
    removePropertyValue,
    clearProperty,
    isTeamSubscribedForNotificaitons,
  };
};
