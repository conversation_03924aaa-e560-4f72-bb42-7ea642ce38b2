import { useCallback } from 'react';
import { Platform } from 'react-native';
import { useUser } from '@scorescast/http-clients';
import { SocialLoginType } from '@scorescast/formatters-constants';

import { GoogleSignin } from '@react-native-google-signin/google-signin';

import {
  AccessToken,
  AuthenticationToken,
  LoginManager,
} from 'react-native-fbsdk-next';

import { appleAuth } from '@invertase/react-native-apple-authentication';

const useLogin = () => {
  const { loginUser, isLoginLoading } = useUser();

  const googleLogin = useCallback(async () => {
    try {
      await GoogleSignin.hasPlayServices();
      const response = await GoogleSignin.signIn();
      if (response?.data?.idToken) {
        loginUser({
          type: SocialLoginType.GOOGLE,
          token: response.data?.idToken,
        });
      }
    } catch (error) {
      console.warn(error);
    }
  }, [loginUser]);

  const facebookLogin = useCallback(async () => {
    try {
      await LoginManager.logInWithPermissions(['public_profile', 'email']);
      if (Platform.OS === 'ios') {
        AuthenticationToken.getAuthenticationTokenIOS().then((data) => {
          if (data?.authenticationToken) {
            loginUser({
              type: SocialLoginType.FACEBOOK,
              token: data?.authenticationToken,
            });
          }
        });
      } else {
        await AccessToken.getCurrentAccessToken().then((data) => {
          if (data?.accessToken) {
            loginUser({
              type: SocialLoginType.FACEBOOK,
              token: data?.accessToken.toString(),
            });
          }
        });
      }
    } catch (error) {
      console.error('Login failed with error:', error);
    }
  }, [loginUser]);

  const appleLogin = useCallback(async () => {
    try {
      if (Platform.OS === 'ios') {
        const appleAuthRequestResponse = await appleAuth.performRequest({
          requestedOperation: appleAuth.Operation.LOGIN,
          requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
        });

        const { identityToken } = appleAuthRequestResponse;

        if (identityToken) {
          const credentialState = await appleAuth.getCredentialStateForUser(
            appleAuthRequestResponse.user
          );
          if (credentialState === appleAuth.State.AUTHORIZED) {
            loginUser({
              type: SocialLoginType.APPLE,
              token: identityToken,
            });
          }
        }
      }
    } catch (error) {
      console.error('Login failed with error:', error);
    }
  }, [loginUser]);

  const login = useCallback(
    (type: typeof SocialLoginType) => {
      switch (type) {
        case SocialLoginType.GOOGLE:
          googleLogin();
          break;
        case SocialLoginType.FACEBOOK:
          facebookLogin();
          break;
        case SocialLoginType.APPLE:
          appleLogin();
          break;
        default:
          break;
      }
    },
    [appleLogin, facebookLogin, googleLogin]
  );

  return { login, isLoginLoading };
};

export default useLogin;
