import { useCallback } from 'react';
import appsFlyer from 'react-native-appsflyer';
import { emitEvent, Events } from '@scorescast/formatters-constants';

export const useAppsFlyer = () => {
  const generateShareTeamLink = useCallback((teamUniqueId: string) => {
    appsFlyer.setAppInviteOneLinkID('mlG0', () => {});

    return new Promise((resolve, reject) => {
      appsFlyer.generateInviteLink(
        {
          channel: 'User_invite',
          campaign: 'user_invite',
          customerID: 'not_defined',
          userParams: {
            af_sub1: teamUniqueId,
            af_dp: 'scorescast://',
          },
        },
        (link) => {
          resolve(link); // ✅ This resolves the Promise
        },
        (err) => {
          console.error('AppsFlyer link error:', err);
          reject(err); // ❌ This rejects it on error
        }
      );
    });
  }, []);

  const initAppsFlyer = useCallback(() => {
    const onDeepLinkCanceller = appsFlyer.onDeepLink((res) => {
      if (res?.deepLinkStatus !== 'NOT_FOUND') {
        if (res?.data?.af_sub1) {
          emitEvent(Events.SEARCH_TEAM_BY_ID, {
            teamUniqueId: res?.data?.af_sub1,
          });
        }
      }
    });
    appsFlyer.initSdk(
      {
        devKey: '9pZZJGv2xLpbfE9EegbXPb',
        isDebug: true,
        appId: '6743314820',
        onInstallConversionDataListener: true,
        onDeepLinkListener: true,
      },
      (result) => {
        console.log(result);
      },
      (error) => {
        console.error(error);
      }
    );
    return { onDeepLinkCanceller };
  }, []);

  return { initAppsFlyer, generateShareTeamLink };
};
