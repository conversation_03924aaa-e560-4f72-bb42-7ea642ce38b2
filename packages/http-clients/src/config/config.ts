export interface Endpoints {
  retrieveUserDetails: string;
  retrieveTeamPlayers: string;
  retrieveTeamDetailsByUniqueId: string;
  retrieveFollowedTeams: string;
  retrieveAgentTeams: string;
  retrieveTeamMatches: string;
  retrieveMatchDetails: string;
  followTeam: string;
  unfollowTeam: string;
  claimMatch: string;
  createEvent: string;
  addPlayerToTeam: string;
  deletePlayerFromTeam: string;
  deleteUserByEmail: string;
  reactEvent: string;
  retrieveAppConfig: string;
  retrieveAdminForClubs: string;
  retrieveAdminForTeams: string;
  updateClub: string;
  createClub: string;
  createTeam: string;
  updateTeam: string;
  deleteTeam: string;
  createMatch: string;
  retrieveUsersFollowingTeam: string;
  [key: string]: string;
}

interface ConfigType {
  protocol: string;
  domain: string;
  endpoints: Endpoints;
  getFullUrl: (endpoint: string, queryParams?: string) => string;
}

const Config: ConfigType = {
  protocol: 'https',
  domain: 'scorescast.app',
  endpoints: {
    retrieveAdminForClubs: '/api/user/retrieveAdminClubs',
    retrieveAdminForTeams: '/api/user/retrieveAdminTeams',
    retrieveUserDetails: '/api/user/retrieveUserDetails',
    retrieveTeamPlayers: '/api/manage/player/retrievePlayersForTeam',
    retrieveTeamDetailsByUniqueId: '/api/team/retrieveTeamDetailsByUniqueId',
    retrieveFollowedTeams: '/api/user/retrieveFollowedTeams',
    retrieveAgentTeams: '/api/user/retrieveAgentTeams',
    followTeam: '/api/user/followTeam',
    unfollowTeam: '/api/user/unfollowTeam',
    retrieveTeamMatches: '/api/match/retrieveTeamMatches',
    retrieveMatchDetails: '/api/match/retrieveMatchDetails',
    claimMatch: '/api/match/claimMatch',
    createTeam: '/api/manage/team/create',
    updateTeam: '/api/manage/team/update',
    createEvent: '/api/manage/event/create',
    deleteUserByEmail: '/api/manage/user/deleteByEmail',
    addPlayerToTeam: '/api/manage/player/create',
    deletePlayerFromTeam: '/api/manage/player/deleteById',
    reactEvent: '/api/user/addReaction',
    retrieveAppConfig: '/api/retrieveAppConfig',
    updateClub: '/api/manage/club/update',
    createClub: '/api/manage/club/create',
    deleteClub: '/api/manage/club/deleteById',
    retrieveAgentsOfTeam: '/api/manage/user/retrieveAgentsOfTeam',
    retrieveAdminsOfTeam: '/api/manage/user/retrieveAdminsOfTeam',
    deleteTeam: '/api/manage/team/deleteById',
    createMatch: '/api/manage/match/create',
    updateMatch: '/api/manage/match/update',
    deleteMatch: '/api/manage/match/deleteById',
    retrieveUsersFollowingTeam: '/api/team/retrieveUsersFollowingTeam',
  },
  getFullUrl(endpoint: string, queryParams?: string) {
    return `${this.protocol}://${this.domain}${endpoint}${queryParams ? queryParams : ''}`;
  },
};

export default Config;
