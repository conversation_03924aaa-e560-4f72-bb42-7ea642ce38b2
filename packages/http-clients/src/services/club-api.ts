import { i18next } from '@scorescast/translations';
import CustomError from '../errors/CustomError';
import Config from '../config/config';
import { getAPIHeaders } from '../helpers/getAPIHeaders';

export interface Club {
  id: number;
  name: string;
  crest: string;
  website?: string;
}

export interface UpdateClubData {
  id: number;
  name: string;
  crest: string | null;
}

export interface CreateClubData {
  name: string;
  crest: string | null;
  adminEmail: string;
}

export const ClubApi = {
  updateClub: async (clubData: UpdateClubData): Promise<any> => {
    const url = Config.getFullUrl(Config.endpoints.updateClub);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        ...(await getAPIHeaders()),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(clubData),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.UPDATE_CLUB'),
        response.status
      );
    }
    return response.json();
  },

  createClub: async (clubData: CreateClubData): Promise<any> => {
    const url = Config.getFullUrl(Config.endpoints.createClub);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        ...(await getAPIHeaders()),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(clubData),
    });

    if (!response.ok) {
      try {
        const errorData = await response.json();

        if (errorData.errorCode === 'CLUB_NAME_ALEADY_EXIST') {
          throw new CustomError(
            i18next.t('API_ERRORS.CLUB_NAME_ALEADY_EXIST'),
            response.status
          );
        }
      } catch (e) {
        console.error(e);
      }

      throw new CustomError(
        i18next.t('API_ERRORS.CREATE_CLUB'),
        response.status
      );
    }

    return response.json();
  },

  deleteClub: async (clubId: number): Promise<any> => {
    const url = `${Config.getFullUrl(Config.endpoints.deleteClub)}?id=${clubId}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        ...(await getAPIHeaders()),
      },
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.DELETE_CLUB'),
        response.status
      );
    }

    return response.json();
  },

  retrieveAgentsOfTeam: async (teamId: number): Promise<any> => {
    const url = `${Config.getFullUrl(Config.endpoints.retrieveAgentsOfTeam)}?teamId=${teamId}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        ...(await getAPIHeaders()),
      },
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.RETRIEVE_TEAM_AGENTS'),
        response.status
      );
    }
    return response.json()
  },

  retrieveAdminsOfTeam: async (teamId: number): Promise<any> => {
    const url = `${Config.getFullUrl(Config.endpoints['retrieveAdminsOfTeam'])}?teamId=${teamId}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        ...(await getAPIHeaders()),
      },
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.RETRIEVE_TEAM_ADMINS'),
        response.status
      );
    }

    return response.json();
  },
};
