import { SocialLoginType } from '@scorescast/formatters-constants';
import { i18next } from '@scorescast/translations';
import CustomError from '../errors/CustomError';
import Config from '../config/config';
import { getAPIHeaders } from '../helpers/getAPIHeaders';

export const UserApi = {
  login: async (type: typeof SocialLoginType, token: string): Promise<any> => {
    const url = Config.getFullUrl(Config.endpoints.retrieveUserDetails);
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Provider: type,
        Authorization: `${token}`,
        ...(await getAPIHeaders(false)),
      },
    });
    if (!response.ok) {
      throw new CustomError(i18next.t('API_ERRORS.LOGIN'), response.status);
    }
    const data = await response.json();
    return { user: data, type, token };
  },

  fetchUser: async (): Promise<any[]> => {
    const url = Config.getFullUrl(Config.endpoints.retrieveUserDetails);
    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });
    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.FETCH_USER'),
        response.status
      );
    }
    return response.json();
  },

  deleteUserByEmail: async (email: string): Promise<any> => {
    const params = `?email=${encodeURIComponent(email)}`;
    const url = Config.getFullUrl(Config.endpoints.deleteUserByEmail, params);

    const response = await fetch(url, {
      method: 'POST',
      headers: await getAPIHeaders(),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.FETCH_USER'),
        response.status
      );
    }
    return response.json();
  },

  reactEvent: async (eventId: number, reactionType: string): Promise<any> => {
    const params = `?eventId=${eventId}&reaction=${reactionType}`;
    const url = Config.getFullUrl(Config.endpoints.reactEvent, params);

    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.FETCH_USER'),
        response.status
      );
    }
    return response.json();
  },

  retrieveAppConfig: async (): Promise<any[]> => {
    const url = Config.getFullUrl(Config.endpoints.retrieveAppConfig);
    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });
    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.FETCH_USER'),
        response.status
      );
    }
    return response.json();
  },

  retrieveAdminForClubs: async (): Promise<any[]> => {
    const url = Config.getFullUrl(Config.endpoints.retrieveAdminForClubs);
    const response = await fetch(url, {
      method: 'POST',
      headers: await getAPIHeaders(),
    });
    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.FETCH_USER'),
        response.status
      );
    }

    return response.json();
  },

  retrieveAdminForTeams: async (): Promise<any[]> => {
    const url = Config.getFullUrl(Config.endpoints.retrieveAdminForTeams);
    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });
    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.FETCH_USER'),
        response.status
      );
    }
    
    return response.json();
  },
};
