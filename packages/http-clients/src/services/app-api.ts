import { i18next } from '@scorescast/translations';
import CustomError from '../errors/CustomError';
import Config from '../config/config';
import { getAPIHeaders } from '../helpers/getAPIHeaders';

export const ApplicationApi = {
  retrieveAppConfig: async (): Promise<any[]> => {
    const url = Config.getFullUrl(Config.endpoints.retrieveAppConfig);
    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });
    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.FETCH_USER'),
        response.status
      );
    }
    return response.json();
  },
};
