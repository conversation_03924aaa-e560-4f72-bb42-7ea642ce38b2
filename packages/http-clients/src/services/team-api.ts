import { i18next } from '@scorescast/translations';
import { TeamsResponse } from '@scorescast/formatters-constants';
import CustomError from '../errors/CustomError';
import Config from '../config/config';
import { getAPIHeaders } from '../helpers/getAPIHeaders';
import { Player } from './agent-api';

export interface CreateTeamData {
  name: string;
  shortName: string;
  image: string;
  clubName: string;
  ageGroup: string;
}

export interface UpdateTeamData {
  id: number;
  name: string;
  image: string;
  ageGroup: string;
  shortName: string;
}

export const TeamApi = {
  createTeam: async (teamData: CreateTeamData): Promise<any> => {
    const url = Config.getFullUrl(Config.endpoints.createTeam);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        ...(await getAPIHeaders()),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(teamData),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.CREATE_TEAM'),
        response.status
      );
    }

    return response.json();
  },

  updateTeam: async (teamData: UpdateTeamData): Promise<any> => {
    const params = `?id=${teamData.id}&name=${encodeURIComponent(teamData.name)}&image=${encodeURIComponent(teamData.image)}&ageGroup=${teamData.ageGroup}&shortName=${teamData.shortName}`;
    const url = Config.getFullUrl(Config.endpoints.updateTeam, params);

    const response = await fetch(url, {
      method: 'POST',
      headers: await getAPIHeaders(),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.UPDATE_TEAM'),
        response.status
      );
    }

    return response.json();
  },

  retrieveTeamById: async (teamUniqueId: string): Promise<any[]> => {
    const params = `?teamUniqueId=${teamUniqueId}`;
    const url = Config.getFullUrl(
      Config.endpoints.retrieveTeamDetailsByUniqueId,
      params
    );

    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });
    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.RETRIEVE_TEAM'),
        response.status
      );
    }
    return response.json();
  },

  retrieveFollowedTeams: async (): Promise<typeof TeamsResponse> => {
    const url = Config.getFullUrl(Config.endpoints.retrieveFollowedTeams);

    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.FETCH_FOLLOWED_TEAMS'),
        response.status
      );
    }

    return response.json();
  },

  retrieveTeamPlayers: async (
    id: number | null
  ): Promise<typeof TeamsResponse> => {
    const params = `?id=${id}`;
    const url = Config.getFullUrl(Config.endpoints.retrieveTeamPlayers, params);

    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.RETRIEVE_TEAM'),
        response.status
      );
    }
    return response.json();
  },

  retrieveAgentTeams: async (): Promise<typeof TeamsResponse> => {
    const url = Config.getFullUrl(Config.endpoints.retrieveAgentTeams);

    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });
    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.FETCH_FOLLOWED_TEAMS'),
        response.status
      );
    }

    return response.json();
  },

  followTeam: async (teamUniqueId: string): Promise<any[]> => {
    const params = `?teamUniqueId=${teamUniqueId}`;
    const url = Config.getFullUrl(Config.endpoints.followTeam, params);

    const response = await fetch(url, {
      method: 'POST',
      headers: await getAPIHeaders(),
    });
    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.FOLLOW_TEAM'),
        response.status
      );
    }
    return response.json();
  },

  unfollowTeam: async (teamUniqueId: string): Promise<any[]> => {
    const params = `?teamUniqueId=${teamUniqueId}`;
    const url = Config.getFullUrl(Config.endpoints.unfollowTeam, params);

    const response = await fetch(url, {
      method: 'POST',
      headers: await getAPIHeaders(),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.FOLLOW_TEAM'),
        response.status
      );
    }
    return response.json();
  },

  retrieveUsersFollowingTeam: async (teamId: number): Promise<any> => {
    const params = `?teamId=${teamId}`;
    const url = Config.getFullUrl(
      Config.endpoints.retrieveUsersFollowingTeam,
      params
    );

    console.log('🔍 [TeamAPI] Fetching team followers:', {
      teamId,
      url,
      endpoint: Config.endpoints.retrieveUsersFollowingTeam,
      params,
    });

    try {
      const headers = await getAPIHeaders();
      console.log('📤 [TeamAPI] Request headers:', headers);

      const response = await fetch(url, {
        method: 'GET',
        headers,
      });

      console.log('📥 [TeamAPI] Response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries()),
      });

      if (!response.ok) {
        let errorBody;
        try {
          errorBody = await response.text();
          console.error('❌ [TeamAPI] Error response body:', errorBody);
        } catch (e) {
          console.error('❌ [TeamAPI] Could not read error response body:', e);
        }

        throw new CustomError(
          i18next.t('API_ERRORS.FETCH_TEAM_FOLLOWERS'),
          response.status
        );
      }

      const responseData = await response.json();
      console.log('✅ [TeamAPI] Success response data:', responseData);

      return responseData;
    } catch (error) {
      console.error('💥 [TeamAPI] Request failed:', error);
      throw error;
    }
  },

  deleteTeam: async (teamId: number): Promise<any> => {
    const params = `?id=${teamId}`;
    const url = Config.getFullUrl(Config.endpoints.deleteTeam, params);

    const response = await fetch(url, {
      method: 'POST',
      headers: await getAPIHeaders(),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.DELETE_TEAM'),
        response.status
      );
    }
    return response.json();
  },

  addPlayerToTeam: async (player: Player): Promise<any> => {
    const url = Config.getFullUrl(Config.endpoints.addPlayerToTeam);

    const response = await fetch(url, {
      method: 'POST',
      headers: await getAPIHeaders(),
      body: JSON.stringify(player),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.ADD_PLAYER'),
        response.status
      );
    }
    return response.json();
  },

  deletePlayerFromTeam: async (player: { id: number }): Promise<any> => {
    const params = `?id=${player.id}`;
    const url = Config.getFullUrl(
      Config.endpoints.deletePlayerFromTeam,
      params
    );

    const response = await fetch(url, {
      method: 'POST',
      headers: await getAPIHeaders(),
      body: JSON.stringify({}),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.DELETE_PLAYER'),
        response.status
      );
    }
    return response.json();
  },

  // Match management functions
  createMatch: async (matchData: CreateMatchData): Promise<any> => {
    const url = Config.getFullUrl(Config.endpoints.createMatch);

    const response = await fetch(url, {
      method: 'POST',
      headers: await getAPIHeaders(),
      body: JSON.stringify(matchData),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.CREATE_MATCH'),
        response.status
      );
    }
    return response.json();
  },
};

// Match management interfaces
export interface CreateMatchData {
  usTeamName: string;
  themTeamName: string;
  location: string;
  matchType: string;
  matchSetup: string;
  description: string;
  matchTime: number;
  teamId: number;
}
