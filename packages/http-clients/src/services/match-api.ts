import { i18next } from '@scorescast/translations';
import CustomError from '../errors/CustomError';
import Config from '../config/config';
import { TeamMatchesResponse } from '@scorescast/formatters-constants';
import { getAPIHeaders } from '../helpers/getAPIHeaders';

export const MatchApi = {
  retrieveTeamMatches: async (
    teamId: string
  ): Promise<typeof TeamMatchesResponse> => {
    const params = `?uniqueId=${teamId}&filters=all`;
    const url = Config.getFullUrl(Config.endpoints.retrieveTeamMatches, params);

    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });
    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.RETRIEVE_TEAM'),
        response.status
      );
    }
    return response.json();
  },

  retrieveMatchDetails: async (
    matchId: string
  ): Promise<typeof TeamMatchesResponse> => {
    const params = `?id=${matchId}`;
    const url = Config.getFullUrl(
      Config.endpoints.retrieveMatchDetails,
      params
    );

    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.RETRIEVE_MATCH_DETAILS'),
        response.status
      );
    }

    return response.json();
  },

  deleteMatch: async (matchId: string): Promise<any> => {
    const params = `?id=${matchId}`;
    const url = Config.getFullUrl(Config.endpoints.deleteMatch, params);

    const response = await fetch(url, {
      method: 'POST',
      headers: await getAPIHeaders(),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.DELETE_MATCH'),
        response.status
      );
    }

    return response.json();
  },

  updateMatch: async (matchId: string, matchData: any): Promise<any> => {
    const url = Config.getFullUrl(Config.endpoints['updateMatch']);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        ...(await getAPIHeaders()),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...matchData, id: matchId }),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.UPDATE_MATCH'),
        response.status
      );
    }

    return response.json();
  },
};
