export const ImgbbApi = {
  uploadImage: async (image: string) => {
    const url =
      'https://api.imgbb.com/1/upload?key=9e0f48b6a238f8e899632f1c62abba1f';
    const formData = new FormData();
    formData.append('image', image);

    const response = await fetch(url, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Failed to upload image');
    }

    return response.json();
  },
};
