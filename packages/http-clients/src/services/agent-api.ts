import { i18next } from '@scorescast/translations';
import CustomError from '../errors/CustomError';
import Config from '../config/config';
import { getAPIHeaders } from '../helpers/getAPIHeaders';

export interface AgentEvent {
  matchId: null | string;
  playerId?: null | string;
  who?: null | string;
  action?: string;
  outcome?: string;
  period?: string;
  timestamp?: number;
}

export interface Player {
  id: number;
  jerseyName: string;
  number: string;
  firstName: string;
  lastName: string;
  federationId: string;
}

export enum AgentEventSideSelection {
  US = 'US',
  THEM = 'THEM',
}

export const AgentApi = {
  claimMatch: async (matchId: string): Promise<any[]> => {
    const params = `?matchId=${matchId}`;
    const url = Config.getFullUrl(Config.endpoints.claimMatch, params);

    const response = await fetch(url, {
      method: 'GET',
      headers: await getAPIHeaders(),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.RETRIEVE_TEAM'),
        response.status
      );
    }
    return response.json();
  },
  createEvent: async (event: AgentEvent): Promise<any[]> => {
    const url = Config.getFullUrl(Config.endpoints.createEvent);

    const response = await fetch(url, {
      method: 'POST',
      headers: await getAPIHeaders(),
      body: JSON.stringify(event),
    });

    if (!response.ok) {
      throw new CustomError(
        i18next.t('API_ERRORS.RETRIEVE_TEAM'),
        response.status
      );
    }
    return response.json();
  },
};
