import { ToasterType } from '@scorescast/formatters-constants';
import { i18next } from '@scorescast/translations';
import { useEffect } from 'react';
import { useAuthStore } from '../stores/useAuthStore';
import { useToasterStore } from '../stores/useToasterStore';
import CustomError from '../errors/CustomError';

export const useErrorNotification = (
  isError: boolean,
  error: CustomError | null
) => {
  const { clearToken } = useAuthStore();
  const { addToaster } = useToasterStore();

  useEffect(() => {
    if (isError && error instanceof CustomError && error?.status === 401) {
      addToaster({
        type: ToasterType.ERROR,
        title: i18next.t('API_ERRORS.TITLE'),
        description: i18next.t('API_ERRORS.LOGOUT'),
      });
      clearToken();
      return;
    }
    if (error instanceof CustomError) {
      addToaster({
        type: ToasterType.ERROR,
        title: i18next.t('API_ERRORS.TITLE'),
        description: error?.message,
      });
    }
  }, [isError]);
};
