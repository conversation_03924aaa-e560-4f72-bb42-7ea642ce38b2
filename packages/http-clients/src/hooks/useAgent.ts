import { useMutation } from '@tanstack/react-query';
import { useDataFetching } from './useDataFetching';
import CustomError from '../errors/CustomError';
import { AgentApi, AgentEvent } from '../services/agent-api';
import { MatchEventTypes } from '@scorescast/formatters-constants/src/constants/match';

export const useAgent = () => {
  const {
    data: claimResponse,
    mutateAsync: claimMatch,
    isPending: isRequestPending,
    error: errorClaimMatch,
    isError: isClaimMatchError,
  } = useMutation({
    mutationFn: async ({ matchId }: { matchId: string }): Promise<any> => {
      return await AgentApi.claimMatch(matchId);
    },
  });

  useDataFetching({
    isError: isClaimMatchError,
    error: errorClaimMatch as CustomError,
  });


  const {
    data: createEventResponse,
    mutateAsync: _createEvent,
    isPending: isEventPending,
  } = useMutation({
    mutationFn: async (event: AgentEvent): Promise<any> => {
      return await Agent<PERSON><PERSON>.createEvent(event as AgentEvent);
    },
  });

  const createEvent = async (
    eventName: MatchEventTypes,
    partialEvent: AgentEvent,
    cb: () => void
  ): Promise<any> => {
    if (!EventTypesMap[eventName]) {
      console.error(`eventName: ${eventName} does not exist on EventTypesMap`);
      return;
    }
    if (partialEvent.timestamp) {
      EventTypesMap[eventName]!.timestamp = partialEvent.timestamp;
    } else {
      EventTypesMap[eventName]!.timestamp = Date.now();
    }
    _createEvent(
      { ...EventTypesMap[eventName], ...partialEvent },
      {
        onSettled: () => {
          cb();
        },
      }
    );
  };

  return {
    createEvent,
    createEventResponse,
    isEventPending,
    claimMatch,
    claimResponse,
    isRequestPending,
    isClaimMatchError,
    errorClaimMatch,
  };
};

type EventTypesMapType = Partial<Record<MatchEventTypes, AgentEvent>>;

const EventTypesMap: EventTypesMapType = {
  [MatchEventTypes['KICKOFF']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'P1',
    period: 'P1',
    timestamp: 0,
  },
  [MatchEventTypes['P1_END']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'HT',
    period: 'P1',
    timestamp: 0,
  },
  [MatchEventTypes['P1_END__1_PERIODS']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'FT',
    period: 'P1',
    timestamp: 0,
  },
  [MatchEventTypes['P1_END__4_PERIODS']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'P1_BREAK',
    period: 'P1',
    timestamp: 0,
  },
  [MatchEventTypes['P2_END__4_PERIODS']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'HT',
    period: 'P2',
    timestamp: 0,
  },
  [MatchEventTypes['P2_END']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'FT',
    period: 'P2',
    timestamp: 0,
  },
  [MatchEventTypes['P3_END']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'P3_BREAK',
    period: 'P3',
    timestamp: 0,
  },
  [MatchEventTypes['P4_END']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'FT',
    period: 'P4',
    timestamp: 0,
  },
  [MatchEventTypes['P2_KICKOFF']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'P2',
    period: 'P2',
    timestamp: 0,
  },
  [MatchEventTypes['P3_KICKOFF']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'P3',
    period: 'P3',
    timestamp: 0,
  },
  [MatchEventTypes['P4_KICKOFF']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'P4',
    period: 'P4',
    timestamp: 0,
  },
  [MatchEventTypes['END_GAME']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'END',
    period: 'END',
    timestamp: 0,
  },
  [MatchEventTypes['ET_KICKOFF__1_PERIODS']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'ET',
    period: 'ET',
    timestamp: 0,
  },
  [MatchEventTypes['ET_KICKOFF']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'ET1',
    period: 'ET1',
    timestamp: 0,
  },
  [MatchEventTypes['ET_END']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'AET',
    period: 'ET',
    timestamp: 0,
  },
  [MatchEventTypes['ET1_END']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'ETHT',
    period: 'ET1',
    timestamp: 0,
  },
  [MatchEventTypes['ET2_KICKOFF']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'ET2',
    period: 'ET2',
    timestamp: 0,
  },
  [MatchEventTypes['ET2_END']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'AET',
    period: 'ET2',
    timestamp: 0,
  },
  [MatchEventTypes['PENS_START']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: '',
    outcome: 'PENS',
    period: 'PENS',
    timestamp: 0,
  },
  [MatchEventTypes['FREEKICK']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: MatchEventTypes['FREEKICK'],
    outcome: 'GOAL',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['SHOT']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: MatchEventTypes['SHOT'],
    outcome: 'GOAL',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['PENALTY_MISS']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: 'PENALTY',
    outcome: 'MISS_PENALTY',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['PENALTY_SAVE']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: 'PENALTY',
    outcome: 'SAVED_PENALTY',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['PENALTY_GOAL']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: 'PENALTY',
    outcome: 'GOAL',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['PENALTY']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: MatchEventTypes['PENALTY'],
    outcome: 'GOAL',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['OWN_GOAL']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: MatchEventTypes['OWN_GOAL'],
    outcome: 'GOAL',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['FOUL_FREEKICK']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: 'FOUL',
    outcome: 'FREEKICK',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['FOUL_PENALTY']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: 'FOUL',
    outcome: 'PENALTY',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['YELLOW_CARD']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: 'FOUL',
    outcome: 'YELLOW_CARD',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['SECOND_YELLOW_CARD']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: 'FOUL',
    outcome: 'SECOND_YELLOW_CARD',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['RED_CARD']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: 'FOUL',
    outcome: 'RED_CARD',
    period: '',
    timestamp: 0,
  },
  [MatchEventTypes['CARDS']]: {
    matchId: null,
    playerId: null,
    who: null,
    action: 'DISSENT',
    outcome: '',
    period: '',
    timestamp: 0,
  },
};
