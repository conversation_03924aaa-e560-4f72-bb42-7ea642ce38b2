import { useQuery } from '@tanstack/react-query';
import { useDataFetching } from './useDataFetching';
import { MatchApi } from '../services/match-api';
import CustomError from '../errors/CustomError';
import { MatchPossibleStatus } from '@scorescast/formatters-constants';

export const useLiveMatch = (
  matchId: string,
  status: typeof MatchPossibleStatus
) => {
  const {
    data: match,
    isLoading: isMatchLoading,
    refetch: refetchMatch,
    error: errorMatch,
    isError: isErrorMatch,
  } = useQuery({
    queryKey: ['match', matchId],
    queryFn: async () => {
      if (!matchId) return;
      return await MatchApi.retrieveMatchDetails(matchId);
    },
    retry: 0,
    enabled: !!matchId,
    refetchInterval: () => {
      if (
        ![MatchPossibleStatus.NS, MatchPossibleStatus.END].includes(status) ||
        (match?.data?.claimStatus === 'CAN_CLAIM' &&
          status === MatchPossibleStatus.NS)
      ) {
        return 5000;
      }
      return 0;
    },
  });

  useDataFetching({
    isError: isErrorMatch,
    error: errorMatch as CustomError,
  });

  return {
    match,
    isMatchLoading,
    refetchMatch,
  };
};
