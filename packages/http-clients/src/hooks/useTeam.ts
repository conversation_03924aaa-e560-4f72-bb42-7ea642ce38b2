import { useMutation, useQuery } from '@tanstack/react-query';
import { ToasterType } from '@scorescast/formatters-constants';
import { i18next } from '@scorescast/translations';
import { useDataFetching } from './useDataFetching';
import {
  TeamApi,
  CreateTeamData,
  UpdateTeamData,
  CreateMatchData,
} from '../services/team-api';
import { useToasterStore } from '../stores/useToasterStore';
import { useUser } from './useUser';
import CustomError from '../errors/CustomError';
import { Player } from '../services/agent-api';

export const UseTeamPlayers = (id: number) => {
  const {
    data: teamPlayersRetrieve,
    isLoading: isTeamPlayersLoading,
    refetch: refetchTeamPlayers,
    error: errorTeamPlayers,
    isError: isTeamPlayersError,
  } = useQuery({
    queryKey: ['teamPlayersRetrieve', id],
    queryFn: () => TeamApi.retrieveTeamPlayers(id),
    retry: 0,
    enabled: !!id,
    staleTime: Infinity,
    // refetchInterval: 10000,
  });
  useDataFetching({
    isError: isTeamPlayersError,
    error: errorTeamPlayers as any,
  });

  return {
    teamPlayersRetrieve,
    isTeamPlayersLoading,
    refetchTeamPlayers,
  };
};

export const UseTeamFollowers = (teamId: number) => {
  const {
    data: teamFollowers,
    isLoading: isTeamFollowersLoading,
    refetch: refetchTeamFollowers,
    error: errorTeamFollowers,
    isError: isTeamFollowersError,
  } = useQuery({
    queryKey: ['teamFollowers', teamId],
    queryFn: () => TeamApi.retrieveUsersFollowingTeam(teamId),
    retry: 0,
    enabled: !!teamId,
    staleTime: Infinity,
  });

  useDataFetching({
    isError: isTeamFollowersError,
    error: errorTeamFollowers as any,
  });

  return {
    teamFollowers,
    isTeamFollowersLoading,
    refetchTeamFollowers,
  };
};

export const useTeam = (
  retrieveAgentTeams: boolean = false,
  isLoggedIn: boolean = true
) => {
  const { addToaster } = useToasterStore();
  const {
    data: team,
    mutateAsync: retrieveTeamById,
    isPending: isTeamLoading,
    error: loadingTeamError,
    isError: isLoadingTeamError,
  } = useMutation({
    mutationFn: async ({
      teamUniqueId,
    }: {
      teamUniqueId: string;
    }): Promise<any> => {
      return await TeamApi.retrieveTeamById(teamUniqueId);
    },
  });

  const {
    data: followedTeams,
    isLoading: isFollowedTeamsLoading,
    refetch: refetchFollowedTeams,
    error: errorFollowedTeams,
    isError: isErrorFollowedTeams,
  } = useQuery({
    queryKey: ['FOLLOWED_TEAMS'],
    queryFn: TeamApi.retrieveFollowedTeams,
    retry: 0,
    enabled: isLoggedIn,
  });

  const {
    data: agentTeams,
    isLoading: isAgentTeamsLoading,
    refetch: refetchAgentTeams,
    error: errorAgentTeams,
    isError: isErrorAgentTeams,
  } = useQuery({
    queryKey: ['AGENT_TEAMS'],
    queryFn: TeamApi.retrieveAgentTeams,
    retry: 0,
    enabled: retrieveAgentTeams,
    refetchInterval: 10000,
  });

  const {
    mutateAsync: followTeam,
    isPending: isFollowTeamLoading,
    error: followTeamError,
    isError: isFollowTeamError,
  } = useMutation({
    mutationFn: async ({
      teamUniqueId,
    }: {
      teamUniqueId: string;
    }): Promise<any> => {
      return await TeamApi.followTeam(teamUniqueId);
    },
    onSuccess: () => {
      addToaster({
        type: ToasterType.SUCCESS,
        title: i18next.t('API_SUCCESS.FOLLOW_TEAM_TITLE'),
        description: i18next.t('API_SUCCESS.FOLLOW_TEAM_MESSAGE'),
      });
    },
  });

  const {
    data: unfollowTeamResponse,
    mutateAsync: unfollowTeam,
    isPending: isUnfollowTeamLoading,
    error: unfollowTeamError,
    isError: isUnfollowTeamError,
  } = useMutation({
    mutationFn: async ({
      teamUniqueId,
    }: {
      teamUniqueId: string;
    }): Promise<any> => {
      return await TeamApi.unfollowTeam(teamUniqueId);
    },
  });

  useDataFetching({
    isError: isLoadingTeamError,
    error: loadingTeamError as any,
  });
  useDataFetching({
    isError: isErrorFollowedTeams,
    error: errorFollowedTeams as any,
  });
  useDataFetching({
    isError: isFollowTeamError,
    error: followTeamError as any,
  });

  useDataFetching({
    isError: isErrorAgentTeams,
    error: errorAgentTeams as any,
  });

  useDataFetching({
    isError: isUnfollowTeamError,
    error: unfollowTeamError as any,
  });

  const { refetchRetrieveAdminForTeams } = useUser();

  const {
    mutateAsync: createTeam,
    isPending: isCreateTeamLoading,
    error: createTeamError,
    isError: isCreateTeamError,
  } = useMutation({
    mutationFn: async (teamData: CreateTeamData): Promise<any> => {
      return await TeamApi.createTeam(teamData);
    },
    onSuccess: () => {
      addToaster({
        type: ToasterType.SUCCESS,
        title: i18next.t('API_SUCCESS.CREATE_TEAM_TITLE'),
        description: i18next.t('API_SUCCESS.CREATE_TEAM_MESSAGE'),
      });
      refetchRetrieveAdminForTeams();
    },
  });

  const {
    mutateAsync: updateTeam,
    isPending: isUpdateTeamLoading,
    error: updateTeamError,
    isError: isUpdateTeamError,
  } = useMutation({
    mutationFn: async (teamData: UpdateTeamData): Promise<any> => {
      return await TeamApi.updateTeam(teamData);
    },
    onSuccess: () => {
      addToaster({
        type: ToasterType.SUCCESS,
        title: i18next.t('API_SUCCESS.UPDATE_TEAM_TITLE'),
        description: i18next.t('API_SUCCESS.UPDATE_TEAM_MESSAGE'),
      });
      refetchRetrieveAdminForTeams();
    },
  });

  const {
    mutateAsync: deleteTeamMutation,
    isPending: isDeleteTeamLoading,
    error: deleteTeamError,
    isError: isDeleteTeamError,
  } = useMutation({
    mutationFn: async (teamId: number): Promise<any> => {
      return await TeamApi.deleteTeam(teamId);
    },
    onSuccess: () => {
      addToaster({
        type: ToasterType.SUCCESS,
        title: i18next.t('API_SUCCESS.DELETE_TEAM_TITLE'),
        description: i18next.t('API_SUCCESS.DELETE_TEAM_MESSAGE'),
      });
      refetchRetrieveAdminForTeams();
    },
  });

  const deleteTeam = async (
    teamId: number,
    options?: { onSuccess?: () => void }
  ) => {
    try {
      await deleteTeamMutation(teamId);
      if (options?.onSuccess) {
        options.onSuccess();
      }
    } catch (error) {
      console.error('Error deleting team:', error);
    }
  };

  useDataFetching({
    isError: isCreateTeamError,
    error: createTeamError as any,
  });

  useDataFetching({
    isError: isUpdateTeamError,
    error: updateTeamError as any,
  });

  useDataFetching({
    isError: isDeleteTeamError,
    error: deleteTeamError as any,
  });

  const {
    mutateAsync: addPlayerToTeam,
    isPending: isAddPlayerLoading,
    error: addPlayerError,
    isError: isAddPlayerError,
  } = useMutation({
    mutationFn: async (player: Player): Promise<any> => {
      return await TeamApi.addPlayerToTeam(player);
    },
    onSuccess: () => {
      addToaster({
        type: ToasterType.SUCCESS,
        title: i18next.t('API_SUCCESS.ADD_PLAYER_TITLE'),
        description: i18next.t('API_SUCCESS.ADD_PLAYER_MESSAGE'),
      });
    },
  });

  const {
    mutateAsync: deletePlayerFromTeam,
    isPending: isDeletePlayerLoading,
    error: deletePlayerError,
    isError: isDeletePlayerError,
  } = useMutation({
    mutationFn: async (player: { id: number }): Promise<any> => {
      return await TeamApi.deletePlayerFromTeam(player);
    },
    onSuccess: () => {
      addToaster({
        type: ToasterType.SUCCESS,
        title: i18next.t('API_SUCCESS.DELETE_PLAYER_TITLE'),
        description: i18next.t('API_SUCCESS.DELETE_PLAYER_MESSAGE'),
      });
    },
  });

  useDataFetching({
    isError: isAddPlayerError,
    error: addPlayerError as CustomError,
  });

  useDataFetching({
    isError: isDeletePlayerError,
    error: deletePlayerError as CustomError,
  });

  // Create match mutation
  const createMatchMutation = useCreateMatch();

  return {
    team,
    isTeamLoading,
    loadingTeamError,
    retrieveTeamById,
    followedTeams,
    isFollowedTeamsLoading,
    refetchFollowedTeams,
    followTeam,
    isFollowTeamLoading,
    agentTeams,
    isAgentTeamsLoading,
    refetchAgentTeams,
    unfollowTeam,
    isUnfollowTeamLoading,
    unfollowTeamResponse,
    createTeam,
    isCreateTeamLoading,
    updateTeam,
    isUpdateTeamLoading,
    deleteTeam,
    isDeleteTeamLoading,
    addPlayerToTeam,
    isAddPlayerLoading,
    addPlayerError,
    isAddPlayerError,
    deletePlayerFromTeam,
    isDeletePlayerLoading,
    deletePlayerError,
    isDeletePlayerError,

    // Match management mutations
    createMatch: createMatchMutation.mutateAsync,
    isCreateMatchLoading: createMatchMutation.isPending,
    createMatchError: createMatchMutation.error,
    isCreateMatchError: createMatchMutation.isError,
  };
};

// Create match hook
const useCreateMatch = () => {
  const { addToaster } = useToasterStore();

  return useMutation({
    mutationFn: (params: {
      matchData: CreateMatchData;
      options?: { onSuccess?: () => void };
    }) => TeamApi.createMatch(params.matchData),
    onSuccess: (_, params) => {
      addToaster({
        type: ToasterType.SUCCESS,
        title: i18next.t('API_SUCCESS.TITLE'),
        description: i18next.t('ADMIN.EDIT_MATCH.CREATE_SUCCESS'),
      });

      // Call the onSuccess callback if provided
      if (params.options?.onSuccess) {
        params.options.onSuccess();
      }
    },
    onError: (error: CustomError) => {
      addToaster({
        type: ToasterType.ERROR,
        title: i18next.t('API_ERRORS.TITLE'),
        description: error.message,
      });
    },
  });
};
