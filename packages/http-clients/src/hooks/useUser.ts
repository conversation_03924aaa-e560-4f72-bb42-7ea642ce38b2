import { useMutation, useQuery } from '@tanstack/react-query';
import { useDataFetching } from './useDataFetching';
import { UserApi } from '../services/user-api';
import { SocialLoginType } from '@scorescast/formatters-constants';
import CustomError from '../errors/CustomError';
import { useAuthStore } from '../stores/useAuthStore';

export const useUser = () => {
  const { setToken, setUser, clearToken } = useAuthStore();

  const {
    mutateAsync: loginUser,
    isPending: isLoginLoading,
    error: userError,
    isError: isUserError,
  } = useMutation({
    mutationFn: async ({
      type,
      token,
    }: {
      type: typeof SocialLoginType;
      token: string;
    }): Promise<any> => {
      return await UserApi.login(type, token);
    },
    onSuccess: (response) => {
      if (response?.user?.data) {
        setToken(response?.type, response?.token, response?.user?.data);
      }
    },
    onError: (error: CustomError) => {
      console.error('loginUser error', error);
    },
  });

  const {
    mutateAsync: fetchUser,
    error: fetchUserError,
    isError: isFetchUserError,
    isPending: isFetchUserLoading,
  } = useMutation({
    mutationFn: async (): Promise<any> => {
      return await UserApi.fetchUser();
    },
    onSuccess: (response: any) => {
      if (!response?.error) {
        setUser(response?.data);
      }
    },
  });

  const {
    mutateAsync: deleteUser,
    isPending: isDeleteUserLoading,
    error: deleteUserError,
    isError: isDeleteUserError,
  } = useMutation({
    mutationFn: async ({ email }: { email: string }): Promise<any> => {
      return await UserApi.deleteUserByEmail(email);
    },
    onSuccess: (response: any) => {
      if (!response?.error) {
        clearToken();
      }
    },
    onError: (error: CustomError) => {
      console.error('deleteUser error', error);
    },
  });

  const {
    mutateAsync: reactEvent,
    isPending: isReactEventLoading,
    error: reactEventError,
    isError: isReactEventError,
  } = useMutation({
    mutationFn: async ({
      eventId,
      reactionType,
    }: {
      eventId: number;
      reactionType: string;
    }): Promise<any> => {
      return await UserApi.reactEvent(eventId, reactionType);
    },
    onSuccess: () => {},
    onError: (error: CustomError) => {
      console.error('loginUser error', error);
    },
  });

  useDataFetching({ isError: isUserError, error: userError as CustomError });
  useDataFetching({
    isError: isFetchUserError,
    error: fetchUserError as CustomError,
  });
  useDataFetching({
    isError: isDeleteUserError,
    error: deleteUserError as CustomError,
  });
  useDataFetching({
    isError: isReactEventError,
    error: reactEventError as CustomError,
  });

  const {
    data: adminForClubs,
    isLoading: isRetrieveAdminForClubsLoading,
    refetch: refetchRetrieveAdminForClubs,
    error: errorRetrieveAdminForClubs,
    isError: isErrorRetrieveAdminForClubs,
  } = useQuery({
    queryKey: ['ADMIN_FOR_CLUBS'],
    queryFn: UserApi.retrieveAdminForClubs,
    retry: 0,
  });

  const {
    data: adminForTeams,
    isLoading: isRetrieveAdminForTeamsLoading,
    refetch: refetchRetrieveAdminForTeams,
    error: errorRetrieveAdminForTeams,
    isError: isErrorRetrieveAdminForTeams,
  } = useQuery({
    queryKey: ['ADMIN_FOR_TEAMS'],
    queryFn: UserApi.retrieveAdminForTeams,
    retry: 0,
  });

  return {
    adminForClubs,
    isRetrieveAdminForClubsLoading,
    refetchRetrieveAdminForClubs,
    errorRetrieveAdminForClubs,
    isErrorRetrieveAdminForClubs,
    adminForTeams,
    isRetrieveAdminForTeamsLoading,
    refetchRetrieveAdminForTeams,
    errorRetrieveAdminForTeams,
    isErrorRetrieveAdminForTeams,
    isLoginLoading,
    userError,
    loginUser,
    fetchUser,
    isFetchUserError,
    fetchUserError,
    isFetchUserLoading,
    deleteUser,
    isDeleteUserLoading,
    deleteUserError,
    isDeleteUserError,
    reactEvent,
    isReactEventLoading,
    reactEventError,
    isReactEventError,
  };
};
