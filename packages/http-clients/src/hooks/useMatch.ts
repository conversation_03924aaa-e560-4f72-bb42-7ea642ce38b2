import { useQuery, useMutation } from '@tanstack/react-query';
import { useDataFetching } from './useDataFetching';
import { MatchApi } from '../services/match-api';
import CustomError from '../errors/CustomError';
import { ToasterType } from '@scorescast/formatters-constants';
import { i18next } from '@scorescast/translations';
import { useToasterStore } from '../stores/useToasterStore';

export const useMatch = (
  teamUniqueId: string | undefined,
  teamImage: string | undefined
) => {
  const { addToaster } = useToasterStore();

  const {
    data: matches,
    isLoading: isTeamMatchesLoading,
    refetch: refetchTeamMatches,
    error: errorTeamMatches,
    isError: isErrorTeamMatches,
  } = useQuery({
    queryKey: ['teamMatches', teamUniqueId],
    queryFn: async () => {
      if (!teamUniqueId) return;
      return await MatchApi.retrieveTeamMatches(teamUniqueId);
    },
    retry: 0,
    enabled: !!teamUniqueId,
    refetchInterval: 10000,
  });

  const deleteMatchMutation = useMutation({
    mutationFn: async (params: {
      matchId: string;
      options?: { onSuccess?: () => void };
    }): Promise<any> => {
      return await MatchApi.deleteMatch(params.matchId);
    },
    onSuccess: (_, params) => {
      addToaster({
        type: ToasterType.SUCCESS,
        title: i18next.t('API_SUCCESS.DELETE_MATCH_TITLE'),
        description: i18next.t('API_SUCCESS.DELETE_MATCH_MESSAGE'),
      });

      // Call the onSuccess callback if provided
      if (params.options?.onSuccess) {
        params.options.onSuccess();
      }
    },
    onError: (error: CustomError) => {
      addToaster({
        type: ToasterType.ERROR,
        title: i18next.t('API_ERRORS.TITLE'),
        description: error.message || i18next.t('API_ERRORS.DELETE_MATCH'),
      });
    },
  });

  const updateMatchMutation = useMutation({
    mutationFn: async (params: {
      matchId: string;
      matchData: any;
      options?: { onSuccess?: () => void };
    }): Promise<any> => {
      return await MatchApi.updateMatch(params.matchId, params.matchData);
    },
    onSuccess: (_, params) => {
      addToaster({
        type: ToasterType.SUCCESS,
        title: i18next.t('API_SUCCESS.UPDATE_MATCH_TITLE'),
        description: i18next.t('API_SUCCESS.UPDATE_MATCH_MESSAGE'),
      });

      // Call the onSuccess callback if provided
      if (params.options?.onSuccess) {
        params.options.onSuccess();
      }
    },
    onError: (error: CustomError) => {
      addToaster({
        type: ToasterType.ERROR,
        title: i18next.t('API_ERRORS.TITLE'),
        description: error.message || i18next.t('API_ERRORS.UPDATE_MATCH'),
      });
    },
  });

  useDataFetching({
    isError: isErrorTeamMatches,
    error: errorTeamMatches as CustomError,
  });

  useDataFetching({
    isError: deleteMatchMutation.isError,
    error: deleteMatchMutation.error as CustomError,
  });

  useDataFetching({
    isError: updateMatchMutation.isError,
    error: updateMatchMutation.error as CustomError,
  });

  return {
    matches,
    isTeamMatchesLoading,
    refetchTeamMatches,
    deleteMatch: deleteMatchMutation.mutate,
    isDeleteMatchLoading: deleteMatchMutation.isPending,
    updateMatch: updateMatchMutation.mutate,
    isUpdateMatchLoading: updateMatchMutation.isPending,
  };
};
