import { useMutation, useQuery } from '@tanstack/react-query';
import { ClubApi, UpdateClubData, CreateClubData } from '../services/club-api';
import { useToasterStore } from '../stores/useToasterStore';
import { i18next } from '@scorescast/translations';
import CustomError from '../errors/CustomError';
import { useDataFetching } from './useDataFetching';

export const useClub = () => {
  const { addToaster } = useToasterStore();

  const retrieveTeamAgents = (teamId: number) => {
    return ClubApi.retrieveAgentsOfTeam(teamId);
  };

  const retrieveTeamAdmins = (teamId: number) => {
    return ClubApi.retrieveAdminsOfTeam(teamId);
  };

  const {
    mutateAsync: _updateClubAsync,
    isPending: isUpdateClubLoading,
    error: updateClubError,
    isError: isUpdateClubError,
  } = useMutation({
    mutationFn: (clubData: UpdateClubData) => ClubApi.updateClub(clubData),
    onError: (error: CustomError) => {
      addToaster({
        type: 'error',
        title: i18next.t('API_ERRORS.TITLE'),
        description: error.message || i18next.t('ADMIN.CLUB_API.UPDATE_ERROR'),
      });
    },
  });

  const {
    mutateAsync: _createClubAsync,
    isPending: isCreateClubLoading,
    error: createClubError,
    isError: isCreateClubError,
  } = useMutation({
    mutationFn: (clubData: CreateClubData) => ClubApi.createClub(clubData),
    onError: (error: CustomError) => {
      addToaster({
        type: 'error',
        title: i18next.t('API_ERRORS.TITLE'),
        description: error.message || i18next.t('ADMIN.CLUB_API.CREATE_ERROR'),
      });
    },
  });

  const {
    mutateAsync: _deleteClubAsync,
    isPending: isDeleteClubLoading,
    error: deleteClubError,
    isError: isDeleteClubError,
  } = useMutation({
    mutationFn: (clubId: number) => ClubApi.deleteClub(clubId),
    onError: (error: CustomError) => {
      addToaster({
        type: 'error',
        title: i18next.t('API_ERRORS.TITLE'),
        description: error.message || i18next.t('ADMIN.CLUB_API.DELETE_ERROR'),
      });
    },
  });

  const updateClub = (
    clubData: UpdateClubData,
    options?: { onSuccess?: () => void }
  ) => {
    _updateClubAsync(clubData)
      .then(() => {
        addToaster({
          type: 'success',
          title: i18next.t('ADMIN.CLUB_API.SUCCESS'),
          description: i18next.t('ADMIN.CLUB_API.UPDATE_SUCCESS'),
        });
        if (options?.onSuccess) options.onSuccess();
      })
      .catch(() => {
        addToaster({
          type: 'error',
          title: i18next.t('ADMIN.CLUB_API.ERROR'),
          description: i18next.t('ADMIN.CLUB_API.UPDATE_ERROR'),
        });
      });
  };

  const createClub = (
    clubData: CreateClubData,
    options?: { onSuccess?: () => void }
  ) => {
    _createClubAsync(clubData)
      .then(() => {
        addToaster({
          type: 'success',
          title: i18next.t('ADMIN.CLUB_API.SUCCESS'),
          description: i18next.t('ADMIN.CLUB_API.CREATE_SUCCESS'),
        });

        if (options?.onSuccess) options.onSuccess();
      })
      .catch(() => {
        addToaster({
          type: 'error',
          title: i18next.t('ADMIN.CLUB_API.ERROR'),
          description: i18next.t('ADMIN.CLUB_API.CREATE_ERROR'),
        });
      });
  };

  const deleteClub = (clubId: number, options?: { onSuccess?: () => void }) => {
    _deleteClubAsync(clubId)
      .then(() => {
        addToaster({
          type: 'success',
          title: i18next.t('ADMIN.CLUB_API.SUCCESS'),
          description: i18next.t('ADMIN.CLUB_API.DELETE_SUCCESS'),
        });

        if (options?.onSuccess) options.onSuccess();
      })
      .catch(() => {
        addToaster({
          type: 'error',
          title: i18next.t('ADMIN.CLUB_API.ERROR'),
          description: i18next.t('ADMIN.CLUB_API.DELETE_ERROR'),
        });
      });
  };

  useDataFetching({
    isError: isUpdateClubError || isCreateClubError || isDeleteClubError,
    error: (updateClubError ||
      createClubError ||
      deleteClubError) as CustomError,
  });

  return {
    updateClub,
    isUpdateClubLoading,
    updateClubError,
    isUpdateClubError,
    createClub,
    isCreateClubLoading,
    createClubError,
    isCreateClubError,
    deleteClub,
    isDeleteClubLoading,
    deleteClubError,
    isDeleteClubError,
    retrieveTeamAgents,
    retrieveTeamAdmins,
  };
};
