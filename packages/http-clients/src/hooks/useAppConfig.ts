import { useMutation } from '@tanstack/react-query';
import { ApplicationApi } from '../services/app-api';
import { useAppStore } from '../stores/useAppStore';

export const useAppConfig = () => {
  const { setConfig } = useAppStore();

  const {
    mutateAsync: fetchConfig,
    error: fetchConfigError,
    isError: isFetchConfigError,
  } = useMutation({
    mutationFn: async (): Promise<any> => {
      return await ApplicationApi.retrieveAppConfig();
    },
    onSuccess: (response: any) => {
      if (!response?.error) {
        setConfig(response?.data);
      }
    },
    onError: () => {},
  });

  return {
    fetchConfig,
    fetchConfigError,
    isFetchConfigError,
  };
};
