import { SocialLoginType } from '@scorescast/formatters-constants';
import { User } from '@scorescast/formatters-constants';
import { create } from 'zustand';
import { storage, STORAGE_KEYS } from '../storage/storage';

interface AuthStore {
  isAuthenticated: boolean;
  user: typeof User | null;
  setUser: (user: typeof User) => void;
  token: string | null;
  setToken: (
    type: typeof SocialLoginType,
    token: string,
    user: typeof User
  ) => void;
  clearToken: () => void;
  initializeToken: () => void;
}

export const useAuthStore = create<AuthStore>((set) => ({
  user: null,
  token: null,
  isAuthenticated: false,
  setUser: (user) => set({ user }),
  setToken: (type, token, user) => {
    storage.set(STORAGE_KEYS.TOKEN, token);
    storage.set(STORAGE_KEYS.LOGIN_TYPE, type as unknown as string);
    set({ isAuthenticated: true });
    set({ user });
  },
  clearToken: () => {
    storage.set(STORAGE_KEYS.TOKEN, false);
    set({ isAuthenticated: false });
    set({ user: null });
  },
  initializeToken: () => {
    const token = storage.getString(STORAGE_KEYS.TOKEN);
    set({ isAuthenticated: token ? true : false });
  },
}));

/**
 * Custom hook that provides role hierarchy logic
 * Establishes hierarchy: SUPER_ADMIN > CLUB_ADMIN > TEAM_ADMIN
 */
export const useUserRoles = () => {
  const user = useAuthStore((state) => state.user);

  // Establish role hierarchy: SUPER_ADMIN > CLUB_ADMIN > TEAM_ADMIN
  const isSuperAdmin = user?.roles?.includes('SUPER_ADMIN') || false;
  const isClubAdmin = user?.roles?.includes('CLUB_ADMIN') && !isSuperAdmin;
  const isTeamAdmin =
    user?.roles?.includes('TEAM_ADMIN') &&
    !isSuperAdmin &&
    !user?.roles?.includes('CLUB_ADMIN');

  return {
    user,
    isSuperAdmin,
    isClubAdmin,
    isTeamAdmin,
    // Helper functions for common permission checks
    canEditClubs: isSuperAdmin || isClubAdmin,
    canAddClubs: isSuperAdmin,
    canEditTeams: isSuperAdmin || isClubAdmin,
    canAddTeams: isSuperAdmin || isClubAdmin,
  };
};
