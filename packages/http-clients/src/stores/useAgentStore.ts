import {
  MatchPossibleTeams,
  MatchLocations,
} from './../../../formatters-constants/src/constants/live-match';
import { create } from 'zustand';

interface AgentStore {
  usTeamImage: string;
  currentMatchLocation: MatchLocations | null;
  sideSelection: typeof MatchPossibleTeams;
  setSideSelection: (sideSelection: typeof MatchPossibleTeams) => void;
  setUsTeamImage: (usTeamImage: string) => void;
  setCurrentMatchLocation: (currentMatchLocation: MatchLocations) => void;
}

export const useAgentStore = create<AgentStore>((set) => ({
  sideSelection: MatchPossibleTeams[0],
  usTeamImage: '',
  currentMatchLocation: null,
  setCurrentMatchLocation: (currentMatchLocation) =>
    set({ currentMatchLocation }),
  setUsTeamImage: (usTeamImage) => set({ usTeamImage }),
  setSideSelection: (sideSelection) => set({ sideSelection }),
}));
