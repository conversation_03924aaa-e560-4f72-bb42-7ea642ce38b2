import { create } from 'zustand';
import { Toaster } from '@scorescast/formatters-constants';
import { shallow } from 'zustand/shallow';

interface AuthStore {
  toaster: typeof Toaster | null;
  addToaster: (toaster: typeof Toaster) => void;
  removeToaster: () => void;
}

export const useToasterStore = create<AuthStore>((set, get) => ({
  toaster: null,
  addToaster: (toaster) => {
    const currentToaster = get().toaster;
    if (JSON.stringify(currentToaster) !== JSON.stringify(toaster)) {
      set({ toaster });
    }
  },
  removeToaster: () => {
    setTimeout(() => {
      set({ toaster: null });
    }, 0);
  },
}));
