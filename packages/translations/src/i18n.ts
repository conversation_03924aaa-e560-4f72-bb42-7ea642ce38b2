import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import en from './resources/en.json';
import ro from './resources/ro.json';

const resources = {
  en: {
    translation: en,
  },
  ro: {
    translation: ro,
  },
};

const fallBackLanguage = 'en';

const i18nextNewInstance = i18next.createInstance();

i18nextNewInstance.use(initReactI18next).init({
  resources,
  lng: fallBackLanguage,
  fallbackLng: fallBackLanguage,
  interpolation: {
    escapeValue: false,
  },
});

export default i18nextNewInstance;
