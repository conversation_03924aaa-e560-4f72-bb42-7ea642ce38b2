{"COMMON": {"CONFIRM": "Confirm", "CANCEL": "Cancel"}, "ADMIN": {"NO_ADMIN_ROLE": "You don't have any admin role", "UNKNOWN_ADMIN_ROLE": "Unknown admin role", "CLUB_ADMIN": {"CLUB_ADMIN_PANEL": "All Clubs", "ADD_CLUB": "Add Club", "CONTACT_SUPPORT_FOR_CLUB": "Contact support to add a club", "CONTACT_SUPPORT_FOR_TEAM": "Contact support to get access to teams", "NO_CLUBS_ADDED": "You have not added any clubs yet"}, "VIEW_CLUB": {"CLUB_DETAILS": "Club Page", "TEAMS": "Teams", "NO_TEAMS": "No teams added yet", "ADD_TEAM": "Add Team", "DELETE_CLUB": "Delete Club", "DELETE_CONFIRM_TITLE": "Delete Club", "DELETE_CONFIRM_MESSAGE": "Are you sure you want to delete this club? This action cannot be undone.", "CANCEL": "Cancel", "CONFIRM_DELETE": "Delete"}, "EDIT_CLUB": {"EDIT_CLUB": "Edit Club", "ADD_CLUB": "Add new Club", "CLUB_NAME": "Club Name", "CLUB_NAME_ERROR": "Club name must be at least 3 characters", "ENTER_CLUB_NAME": "Enter club name", "SAVE_CHANGES": "Save Changes", "CREATE_CLUB": "Create Club", "CLUB_EMAIL": "Club Email", "ENTER_CLUB_EMAIL": "Enter club email", "CREST_REQUIRED": "Crest is required"}, "VIEW_TEAM": {"TEAM_ADMIN_PANEL": "All Teams", "ADD_TEAM": "Add Team", "CONTACT_SUPPORT_FOR_TEAM": "Contact support to add a team", "ALL_TEAMS": "All Teams", "TEAM_DETAILS": "Team Page"}, "EDIT_TEAM": {"TITLE": "Edit Team", "EDIT_TEAM": "Edit Team", "ADD_TEAM": "Add Team", "UPLOAD_LOGO": "Upload team logo", "TEAM_NAME": "Team Name", "TEAM_SHORT_NAME": "Team Short Name", "TEAM_AGE_GROUP": "Age Group", "TEAM_NAME_ERROR": "Team name must be at least 3 characters", "ENTER_TEAM_NAME": "Enter team name", "ENTER_TEAM_SHORT_NAME": "Enter team short name", "ENTER_TEAM_AGE_GROUP": "Enter age group", "IMAGE_REQUIRED": "Team logo is required", "SAVE_CHANGES": "Save Changes", "CREATE_TEAM": "Create Team"}, "PLAYER_ACTION": {"PLAYERS": "Players", "ADD_PLAYER": "Add Player", "ADD": {"TITLE": "Add Player"}, "EDIT": {"TITLE": "Edit Player"}, "FORM": {"JERSEY_NAME": "Jersey Name", "JERSEY_NUMBER": "Jersey Number", "DELETE_PLAYER": "Delete Player", "ADD_PLAYER": "Add Player"}, "DELETE_CONFIRM_TITLE": "Delete Player", "DELETE_CONFIRM_MESSAGE": "Are you sure you want to delete {{playerName}}? This action cannot be undone.", "CANCEL": "Cancel", "CONFIRM_DELETE": "Delete", "VALIDATION": {"EXISTING_NUMBER_TITLE": "Duplicate number", "EXISTING_NUMBER_DESCRIPTION": "The number {{jersey<PERSON><PERSON>ber}} already exists for player {{player}}. Please pick another number."}}, "CLUB_API": {"UPDATE_ERROR": "Failed to update club", "UPDATE_SUCCESS": "Club updated successfully", "CREATE_ERROR": "Failed to create club", "CREATE_SUCCESS": "Club created successfully", "DELETE_ERROR": "Failed to delete club", "DELETE_SUCCESS": "Club deleted successfully", "ERROR": "Error", "SUCCESS": "Success"}, "TEAM_MATCHES": {"ADD_MATCH": "Add Match", "UPCOMING_MATCHES": "Upcoming Matches", "NO_UPCOMING_MATCHES": "No upcoming matches found"}, "EDIT_MATCH": {"ADD_TITLE": "Add Match", "EDIT_TITLE": "Edit Match", "LOADING": "Loading Match...", "TEAM_NAME": "Team Name", "OPPONENT_NAME": "Opponent Name", "ENTER_OPPONENT_NAME": "Enter opponent name", "DATE_TIME": "Date and Time", "DATE": "Date", "TIME": "Time", "SELECT_DATE": "Select Date", "SELECT_TIME": "Select Time", "LOCATION": "Location", "MATCH_TYPE": "Match Type", "MATCH_SETUP": "Match Setup", "DESCRIPTION": "Description", "ENTER_DESCRIPTION": "Enter match description (optional)", "SAVE": "Save Match", "DONE": "Done", "CREATE_SUCCESS": "Match created successfully", "UPDATE_SUCCESS": "Match updated successfully", "DELETE_SUCCESS": "Match deleted successfully", "DELETE": "Delete Match", "DELETE_CONFIRM_TITLE": "Delete Match", "DELETE_CONFIRM_MESSAGE": "Are you sure you want to delete this match? This action cannot be undone.", "CONFIRM_DELETE": "Delete", "MATCH_TYPE_OPTIONS": {"FVF": "Five vs Five", "SVS": "Six vs Six", "EVE": "Eleven vs Eleven"}, "MATCH_SETUP_OPTIONS": {"P1": "One Period", "P2": "Two Periods", "P4": "Four Periods"}, "LOCATION_OPTIONS": {"HOME": "Home", "AWAY": "Away"}}}, "ONBOARDING": {"FIRST_STEP": {"TITLE": "Follow the most important games of all", "BUTTON_TITLE": "Get Started"}, "SECOND_STEP": {"TITLE": "Notifications when you can't be there", "BUTTON_TITLE": "Continue"}, "THIRD_STEP": {"TITLE": "Share all the action with your followers", "BUTTON_TITLE": "Continue"}, "FOURTH_STEP": {"TITLE": "Insights even when you are there", "BUTTON_TITLE": "<PERSON><PERSON>"}, "FIFTH_STEP": {"TITLE": "Welcome back!", "BUTTON_TITLE": "<PERSON><PERSON>"}, "SOCIAL_LOGIN": {"FACEBOOK": "Login with Facebook", "GOOGLE": "Login with Google", "APPLE": "Login with Apple"}}, "IMG_UPLOADER": {"UPLOAD_PLACEHOLDER": "Tap to upload team badge", "WEBP_NOT_VALID": "Webp format is not valid", "UPLOADING": "Uploading...", "PROCESSING_FAILED": "Failed to process image"}, "BOTTOM_BAR": {"HOME": "Home", "ONGOING": "Ongoing", "PROFILE": "Profile"}, "HOME": {"TITLE": "Let's start", "CODE_READER_TITLE": "Enter the 5 Digit Code", "QR_READER_TITLE": "Scan the QR code of your team", "QR_CODE_BUTTON_TITLE": "Scan QR", "QR_CODE_NOT_AVAILABLE_TITLE": "Oooops...", "QR_CODE_NOT_AVAILABLE_MESSAGE": "This feature is not available yet. Come later to enjoy it!"}, "PROFILE": {"TITLE": "Profile", "ITEMS": {"FOLLOWING": "Following", "NOTIFICATIONS": "Notifications", "BECOME_AN_AGENT": "Become an agent", "ABOUT": "About Scorescast", "DETAILS": "Your details", "MANAGE_SECTION": "Admin Management", "LOGOUT": "Logout"}}, "DETAILS": {"TITLE": "Your details", "LINK_ACCOUNT": "Link account", "LINK_ACCOUNT_TYPE": "Signed in with {{type}}", "SIGN_OUT": "Sign out"}, "ABOUT": {"TITLE": "Scorescast", "REPORT_A_BUG": "Report a bug", "PRIVACY_STATEMENT": "Privacy statement", "TERMS_OF_SERVICE": "Terms of service", "COMMUNITY_GUIDELINES": "Community guidelines", "CREDITS": "Credits", "REQUEST_ACCOUNT_DELETION": "Request account deletion", "DELETE_ACCOUNT": {"TITLE": "Delete account", "DESCRIPTION": "Are you sure you want to delete your account? This action cannot be undone.", "DELETE": "Delete"}, "VERSION": "Version"}, "FOLLOWING": {"TITLE": "Following", "TEAMS": "Teams", "AGENT_ACCESS": "Agent access", "DELETE_TEAM": {"TITLE": "Delete team", "DESCRIPTION": "Are you sure you want to delete this team? This action cannot be undone.", "CANCEL": "Cancel", "DELETE": "Delete"}, "MANAGE_TEAM": {"NOTIFICATIONS": "Receive notifications", "SHARE": "Share team", "DELETE": "Not a fan anymore? Delete"}, "SHARE_TEAM": {"SHARE": "Share", "SMS": "🏆 Sign up and support {{teamName}} on Scorescast to get live scores, stats, and updates!👉 {{link}}"}}, "NOTIFICATIONS": {"TITLE": "Notifications", "NOTIFICATIONS": "Notifications", "REMINDERS": "Reminders"}, "BECOME_AN_AGENT": {"ERROR_TITLE": "Oooops...", "ERROR_MESSAGE": "This feature is not available yet. Come later to enjoy it!"}, "API_ERRORS": {"TITLE": "Ooops...", "FOLLOW_TEAM": "We are experiencing issue with following this team. Please try again later.", "RETRIEVE_TEAM": "We are experiencing issue with retrieving this team. Please try again later.", "FETCH_FOLLOWED_TEAMS": "We are experiencing issue with retrieving your followed teams. Please try again later.", "FETCH_TEAM_FOLLOWERS": "We are experiencing issue with retrieving team followers. Please try again later.", "LOGOUT": "Your session has expired. Please login again.", "LOGIN": "We are experiencing issue with logging in. Please try again later.", "FETCH_USER": "We are experiencing issue with fetching user details. Please try again later.", "RETRIEVE_MATCH_DETAILS": "We are experiencing issue with retrieving match details. Please try again later.", "UPDATE_CLUB": "We are experiencing issue with updating club. Please try again later.", "CREATE_CLUB": "We are experiencing issue with creating club. Please try again later.", "DELETE_CLUB": "We are experiencing issue with deleting club. Please try again later.", "RETRIEVE_TEAM_AGENTS": "We are experiencing issue with retrieving team agents. Please try again later.", "RETRIEVE_TEAM_ADMINS": "We are experiencing issue with retrieving team admins. Please try again later.", "CLUB_NAME_ALEADY_EXIST": "Club name already exist", "UPDATE_TEAM": "Failed to update team", "CREATE_TEAM": "Failed to create team", "DELETE_TEAM": "Failed to delete team", "ADD_PLAYER": "Failed to add player", "DELETE_PLAYER": "Failed to delete player", "CREATE_MATCH": "Failed to create match", "UPDATE_MATCH": "Failed to update match", "DELETE_MATCH": "Failed to delete match", "RETRIEVE_MATCH": "Failed to retrieve match details"}, "API_SUCCESS": {"FOLLOW_TEAM_TITLE": "Added!", "FOLLOW_TEAM_MESSAGE": "You are now following this team. Make sure to allow notifications to receive updates.", "UPDATE_CLUB_TITLE": "Success", "UPDATE_CLUB_MESSAGE": "Club updated successfully", "CREATE_CLUB_TITLE": "Success", "CREATE_CLUB_MESSAGE": "Club created successfully", "UPDATE_TEAM_TITLE": "Success", "UPDATE_TEAM_MESSAGE": "Team updated successfully", "CREATE_TEAM_TITLE": "Success", "CREATE_TEAM_MESSAGE": "Team created successfully", "DELETE_TEAM_TITLE": "Success", "DELETE_TEAM_MESSAGE": "Team deleted successfully", "ADD_PLAYER_TITLE": "Player Added", "ADD_PLAYER_MESSAGE": "Player has been added successfully", "DELETE_PLAYER_TITLE": "Player Deleted", "DELETE_PLAYER_MESSAGE": "Player has been deleted successfully", "DELETE_MATCH_TITLE": "Match Deleted", "DELETE_MATCH_MESSAGE": "Match has been deleted successfully", "UPDATE_MATCH_TITLE": "Match Updated", "UPDATE_MATCH_MESSAGE": "Match has been updated successfully"}, "TEAM_CONFIRMATION": {"TITLE": "Is this your team?", "MESSAGE": "Confirm to proceed", "BUTTON": "Follow", "DISCARD": "Not this team, go back"}, "TEAM": {"TEAM_AGENTS": "Team Agents", "TEAM_ADMINS": "Team Admins", "PLAYERS": "Players", "MATCHES": "Matches", "FOLLOWERS": "Followers", "DELETE_TEAM": "Delete Team", "DELETE_TEAM_CONFIRM_TITLE": "Delete Team", "DELETE_TEAM_CONFIRM_MESSAGE": "Are you sure you want to delete this team? This action cannot be undone.", "CANCEL": "Cancel", "CONFIRM_DELETE": "Delete", "NO_AGENTS": "No agents found for this team", "ADD_AGENT": "Add Agent", "AGENT_DETAILS": "Agent Details", "NO_ADMINS": "No admins found for this team", "ADD_ADMIN": "Add Admin", "ADMIN_DETAILS": "Admin Details", "NO_FOLLOWERS": "No followers found for this team", "FIRST_NAME": "First Name", "LAST_NAME": "Last Name", "EMAIL": "Email", "REMOVE_ACCESS": "Remove Access"}, "TEAM_MATCHES_TABS": {"ALL": "All", "LIVE": "Live", "UPCOMING": "Upcoming", "PAST": "Past"}, "MATCH_TABS": {"STATS": "Stats", "FEED": "Feed"}, "MATCH_TIME": {"UPCOMING": "Upcoming", "LIVE": "Live", "PAST": "Fulltime", "P1": "1st Period", "P2": "2nd Period", "P3": "3rd Period", "P4": "4th Period", "HT": "Halftime", "ET1": "1st Extra Time", "ET2": "2nd Extra Time", "PENS": "Penalties", "PENS_SHORT": "PENS", "P1_BREAK": "Break", "P3_BREAK": "Break", "ETHT": "ET Halftime", "AET": "After ET"}, "MATCH_SCREEN": {"STARTED_TITLE": "Live game", "SCHEDULED_TITLE": "Upcoming game", "END_TITLE": "Fulltime"}, "MATCH_DETAILS": {"HOME_TEAM": "Home Team", "AWAY_TEAM": "Away Team", "CURRENT_PERIOD": "Current period", "DATE": "Date"}, "MATCH_STATS": {"NOT_AVAILABLE": "This feature is not available yet."}, "MATCH_FEED": {"NO_EVENTS": "This match has not events yet!", "PERIOD_1": "Period 1", "PERIOD_2": "Period 2", "PERIOD_3": "Period 3", "PERIOD_4": "Period 4", "EXTRA_TIME_1": "Extra Time 1", "EXTRA_TIME_2": "Extra Time 2", "PENALTIES": "Penalties", "FEED_ITEMS": {"FOUL": {"TITLE": "F<PERSON>l"}, "YELLOW_CARD": {"TITLE": "Yellow Card"}, "SECOND_YELLOW_CARD": {"TITLE": "Second Yellow Card"}, "RED_CARD": {"TITLE": "Red Card"}, "PENALTY_SAVED": {"TITLE": "Penalty Saved"}, "PENALTY_CONCEDED": {"TITLE": "Penalty conceded"}, "PENALTY_GOAL": {"TITLE": "Penalty Goal"}, "MISS_PENALTY": {"TITLE": "Missed Penalty"}, "SAVED_PENALTY": {"TITLE": "Saved <PERSON>"}, "GOAL": {"TITLE": "Goal"}, "OWN_GOAL": {"TITLE": "Own Goal"}, "ASSIST": {"TITLE": "Assist"}, "KICKOFF": {"TITLE": "Kick off"}, "END_P1": {"TITLE": "End of 1st period"}, "KICKOFF_P2": {"TITLE": "Kick off 2nd period"}, "END_P2": {"TITLE": "End of 2nd period"}, "KICKOFF_P3": {"TITLE": "Kick off 3rd period"}, "END_P3": {"TITLE": "End of 3rd period"}, "KICKOFF_P4": {"TITLE": "Kick off 4th period"}, "END_P4": {"TITLE": "End of 4th period"}, "KICKOFF_ET1": {"TITLE": "Kick off 1st extra time"}, "END_ET1": {"TITLE": "End of 1st extra time"}, "KICKOFF_ET2": {"TITLE": "Kick off 2nd extra time"}, "END_ET2": {"TITLE": "End of 2nd extra time"}, "KICKOFF_PENS": {"TITLE": "Penalties"}, "END": {"TITLE": "Match End"}, "METADATA": {"by": "by {{value}}", "cardFor": "for {{value}}", "score": "({{value}})", "is": "The match agent is {{value}}", "to": "Thank you to {{value}}"}}}, "AGENT": {"FEED_VIEW": {"TITLE": "Match Feed"}, "TEAM_MANAGEMENT": {"TITLE": "Edit Team", "MEMBERS": "Team Members", "ADD_MEMBER": "+ Add Member"}, "PLAYER_ACTION": {"EDIT": {"TITLE": "Edit Player"}, "ADD": {"TITLE": "Add Player", "VALIDATION": {"EXISTING_NUMBER_TITLE": "Duplicate number", "EXISTING_NUMBER_DESCRIPTION": "The number {{jersey<PERSON><PERSON>ber}} already exists for player {{player}}. Please pick another number."}}, "FORM": {"FIRST_NAME": "First Name", "LAST_NAME": "Last Name", "JERSEY_NUMBER": "Number", "JERSEY_NAME": "Jersey Name", "FEDERATION_ID": "Federation ID", "DELETE_PLAYER": "Delete Player", "ADD_PLAYER": "Add Player"}}, "CLAIM_MATCH": {"REGISTERED_AGENT_FOR_MATCH_INFO": "You are a registered Agent for this team", "CLAIM_CTA": "Manage this match", "ALREADY_CLAIMED_CTA": "Match already claimed", "GO_BACK_BUTTON": "Go back", "CONFIRMATION": {"TITLE": "Manage this match", "DESCRIPTION": "Are you sure you want to manage this match? This action cannot be undone.", "CTA": "Confirm"}}, "KICKOFF_MATCH": {"P1__NS": {"SHORT_DESCRIPTION": "You are the agent for this match", "EDIT_TEAM_CTA": "Edit Team Players", "KICKOFF_CTA": "Kickoff", "LONG_DESCRIPTION": "You are the agent for this match. You can edit the team players and start the match."}, "NS": {"SHORT_DESCRIPTION": "You are the agent for this match", "EDIT_TEAM_CTA": "Edit Team Players", "KICKOFF_CTA": "Kickoff", "LONG_DESCRIPTION": "You are the agent for this match. You can edit the team players and start the match."}, "HT": {"SHORT_DESCRIPTION": "This is the half time screen", "EDIT_TEAM_CTA": "Edit Team Players", "KICKOFF_CTA": "Start 2nd half", "LONG_DESCRIPTION": "You are the agent for this match. You can edit the team players or start the next half"}, "P1_BREAK": {"SHORT_DESCRIPTION": "This is the 1st period break screen", "EDIT_TEAM_CTA": "Edit Team Players", "KICKOFF_CTA": "Start 2nd Period", "LONG_DESCRIPTION": "You are the agent for this match. You can edit the team players or start the next period"}, "P3_BREAK": {"SHORT_DESCRIPTION": "This is the 3rd period break screen", "EDIT_TEAM_CTA": "Edit Team Players", "KICKOFF_CTA": "Start 4th Period", "LONG_DESCRIPTION": "You are the agent for this match. You can edit the team players or start the next period"}, "P1__FT": {"SHORT_DESCRIPTION": "This is the final time screen", "EDIT_TEAM_CTA": "Edit Team Players", "KICKOFF_CTA": "Start Extra Time", "LONG_DESCRIPTION": "You are the agent for this match. You can edit the team players, end the game, start extra time or penalty kicks.", "END_GAME_CTA": "End Game", "PENALTIES_CTA": "Penalties"}, "FT": {"SHORT_DESCRIPTION": "This is the final time screen", "EDIT_TEAM_CTA": "Edit Team Players", "KICKOFF_CTA": "Start 1st Extra Time", "LONG_DESCRIPTION": "You are the agent for this match. You can edit the team players, end the game, start extra time or penalty kicks.", "END_GAME_CTA": "End Game", "PENALTIES_CTA": "Penalties"}, "ETHT": {"SHORT_DESCRIPTION": "This is the final time screen", "EDIT_TEAM_CTA": "Edit Team Players", "KICKOFF_CTA": "Start 2nd Extra Time", "LONG_DESCRIPTION": "You are the agent for this match. You can edit the team players, end the game, start extra time or penalty kicks.", "END_GAME_CTA": "End Game", "PENALTIES_CTA": "Penalties"}, "AET": {"SHORT_DESCRIPTION": "This is the final time screen", "EDIT_TEAM_CTA": "Edit Team Players", "KICKOFF_CTA": "Start 1st Extra Time", "LONG_DESCRIPTION": "You are the agent for this match. You can edit the team players, end the game, start extra time or penalty kicks.", "END_GAME_CTA": "End Game", "PENALTIES_CTA": "Penalties"}, "PENS": {"END_GAME_CTA": "End Game"}}, "PENALTY_CONFIRM_SHEET": {"PENALTY_SAVE": {"TITLE": "Save", "DESCRIPTION": "Are you sure you want to save this penalty? This action cannot be undone.", "CTA": "Confirm"}, "PENALTY_MISS": {"TITLE": "Miss", "DESCRIPTION": "Are you sure you want to save this penalty? This action cannot be undone.", "CTA": "Confirm"}, "PENALTY_GOAL": {"TITLE": "Goal", "DESCRIPTION": "Are you sure you want to save this penalty? This action cannot be undone.", "CTA": "Confirm"}}, "KICKOFF_CONFIRM_SHEET": {"NEXT_STEP": {"P1__NS": {"TITLE": "Kickoff match", "DESCRIPTION": "Are you sure you want to start the match? This action cannot be undone.", "CTA": "Confirm"}, "NS": {"TITLE": "Kickoff match", "DESCRIPTION": "Are you sure you want to start the match? This action cannot be undone.", "CTA": "Confirm"}, "HT": {"TITLE": "Kickoff 2nd half", "DESCRIPTION": "Are you sure you want to start the 2nd half? This action cannot be undone.", "CTA": "Confirm"}, "P1_BREAK": {"TITLE": "Kickoff 2nd period", "DESCRIPTION": "Are you sure you want to start the 2nd period? This action cannot be undone.", "CTA": "Confirm"}, "P3_BREAK": {"TITLE": "Kickoff 4th period", "DESCRIPTION": "Are you sure you want to start the 4th period? This action cannot be undone.", "CTA": "Confirm"}, "P1__FT": {"TITLE": "Kickoff extra half", "DESCRIPTION": "Are you sure you want to start the extra half? This action cannot be undone.", "CTA": "Confirm"}, "FT": {"TITLE": "Kickoff 1st extra half", "DESCRIPTION": "Are you sure you want to start the 1st extra half? This action cannot be undone.", "CTA": "Confirm"}, "ETHT": {"TITLE": "Kickoff 2nd extra half", "DESCRIPTION": "Are you sure you want to start the 2nd extra half? This action cannot be undone.", "CTA": "Confirm"}}, "END_GAME": {"NS": {"TITLE": "End Game", "DESCRIPTION": "Are you sure you want to end this match? This action cannot be undone.", "CTA": "Confirm"}, "HT": {"TITLE": "End Game", "DESCRIPTION": "Are you sure you want to end this match? This action cannot be undone.", "CTA": "Confirm"}, "FT": {"TITLE": "End Game", "DESCRIPTION": "Are you sure you want to end this match? This action cannot be undone.", "CTA": "Confirm"}, "ETHT": {"TITLE": "End Game", "DESCRIPTION": "Are you sure you want to end this game? This action cannot be undone.", "CTA": "Confirm"}, "AET": {"TITLE": "End Game", "DESCRIPTION": "Are you sure you want to end this game? This action cannot be undone.", "CTA": "Confirm"}, "PENS": {"TITLE": "End Game", "DESCRIPTION": "Are you sure you want to end this game? This action cannot be undone.", "CTA": "Confirm"}}, "PENALTIES": {"NS": {"TITLE": "Start penalties", "DESCRIPTION": "Are you sure you want to start penalties? This action cannot be undone.", "CTA": "Confirm"}, "HT": {"TITLE": "Start penalties", "DESCRIPTION": "Are you sure you want to start penalties? This action cannot be undone.", "CTA": "Confirm"}, "FT": {"TITLE": "Start penalties", "DESCRIPTION": "Are you sure you want to start penalties? This action cannot be undone.", "CTA": "Confirm"}, "ETHT": {"TITLE": "Start penalties", "DESCRIPTION": "Are you sure you want to start penalties? This action cannot be undone.", "CTA": "Confirm"}, "AET": {"TITLE": "Start penalties", "DESCRIPTION": "Are you sure you want to start penalties? This action cannot be undone.", "CTA": "Confirm"}}}, "LIVE_GAME_CONFIRM_SHEET": {"P1": {"TITLE": "End 1st Period", "DESCRIPTION": "Are you sure you want to end the first period of the match? This action cannot be undone.", "CTA": "Confirm"}, "P2": {"TITLE": "End 2nd Period", "DESCRIPTION": "Are you sure you want to end the second period of the match? This action cannot be undone.", "CTA": "Confirm"}, "P3": {"TITLE": "End 3nd Period", "DESCRIPTION": "Are you sure you want to end the third period of the match? This action cannot be undone.", "CTA": "Confirm"}, "P4": {"TITLE": "End 4nd Period", "DESCRIPTION": "Are you sure you want to end the forth period of the match? This action cannot be undone.", "CTA": "Confirm"}, "ET": {"TITLE": "End Extra Period", "DESCRIPTION": "Are you sure you want to end the extra period of the match? This action cannot be undone.", "CTA": "Confirm"}, "ET1": {"TITLE": "End 1st Extra Period", "DESCRIPTION": "Are you sure you want to end the first extra period of the match? This action cannot be undone.", "CTA": "Confirm"}, "ET2": {"TITLE": "End 2nd Extra Period", "DESCRIPTION": "Are you sure you want to end the second extra period of the match? This action cannot be undone.", "CTA": "Confirm"}}, "LIVE_MATCH": {"P1": {"END_PERIOD": "End 1st Period"}, "P4__P1": {"END_PERIOD": "End 1st Period"}, "P2": {"END_PERIOD": "End 2nd Half"}, "P4__P2": {"END_PERIOD": "End 2nd Period"}, "P4__P3": {"END_PERIOD": "End 3rd Period"}, "P4__P4": {"END_PERIOD": "End 4th Period"}, "P4__ET1": {"END_PERIOD": "End 1st Extra Half"}, "P4__ET2": {"END_PERIOD": "End 2nd Extra Half"}, "ET1": {"END_PERIOD": "End 1st Extra Half"}, "ET": {"END_PERIOD": "End Extra Half"}, "ET2": {"END_PERIOD": "End 2nd Extra Half"}, "INFO": "All data you enter will be immediately available to followers. Please double-check the events you add to ensure accuracy and relevance.", "POSSIBLE_EVENTS": {"GOAL": "Goal", "FOUL": "F<PERSON>l"}, "POSSIBLE_TEAMS": {"US": "Us", "THEM": "Them"}, "POSSIBLE_PENALTY_TYPES": {"GOAL": "PENALTY GOAL", "SAVE": "PENALTY SAVE", "MISS": "PENALTY MISS"}, "POSSIBLE_GOAL_TYPES": {"SHOT": "Shot", "FREEKICK": "Freekick", "OWN_GOAL": "Own Goal", "PENALTY": "Penalty"}, "POSSIBLE_FOUL_TYPES": {"FOUL_FREEKICK": "Freekick", "FOUL_PENALTY": "Penalty", "CARDS": "Card"}, "EDIT_TEAM_CTA": "Edit Team Players"}, "PENALTY_SELECTION": {"PENALTY_GOAL": "Penalty Goal", "PENALTY_SAVE": "Penalty Save", "PENALTY_MISS": "Penalty Miss", "SELECT_PLAYER": "Select Player", "CONFIRM": "Confirm", "CANCEL": "Cancel", "PLAYER": "Player:", "PENALTY_GOAL_EVENT_NAME": "Penalty Goal", "PENALTY_MISS_EVENT_NAME": "Penalty Miss", "PENALTY_SAVE_EVENT_NAME": "Penalty Save"}, "GOAL_SELECTION": {"SELECT_PLAYER": "Select Player", "SELECT_ASSIST": "Select Assist", "GOAL": "Goal", "SHOT": "Shot", "FREEKICK": "Freekick", "PENALTY": "Penalty", "OWN_GOAL": "Own Goal", "CONFIRM": "Confirm", "CANCEL": "Cancel", "PLAYER": "Player:", "ASSIST": "Assist:", "CONFIRM_OPOSITION_OWN_GOAL": "Own Goal by opposition."}, "FOUL_SELECTION": {"FOUL": "F<PERSON>l", "FOUL_FREEKICK": "Freekick", "FOUL_PENALTY": "Penalty", "CARDS": "Card", "SELECT_CARD": "Select Card", "SELECT_PLAYER": "Select Player", "POSSIBLE_CARDS": {"YELLOW_CARD": "Yellow", "RED_CARD": "Red", "SECOND_YELLOW_CARD": "2nd Yellow"}, "CONFIRM": "Confirm", "CANCEL": "Cancel", "PLAYER": "Player:"}, "END_GAME": {"TITLE": "Thank you!", "DESCRIPTION": "You succesfully managed this match. We appreaciate you and your effort to help us!", "BUTTON": "Go to match report"}}, "ADD_A_TEAM": {"TITLE": "Add a team", "BOTTOM_SHEET": {"TITLE": "Add a team", "DESCRIPTION": "Enter the 5-digit team code to follow and stay updated with the team's activities.", "SUB_DESCRIPTION": "Enter 5 digit code", "QR_CODE_DESCRIPTION": "Don't want to enter the 5-digit team code? Scan the QR code instead!", "BUTTON": "Search"}}, "QR_CODE_SCANNER": {"TITLE": "Scan the QR code", "DESCRIPTION": "Point your camera at the team's QR code to search it instantly.", "BUTTON": "<PERSON><PERSON>"}, "NOTIFICATIONS_SHEET": {"TITLE": "Never Miss a Moment!", "DESCRIPTION": "Don't miss a beat! Get real-time updates about your favorite team — straight to your phone.", "BUTTON": "Allow notifications", "DISCARD": "No, thanks"}, "REACTIONS_SHEET": {"TITLE": "Reactions", "LABELS": {"LOVE": "shared some love!", "LIKE": "gave it a thumbs up!", "DISLIKE": "smile on this one!", "CLAP": "brought the hype!"}}, "PREMIUM_BOTTOM_SHEET": {"TITLE": "All Access, All Yours", "DESCRIPTION": "You're now a ", "BOLD_DESCRIPTION": "Premium User!", "SUB_DESCRIPTION": "All features are unlocked and ready to explore! Dive in and enjoy the full experience the app has to offer.", "SUB_DESCRIPTION_2": "Your membership is active until", "SUB_DESCRIPTION_3": "31 December 2025.", "BUTTON": "Explore"}, "PREMIUM_BADGE": {"TITLE": "Premium"}, "ERRORS": {"NO_MATCHES": {"TITLE": "No matches found.", "DESCRIPTION": "Come back later to see team matches."}, "NO_INTERNET": {"TITLE": "No internet connection.", "DESCRIPTION": "Please check your internet connection and try again.", "RETRY": "Retry"}, "NO_FOLLOWED_TEAMS": {"TITLE": "No following teams.", "DESCRIPTION": "Follow a team to get started."}, "MAINTENANCE": {"TITLE": "BRB, Fixing Things!", "DESCRIPTION": "We're giving the app a little tune-up! Hang tight — we'll be back before you can say ”app update”.", "RETRY": "Retry"}, "APP_UPDATE": {"TITLE": "Stay up to date!", "DESCRIPTION": "We've made some exciting updates to make your experience even better. Grab the latest version to keep things running smoothly!", "RETRY": "Update"}}}