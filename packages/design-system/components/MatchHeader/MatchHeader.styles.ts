import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const matchHeaderStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    matchHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    scoreCard: {
      alignItems: 'center',
    },
    score: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    scoreText: {
      ...typography['xLarge-bold'],
      paddingHorizontal: spacings.small,
      color: colors.inverse900,
    },
    time: {
      ...typography['xSmall-semibold'],
      color: colors.inverse900,
      textAlign: 'center',
      marginTop: spacings.medium,
    },
    penaltyText: {
      ...typography['xxSmall'],
      color: colors.inverse900,
      textAlign: 'center',
      marginTop: spacings.medium,
    },
    teamAvatar: {
      flex: 1,
    },
  });
