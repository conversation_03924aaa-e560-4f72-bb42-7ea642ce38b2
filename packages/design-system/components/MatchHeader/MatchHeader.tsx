import React, { useMemo } from 'react';
import { Text, View, StyleProp, ViewStyle } from 'react-native';
import { i18next } from '@scorescast/translations';
import {
  formatMatchTime,
  Match,
  MatchPossibleStatus,
} from '@scorescast/formatters-constants';
import { TeamAvatar } from '../TeamAvatar/TeamAvatar';
import { useTheme } from '../../context/ThemeContext';
import { matchHeaderStyles } from './MatchHeader.styles';
import { useAgentStore } from '@scorescast/http-client';

interface MatchHeaderProps {
  match: Match;
  containerStyle?: StyleProp<ViewStyle>;
}

export const MatchHeader = ({ match, containerStyle }: MatchHeaderProps) => {
  const { usTeamImage } = useAgentStore();
  const { colors, spacings, typography } = useTheme();
  const styles = matchHeaderStyles(colors, spacings, typography);
  const hasPenalties =
    match?.penalty?.homeGoals > 0 ||
    match?.penalty?.awayGoals > 0 ||
    match.status === MatchPossibleStatus.PENS;
  const isHomeTeam = match?.location === 'HOME';

  const computeScore = useMemo(() => {
    const homeScore = match?.fullTime?.homeGoals + match?.extraTime?.homeGoals;
    const awayScore = match?.fullTime?.awayGoals + match?.extraTime?.awayGoals;
    return { homeScore, awayScore };
  }, [match]);

  return (
    <View style={[styles.matchHeader, containerStyle]}>
      <TeamAvatar
        disabled
        containerStyle={styles.teamAvatar}
        team={{
          name: isHomeTeam ? match?.usTeamName : match?.themTeamName,
          image: isHomeTeam ? usTeamImage : match?.themTeamImage,
        }}
      />
      <View style={styles.scoreCard}>
        <View style={styles.score}>
          <Text style={styles.scoreText}>
            {match.status !== MatchPossibleStatus.NS
              ? computeScore.homeScore
              : '-'}
          </Text>
          <Text style={styles.scoreText}>:</Text>
          <Text style={styles.scoreText}>
            {match.status !== MatchPossibleStatus.NS
              ? computeScore.awayScore
              : '-'}
          </Text>
        </View>
        <Text style={styles.time}>
          {formatMatchTime(match.matchTime, match.status)}
        </Text>
        {hasPenalties && (
          <Text style={styles.penaltyText}>
            {i18next.t('MATCH_TIME.PENS_SHORT')} ({match.penalty.homeGoals} -{' '}
            {match.penalty.awayGoals})
          </Text>
        )}
      </View>
      <TeamAvatar
        disabled
        containerStyle={styles.teamAvatar}
        team={{
          name: isHomeTeam ? match?.themTeamName : match?.usTeamName,
          image: isHomeTeam ? match?.themTeamImage : usTeamImage,
        }}
      />
    </View>
  );
};
