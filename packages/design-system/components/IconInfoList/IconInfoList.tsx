import React from 'react';
import { View, StyleProp, ViewStyle, ScrollView } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { iconInfoListStyles } from './IconInfoList.styles';
import { IconInfoListItem } from '../IconInfoListItem/IconInfoListItem';

interface IconInfoListItemData {
  id: number | string;
  image?: string;
  title: string;
  onPress?: () => void;
}

interface IconInfoListProps {
  items: IconInfoListItemData[];
  containerStyle?: StyleProp<ViewStyle>;
  scrollable?: boolean;
}

export const IconInfoList: React.FC<IconInfoListProps> = ({
  items,
  containerStyle,
  scrollable = true,
}) => {
  const { colors, spacings } = useTheme();
  const styles = iconInfoListStyles(colors, spacings);

  const content = (
    <View style={[styles.container, containerStyle]}>
      {items.map((item, index) => (
        <IconInfoListItem
          key={item.id.toString()}
          image={item.image}
          title={item.title}
          onPress={item.onPress}
        />
      ))}
    </View>
  );

  if (scrollable) {
    return (
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
      >
        {content}
      </ScrollView>
    );
  }

  return content;
};
