import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';

export const notificationOptionStyles = (
  colors: ColorStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    text: {
      ...typography['small-semibold'],
      color: colors.inverse900,
    },
  });
