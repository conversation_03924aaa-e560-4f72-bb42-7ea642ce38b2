import React from 'react';
import { View, Text, StyleProp, ViewStyle } from 'react-native';
import Toggle from '../Toggle/Toggle';
import { notificationOptionStyles } from './NotificationOption.styles';
import { useTheme } from '../../context/ThemeContext';

interface NotificationOptionProps {
  title: string;
  value: boolean;
  onToggle: () => void;
  style?: StyleProp<ViewStyle>;
}

export const NotificationOption = ({
  title,
  value,
  onToggle,
  style,
}: NotificationOptionProps) => {
  const { colors, typography } = useTheme();
  const styles = notificationOptionStyles(colors, typography);

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.text}>{title}</Text>
      <Toggle isOn={value} onToggle={onToggle} />
    </View>
  );
};
