import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';

export const confirmBottomSheetStyles = (
  spacings: SpacingStyles,
  colors: ColorStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacings.large,
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.xxxxlarge,
    },
    title: {
      ...typography['large-bold'],
      textAlign: 'center',
      color: colors.inverse900,
    },
    subtitle: {
      ...typography.small,
      color: colors.inverse900,
      paddingVertical: spacings.xxxlarge,
      textAlign: 'center',
    },
    buttonContainer: {
      width: '100%',
    },
  });
