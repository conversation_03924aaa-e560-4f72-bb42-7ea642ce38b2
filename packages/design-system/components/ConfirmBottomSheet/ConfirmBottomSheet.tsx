import React, { forwardRef } from 'react';
import { View, Text } from 'react-native';
import { BottomSheet } from '../BottomSheet/BottomSheet';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useTheme } from '../../context/ThemeContext';
import { Button } from '../Button/Button';
import { confirmBottomSheetStyles } from './ConfirmBottomSheet.styles';

type ConfirmBottomSheetProps = {
  onBtnPress: () => void;
  titleText: string;
  descriptionText: string;
  buttonText: string;
};

export const ConfirmBottomSheet = forwardRef<
  BottomSheetModal,
  ConfirmBottomSheetProps
>(({ onBtnPress, titleText, descriptionText, buttonText }, ref) => {
  const { spacings, colors, typography } = useTheme();
  const styles = confirmBottomSheetStyles(spacings, colors, typography);
  return (
    <BottomSheet ref={ref}>
      <View style={styles.container}>
        <Text style={styles.title}>{titleText}</Text>
        <Text style={styles.subtitle}>{descriptionText}</Text>
        <Button
          secondary
          title={buttonText}
          containerStyle={styles.buttonContainer}
          onPress={() => onBtnPress()}
        />
      </View>
    </BottomSheet>
  );
});

ConfirmBottomSheet.displayName = 'ConfirmBottomSheet';
