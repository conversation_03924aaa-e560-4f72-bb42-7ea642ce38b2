import React, { forwardRef, useCallback } from 'react';
import { SocialLoginType } from '@scorescast/formatters-constants';
import { i18next } from '@scorescast/translations';
import { useAppStore } from '@scorescast/http-client';
import { BottomSheet } from '../BottomSheet/BottomSheet';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { Button } from '../Button/Button';
import { View, Platform } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { socialLoginStyles } from './SocialLogin.styles';
import { IconName } from '../Icon/Icon.types';

type SocialLoginProps = {
  onLogin: (type: typeof SocialLoginType) => void;
};

export const SocialLogin = forwardRef<BottomSheetModal, SocialLoginProps>(
  ({ onLogin }, ref) => {
    const { spacings } = useTheme();
    const styles = socialLoginStyles(spacings);
    const { config } = useAppStore();

    const renderSocialButtons = useCallback(() => {
      // If config is available, use it. Otherwise, show default login methods
      const availableLoginMethods = config?.availableLoginMethods || [
        'google',
        'facebook',
        'apple',
      ];

      return availableLoginMethods.map((socialType: string) => {
        if (socialType === SocialLoginType.APPLE && Platform.OS !== 'ios') {
          return null;
        }
        const iconMap = {
          google: IconName.GOOGLE,
          apple: IconName.APPLE,
          facebook: IconName.FACEBOOK,
        };

        const socialLoginTypeMap = {
          google: SocialLoginType.GOOGLE,
          apple: SocialLoginType.APPLE,
          facebook: SocialLoginType.FACEBOOK,
        };

        return (
          <Button
            key={socialType}
            icon={iconMap[socialType]}
            title={i18next.t(
              `ONBOARDING.SOCIAL_LOGIN.${socialType.toUpperCase()}`
            )}
            tertiary
            containerStyle={styles.buttonContainer}
            onPress={() =>
              onLogin(
                socialLoginTypeMap[
                  socialType
                ] as unknown as typeof SocialLoginType
              )
            }
          />
        );
      });
    }, [config, onLogin]);

    return (
      <BottomSheet ref={ref}>
        <View style={styles.container}>{renderSocialButtons()}</View>
      </BottomSheet>
    );
  }
);

SocialLogin.displayName = 'SocialLogin';
