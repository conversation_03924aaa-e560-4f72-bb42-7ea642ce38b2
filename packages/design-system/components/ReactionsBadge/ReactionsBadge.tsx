import React, { useEffect, useRef } from 'react';
import { Text, Animated } from 'react-native';
import {
  Reaction as ReactionModel,
  REACTIONS,
} from '@scorescast/formatters-constants';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { reactionsBadgeStyles } from './ReactionsBadge.styles';
import { useTheme } from '../../context/ThemeContext';
import { ReactionsBottomSheet } from '../ReactionsBottomSheet/ReactionsBottomSheet';

import { TouchableOpacity } from 'react-native-gesture-handler';
interface ReactionsBadgeProps {
  position: 'left' | 'right';
  reactions: ReactionModel[] | undefined;
}

export const ReactionsBadge: React.FC<ReactionsBadgeProps> = ({
  position,
  reactions,
}) => {
  const { colors, spacings, typography } = useTheme();
  const styles = reactionsBadgeStyles(colors, spacings, typography);
  const opacity = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(0.5)).current;
  const bottomSheetRef = useRef<BottomSheetModal>(null);

  useEffect(() => {
    if (reactions && reactions.length > 0) {
      Animated.parallel([
        Animated.timing(opacity, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.spring(scale, {
          toValue: 1,
          friction: 3,
          tension: 80,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(opacity, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scale, {
          toValue: 0.5,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [reactions]);

  if (!reactions || reactions.length === 0) {
    return null;
  }

  return (
    <>
      <Animated.View
        style={[
          styles.reactionContainer,
          {
            opacity,
            transform: [{ scale }],
            [position === 'left' ? 'left' : 'right']: 10,
          },
        ]}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.row}
          onPress={() => bottomSheetRef.current?.present()}
        >
          {reactions.length > 1 && (
            <Text style={styles.numberOfReactions}>{reactions.length}</Text>
          )}
          {reactions.slice(0, 2).map((reaction) => (
            <Text key={reaction.id} style={styles.reactionText}>
              {
                REACTIONS.find((r) => r.reactionType === reaction.reactionType)
                  ?.emoji
              }
            </Text>
          ))}
        </TouchableOpacity>
      </Animated.View>
      <ReactionsBottomSheet ref={bottomSheetRef} reactions={reactions} />
    </>
  );
};
