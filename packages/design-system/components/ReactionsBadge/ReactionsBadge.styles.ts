import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';
import { SpacingStyles } from '../../theme/spacing';

export const reactionsBadgeStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    reactionContainer: {
      position: 'absolute',
      zIndex: 999,
      bottom: 5,
      backgroundColor: colors.inverse900,
      height: 25,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: spacings.medium,
    },
    row: {
      flexDirection: 'row',
    },
    numberOfReactions: {
      color: colors.inverse100,
      ...typography['xSmall-bold'],
    },
    reactionText: {
      color: colors.inverse100,
      ...typography['xSmall-bold'],
      marginHorizontal: spacings.small,
    },
  });
