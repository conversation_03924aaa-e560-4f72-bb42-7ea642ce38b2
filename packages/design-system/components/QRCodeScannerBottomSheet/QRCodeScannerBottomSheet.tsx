import React, { forwardRef, useCallback } from 'react';
import { View, Text } from 'react-native';
import { i18next } from '@scorescast/translations';
import { TEAM_CODE_LENGTH } from '@scorescast/formatters-constants';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useTheme } from '../../context/ThemeContext';
import { BottomSheet } from '../BottomSheet/BottomSheet';
import { Camera, CameraType } from 'react-native-camera-kit';
import { qrCodeScannerBottomSheetStyles } from './QRCodeScannerBottomSheet.styles';
import { IconName } from '../Icon/Icon.types';
import { Icon } from '../Icon/Icon';

type QRCodeScannerBottomSheetProps = {
  onQRCodeScanned: (code: string) => void;
};

export const QRCodeScannerBottomSheet = forwardRef<
  BottomSheetModal,
  QRCodeScannerBottomSheetProps
>(({ onQRCodeScanned }, ref) => {
  const { spacings, colors, typography } = useTheme();
  const styles = qrCodeScannerBottomSheetStyles(spacings, colors, typography);

  const qrCodeScanned = useCallback(
    (event: any) => {
      if (event?.nativeEvent?.codeStringValue.length === TEAM_CODE_LENGTH) {
        onQRCodeScanned(event?.nativeEvent?.codeStringValue);
      }
    },
    [onQRCodeScanned]
  );

  return (
    <BottomSheet ref={ref}>
      <View style={styles.container}>
        <Text style={styles.title}>{i18next.t('QR_CODE_SCANNER.TITLE')}</Text>
        <Text style={styles.subtitle}>
          {i18next.t('QR_CODE_SCANNER.DESCRIPTION')}
        </Text>
        <View style={styles.cameraContainer}>
          <Camera
            scanBarcode={true}
            onReadCode={qrCodeScanned}
            cameraType={CameraType.Back}
            showFrame={false}
            laserColor={'transparent'}
            frameColor={'transparent'}
            ratioOverlayColor={'transparent'}
            style={styles.camera}
          />
          <Icon
            name={IconName.FRAME}
            isPng
            width={300}
            height={300}
            styles={styles.frame}
          />
        </View>
      </View>
    </BottomSheet>
  );
});

QRCodeScannerBottomSheet.displayName = 'QRCodeScannerBottomSheet';
