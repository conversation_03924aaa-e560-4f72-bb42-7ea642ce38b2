import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';

export const qrCodeScannerBottomSheetStyles = (
  spacings: SpacingStyles,
  colors: ColorStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacings.large,
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.xxxxlarge,
    },
    title: {
      ...typography['large-bold'],
      textAlign: 'center',
      color: colors.inverse900,
    },
    subtitle: {
      ...typography.small,
      color: colors.inverse900,
      paddingVertical: spacings.xxxlarge,
      textAlign: 'center',
    },
    cameraContainer: {
      width: 290,
      height: 290,
      alignSelf: 'center',
    },
    camera: {
      position: 'absolute',
      top: 10,
      left: 10,
      right: 0,
      bottom: 0,
    },
    frame: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
  });
