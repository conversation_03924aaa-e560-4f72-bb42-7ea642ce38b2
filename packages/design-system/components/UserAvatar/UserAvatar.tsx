import React from 'react';
import { View, Text, StyleProp, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import {
  getUserColorIndex,
  Reaction,
  REACTIONS,
} from '@scorescast/formatters-constants';
import { userAvatarStyles } from './UserAvatar.styles';

interface UserAvatarProps {
  firstName: string;
  lastName: string;
  email?: string;
  withName?: boolean;
  reaction?: Reaction;
  sizeStyleContainer?: StyleProp<ViewStyle>;
  sizeStyleText?: StyleProp<TextStyle>;
}

export const UserAvatar = ({
  firstName,
  lastName,
  email,
  withName,
  reaction,
  sizeStyleContainer,
  sizeStyleText,
}: UserAvatarProps) => {
  const { spacings, typography, colors, userAvatarsColors } = useTheme();
  const styles = userAvatarStyles(colors, spacings, typography);
  const userAvatarColors = userAvatarsColors[getUserColorIndex(lastName)];

  return (
    <>
      <View
        style={[
          styles.container,
          sizeStyleContainer,
          { backgroundColor: userAvatarColors.backgroundColor },
        ]}
      >
        <Text
          style={[
            styles.initials,
            sizeStyleText,
            { color: userAvatarColors.textColor },
          ]}
        >
          {firstName.charAt(0)}
          {lastName.charAt(0)}
        </Text>
        {reaction && (
          <View style={styles.reactionContainer}>
            <Text style={styles.reactionText}>
              {
                REACTIONS.find((r) => r.reactionType === reaction.reactionType)
                  ?.emoji
              }
            </Text>
          </View>
        )}
      </View>
      {withName && (
        <Text style={styles.name}>
          {firstName} {lastName}
        </Text>
      )}
      {email && <Text style={styles.email}>{email}</Text>}
    </>
  );
};
