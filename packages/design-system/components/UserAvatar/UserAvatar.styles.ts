import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';
import { ColorStyles } from '../../theme/colors.light';

export const userAvatarStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      height: 65,
      width: 65,
      borderRadius: 32,
      alignItems: 'center',
      justifyContent: 'center',
    },
    initials: {
      ...typography['medium-bold'],
    },
    name: {
      color: colors.inverse900,
      ...typography['medium-semibold'],
      paddingTop: spacings.large,
      textAlign: 'center',
    },
    email: {
      ...typography['small'],
      color: colors.inverse850,
      textAlign: 'center',
      marginTop: spacings.medium,
    },
    reactionContainer: {
      height: 24,
      width: 24,
      borderRadius: 12,
      backgroundColor: colors.inverse900,
      alignItems: 'center',
      justifyContent: 'center',
      position: 'absolute',
      bottom: 0,
      right: 0,
    },
    reactionText: {
      ...typography['xxSmall-bold'],
      color: colors.inverse900,
    },
  });
