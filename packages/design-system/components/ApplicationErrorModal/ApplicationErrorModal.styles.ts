import {
  ColorStyles,
  SpacingStyles,
  TypographyStyles,
} from '@scorescast/design-system';
import { StyleSheet } from 'react-native';

export const applicationErrorModalStyles = (
  colors: typeof ColorStyles,
  spacings: typeof SpacingStyles,
  typography: typeof TypographyStyles
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.neutral900,
      paddingHorizontal: spacings.large,
    },
    title: {
      ...typography['large-bold'],
      color: colors.inverse900,
      textAlign: 'center',
    },
    description: {
      ...typography['medium-regular'],
      color: colors.inverse900,
      textAlign: 'center',
    },
    button: {
      marginTop: spacings.xxxxlarge,
      width: '100%',
    },
  });
