import React from 'react';
import { Modal, StyleProp, View, ViewStyle } from 'react-native';
import { Error, useTheme, Button } from '@scorescast/design-system';
import { applicationErrorModalStyles } from './ApplicationErrorModal.styles';
import { LottiesName } from '../Icon/Icon.types';

interface ApplicationErrorModalProps {
  visible: boolean | null;
  lottie: LottiesName;
  title: string;
  description: string;
  buttonTitle: string;
  customLottieStyle?: StyleProp<ViewStyle>;
  callbackFc: () => void;
}

export const ApplicationErrorModal = ({
  visible,
  lottie,
  title,
  description,
  buttonTitle,
  customLottieStyle,
  callbackFc,
}: ApplicationErrorModalProps) => {
  const { colors, spacings, typography } = useTheme();
  const styles = applicationErrorModalStyles(colors, spacings, typography);

  return (
    <Modal visible={visible!} animationType="fade">
      <View style={styles.container}>
        <Error
          lottie={lottie}
          title={title}
          description={description}
          loop={true}
          customLottieStyle={customLottieStyle}
        />
        <Button
          secondary
          containerStyle={styles.button}
          title={buttonTitle}
          onPress={callbackFc}
        />
      </View>
    </Modal>
  );
};
