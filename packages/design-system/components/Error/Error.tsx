import React from 'react';
import { View, Text, Dimensions, StyleProp, ViewStyle } from 'react-native';
import LottieView from 'lottie-react-native';
import { useTheme } from '../../context/ThemeContext';
import { LottiesName } from '../Icon/Icon.types';

import { errorStyles } from './Error.styles';
interface ErrorProps {
  lottie: LottiesName;
  title: string;
  description: string;
  loop?: boolean;
  customLottieStyle?: StyleProp<ViewStyle>;
}

export const Error = ({
  lottie,
  title,
  description,
  loop = false,
  customLottieStyle,
}: ErrorProps) => {
  const { colors, spacings, typography } = useTheme();
  const styles = errorStyles(colors, spacings, typography);

  return (
    <View style={styles.container}>
      <LottieView
        source={lottie}
        autoPlay
        loop={loop}
        style={[
          styles.lottie,
          { width: Dimensions.get('window').width * 0.9 },
          customLottieStyle,
        ]}
      />
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.description}>{description}</Text>
    </View>
  );
};
