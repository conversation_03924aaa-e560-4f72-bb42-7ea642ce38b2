import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const errorStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
      marginHorizontal: spacings.large,
    },
    lottie: {
      aspectRatio: 1,
    },
    title: {
      ...typography['large-bold'],
      color: colors.inverse900,
      marginTop: -spacings.xxlarge,
      marginBottom: spacings.medium,
      textAlign: 'center',
    },
    description: {
      ...typography['xSmall-semibold'],
      color: colors.inverse900,
      marginBottom: spacings.small,
      textAlign: 'center',
    },
  });
