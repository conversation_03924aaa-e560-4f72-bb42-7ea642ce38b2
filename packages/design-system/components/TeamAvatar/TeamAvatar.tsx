import React from 'react';
import {
  Image,
  Text,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
  TextStyle,
  ImageStyle,
} from 'react-native';
import { Team, getTeamImageIndex } from '@scorescast/formatters-constants';
import { teamAvatarStyles } from './TeamAvatar.styles';
import { useTheme } from '../../context/ThemeContext';

interface TeamAvatarProps {
  team: Team;
  disabled?: boolean;
  isSelected?: boolean;
  onPress?: (teamUniqueId?: string) => void;
  containerStyle?: StyleProp<ViewStyle>;
  imageStyle?: StyleProp<ImageStyle>;
  nameStyle?: StyleProp<TextStyle>;
}

export const TeamAvatar = ({
  team,
  onPress,
  disabled,
  isSelected,
  containerStyle,
  imageStyle,
  nameStyle,
}: TeamAvatarProps) => {
  const { colors, spacings, typography, icons } = useTheme();
  const styles = teamAvatarStyles(colors, spacings, typography);

  return (
    <TouchableOpacity
      disabled={disabled}
      activeOpacity={0.8}
      style={[
        styles.container,
        containerStyle,
        isSelected === false && styles.notSelectedContainer,
      ]}
      onPress={() => onPress && onPress(team?.teamUniqueId)}
    >
      <Image
        source={{
          uri:
            team.image ??
            Image.resolveAssetSource(
              icons[`defaultTeamLogo${getTeamImageIndex(team.name)}`]
            ).uri,
        }}
        style={[
          styles.image,
          imageStyle,
          isSelected && styles.selectedContainer,
        ]}
      />
      <Text
        numberOfLines={1}
        style={[styles.name, nameStyle, isSelected && styles.selectedName]}
      >
        {team.name}
      </Text>
    </TouchableOpacity>
  );
};
