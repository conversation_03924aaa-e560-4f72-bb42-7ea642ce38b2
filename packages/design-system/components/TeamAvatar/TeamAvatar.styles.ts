import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';
import { ColorStyles } from '../../theme/colors.light';

export const teamAvatarStyles = (
  colors: ColorStyles,
  spacing: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
    },
    image: {
      width: 60,
      height: 60,
      borderRadius: 10,
      resizeMode: 'contain',
    },
    name: {
      ...typography['small-semibold'],
      color: colors.inverse900,
      paddingTop: spacing.medium,
      flexShrink: 1,
      textAlign: 'center',
    },
    selectedContainer: {
      borderWidth: 3,
      borderColor: colors.neutral100,
    },
    selectedName: {
      ...typography['small-bold'],
    },
    notSelectedContainer: {
      opacity: 0.4,
    },
  });
