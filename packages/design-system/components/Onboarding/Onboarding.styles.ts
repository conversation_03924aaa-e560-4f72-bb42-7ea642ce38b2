import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const onboardingStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    background: {
      flex: 1,
      backgroundColor: colors.neutral900,
      paddingHorizontal: spacings.xxxlarge,
    },
    container: {
      flex: 1,
      justifyContent: 'center',
      zIndex: 2,
    },
    overlay: {
      zIndex: 1,
      flex: 1,
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: colors.neutral900,
    },
    logo: {
      alignSelf: 'center',
    },
    imageContainer: {
      width: 163,
      height: 268,
      marginTop: spacings.xxxxxlarge,
      alignSelf: 'center',
    },
    titleContainer: {
      height: 220,
      justifyContent: 'center',
    },
    title: {
      color: colors.inverse900,
      textAlign: 'center',
      paddingTop: spacings.xxxxlarge,
      paddingBottom: spacings.xxlarge,
      paddingHorizontal: spacings.xxxxxlarge,
      ...typography['xLarge-bold'],
    },
    stepper: {
      paddingTop: spacings.xxlarge,
      height: 5,
    },
  });
