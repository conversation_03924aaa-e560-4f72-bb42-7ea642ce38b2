import React, { useMemo } from 'react';
import {
  ImageBackground,
  ImageSourcePropType,
  View,
  Text,
  Dimensions,
} from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { onboardingStyles } from './Onboarding.styles';
import { IconName } from '../Icon/Icon.types';
import { Icon } from '../Icon/Icon';
import { Button } from '../Button/Button';
import Stepper from '../Stepper/Stepper';

interface OnboardingProps {
  totalSteps: number;
  currentStep: number;
  title: string;
  buttonTitle: string;
  image?: IconName;
  withImage?: boolean;
  withStepper?: boolean;
  backgroundImage: IconName;
  onPress: () => void;
}

export const Onboarding: React.FC<OnboardingProps> = ({
  totalSteps,
  currentStep,
  title,
  buttonTitle,
  image,
  withImage,
  withStepper,
  backgroundImage,
  onPress,
}) => {
  const { colors, spacings, typography, icons } = useTheme();
  const styles = onboardingStyles(colors, spacings, typography);

  const calculateOverlayOpacity = useMemo(() => {
    const first = 0.55;
    const second = 0.85;
    const last = 0.95;

    if (currentStep === 0) {
      return first;
    } else if (currentStep === 1) {
      return second;
    } else if (currentStep === totalSteps + 1) {
      return first;
    }

    const remainingSteps = totalSteps - 1;
    const stepSize = (last - second) / remainingSteps;

    return Math.min(last, second + (currentStep - 1) * stepSize);
  }, [currentStep, totalSteps]);

  return (
    <ImageBackground
      source={icons[backgroundImage] as ImageSourcePropType}
      style={styles.background}
      resizeMode="cover"
    >
      <View style={[styles.overlay, { opacity: calculateOverlayOpacity }]} />
      <View style={styles.container}>
        <Icon
          isPng
          name={IconName.LOGO}
          width={300}
          height={83}
          styles={styles.logo}
        />
        <View style={styles.imageContainer}>
          {withImage && (
            <Icon
              isPng
              name={image}
              width={Dimensions.get('window').width - 50}
              height={310}
              styles={{ alignSelf: 'center' }}
            />
          )}
        </View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{title}</Text>
        </View>
        <Button secondary onPress={onPress} title={buttonTitle} />
        <View style={styles.stepper}>
          {withStepper && (
            <Stepper totalSteps={totalSteps} currentStep={currentStep} />
          )}
        </View>
      </View>
    </ImageBackground>
  );
};

export default Onboarding;
