import React from 'react';
import { Text, TouchableOpacity, StyleProp, ViewStyle } from 'react-native';
import LottieView from 'lottie-react-native';
import { i18next } from '@scorescast/translations';
import { Lotties } from '../Icon/Icons';
import { useTheme } from '../../context/ThemeContext';
import { premiumBadgeStyles } from './PremiumBadge.styles';

type PremiumBadgeProps = {
  onPress: () => void;
  containerStyle?: StyleProp<ViewStyle>;
};

export const PremiumBadge = ({
  onPress,
  containerStyle,
}: PremiumBadgeProps) => {
  const { colors, spacings, typography } = useTheme();
  const styles = premiumBadgeStyles(colors, spacings, typography);
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={[styles.container, containerStyle]}
      onPress={onPress}
    >
      <LottieView
        source={Lotties.premium}
        autoPlay
        loop
        style={styles.lottie}
      />
      <Text style={styles.text}>{i18next.t('PREMIUM_BADGE.TITLE')}</Text>
    </TouchableOpacity>
  );
};
