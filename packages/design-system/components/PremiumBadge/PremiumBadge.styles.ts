import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const premiumBadgeStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacings.medium,
      borderRadius: 24,
      justifyContent: 'center',
      backgroundColor: colors.inverse900,
    },
    lottie: {
      width: 40,
      height: 50,
    },
    text: {
      ...typography['xSmall-bold'],
      color: colors.inverse100,
      marginHorizontal: spacings.small,
    },
  });
