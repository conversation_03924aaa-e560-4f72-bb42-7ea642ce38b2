import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';

export const selectStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      marginTop: spacings.xxxxlarge,
    },
    label: {
      color: colors.inverse900,
      ...typography['medium-bold'],
      marginBottom: spacings.xlarge,
    },
    selectContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.neutral700,
      borderRadius: 10,
      height: 70,
      justifyContent: 'center',
      shadowColor: colors.shadowColor,
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    selectContent: {
      flexDirection: 'row',
      alignItems: 'center',
      width: '100%',
      justifyContent: 'space-between',
    },
    addMemberButton: {
      flex: 1,
      borderRadius: 5,
      height: 40,
    },
    addMemberButtonText: {
      ...typography['small-semibold'],
    },
    line: {
      flex: 1,
      height: 3,
      backgroundColor: colors.inverse900,
      marginRight: spacings.large,
      marginLeft: spacings.ultralarge,
    },
    plus: {
      color: colors.inverse900,
      ...typography['small-bold'],
      marginRight: spacings.ultralarge,
    },
    selectedContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      width: '100%',
      backgroundColor: colors.neutral100,
      borderRadius: 10,
      paddingHorizontal: spacings.large,
      height: 70,
      paddingVertical: spacings.medium,
    },
    selectedCircle: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: colors.inverse100,
      justifyContent: 'center',
      alignItems: 'center',
    },
    selectedCircleText: {
      color: colors.inverse900,
      ...typography['small-bold'],
    },
    selectedText: {
      color: colors.inverse100,
      ...typography['small-bold'],
      marginLeft: spacings.medium,
    },
    playersListContainer: {
      paddingVertical: spacings.xxxlarge,
    },
    player: {
      paddingHorizontal: spacings.large,
      paddingBottom: spacings.xxlarge,
      alignItems: 'center',
      flex: 1,
    },
    playerNumber: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: colors.inverse900,
      justifyContent: 'center',
      alignItems: 'center',
    },
    playerNumberText: {
      ...typography['small-bold'],
      color: colors.inverse100,
    },
    playerName: {
      ...typography['small-bold'],
      color: colors.inverse900,
      marginTop: spacings.medium,
      textAlign: 'center',
    },
  });
