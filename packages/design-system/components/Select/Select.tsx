import React, { forwardRef, useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { BottomSheet } from '../BottomSheet/BottomSheet';
import { useTheme } from '../../context/ThemeContext';
import { selectStyles } from './Select.styles';

type SelectProps = {
  onItemSelect: (selectedItem: {
    id: string;
    name: string;
    number: string;
  }) => void;
  onOpen: () => void;
  list: Array<{ id: string; name: string; number: string }>;
  selectLabel: string;
  defaultValue?: string; // The id of the default selected item
};

export const Select = forwardRef<BottomSheetModal, SelectProps>(
  ({ onItemSelect, onOpen, list, selectLabel, defaultValue }, ref) => {
    const { spacings, colors, typography } = useTheme();
    const styles = selectStyles(colors, spacings, typography);

    // Initialize selected state with default value if provided
    const [selected, setSelected] = useState<{
      id: string;
      name: string;
      number: string;
    } | null>(() => {
      if (defaultValue) {
        const defaultItem = list.find((item) => item.id === defaultValue);
        return defaultItem || null;
      }
      return null;
    });

    // Update selected state when defaultValue or list changes
    useEffect(() => {
      if (defaultValue) {
        const defaultItem = list.find((item) => item.id === defaultValue);
        if (defaultItem && (!selected || selected.id !== defaultValue)) {
          setSelected(defaultItem);
        }
      }
    }, [defaultValue, list, selected]);

    return (
      <View style={styles.container}>
        <Text style={styles.label}>{selectLabel}</Text>
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.selectContainer}
          onPress={onOpen}
        >
          {selected ? (
            <View style={styles.selectedContainer}>
              <View style={styles.selectedCircle}>
                <Text style={styles.selectedCircleText}>{selected.number}</Text>
              </View>
              <Text style={styles.selectedText}>{selected.name}</Text>
            </View>
          ) : (
            <View style={styles.selectContent}>
              <View style={styles.line} />
              <Text style={styles.plus}>+</Text>
            </View>
          )}
        </TouchableOpacity>
        <BottomSheet ref={ref}>
          <FlatList
            bounces={false}
            data={list}
            numColumns={3}
            style={styles.playersListContainer}
            keyExtractor={(item) => `${item.id}_${item.name}_${Math.random()}`}
            renderItem={({ item }) => (
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => {
                  setSelected(item);
                  onItemSelect(item);
                }}
                style={styles.player}
              >
                <View style={styles.playerNumber}>
                  <Text style={styles.playerNumberText}>{item.number}</Text>
                </View>
                <Text style={styles.playerName}>{item.name}</Text>
              </TouchableOpacity>
            )}
          />
        </BottomSheet>
      </View>
    );
  }
);

Select.displayName = 'Select';

export default Select;
