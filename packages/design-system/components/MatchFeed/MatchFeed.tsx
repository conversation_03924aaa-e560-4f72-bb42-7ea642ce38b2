import { Match } from '@scorescast/formatters-constants';
import { i18next } from '@scorescast/translations';
import {
  FEED_PERIOD_LABELS,
  FEED_PERIOD_ORDER,
} from '@scorescast/formatters-constants';
import React, { useCallback, useRef, useState } from 'react';
import { FlatList } from 'react-native';
import LottieView from 'lottie-react-native';
import { FeedItem } from '../FeedItem/FeedItem';
import { Lotties } from '../Icon/Icons';
import { MatchInfoCard } from '../MatchInfoCard/MatchInfoCard';
import { matchFeedStyles } from './MatchFeed.styles';
import { useTheme } from '../../context/ThemeContext';

interface MatchFeedProps {
  match: Match;
  refetchMatch: () => void;
  reactEvent: (event: {
    eventId: number;
    reactionType: string;
  }) => Promise<void>;
}

export const MatchFeed: React.FC<MatchFeedProps> = ({
  match,
  refetchMatch,
  reactEvent,
}) => {
  const { spacings } = useTheme();
  const styles = matchFeedStyles(spacings);
  const [shouldDisplayAnimation, setShouldDisplayAnimation] = useState(false);
  const [reactionType, setReactionType] = useState<string | null>(null);
  const lottieRef = useRef<LottieView>(null);

  const handleReactionPress = useCallback(
    (type: string, id: number) => {
      reactEvent({ eventId: id, reactionType: type }).then(() => {
        if (lottieRef?.current) {
          lottieRef.current.reset();
        }
        setReactionType(type.toLowerCase());
        setShouldDisplayAnimation(true);
        setTimeout(async () => {
          if (lottieRef?.current) {
            lottieRef.current.play();
          }
        }, 10);
        refetchMatch();
      });
    },
    [lottieRef]
  );

  const formattedFeed = FEED_PERIOD_ORDER.filter(
    (period) => match?.feeds[period]
  ).reduce(
    (acc, period) => {
      acc.push({
        title: FEED_PERIOD_LABELS[period],
        isHeader: true,
        period: period,
      });
      acc.push(
        ...(match?.feeds[period]?.map((event) => ({ ...event, period })) ?? [])
      );
      return acc;
    },
    [] as Array<{ title: string; isHeader?: boolean; period?: string }>
  );

  if (!formattedFeed || (formattedFeed && formattedFeed.length === 0)) {
    return (
      <MatchInfoCard
        centerText={i18next.t('MATCH_FEED.NO_EVENTS')}
        containerStyle={styles.matchDetailsContainer}
      />
    );
  }

  return (
    <>
      <FlatList
        data={formattedFeed}
        showsVerticalScrollIndicator={false}
        bounces={false}
        keyExtractor={(_, index) => index.toString()}
        renderItem={({ item }) => (
          <FeedItem item={item} onReactionPress={handleReactionPress} />
        )}
      />
      <LottieView
        ref={lottieRef}
        source={Lotties[reactionType ?? 'clap']}
        loop={false}
        autoPlay={false}
        onAnimationFinish={() => {
          setShouldDisplayAnimation(false);
        }}
        style={
          shouldDisplayAnimation && reactionType
            ? styles.lottie
            : styles.hiddenLottie
        }
      />
    </>
  );
};
