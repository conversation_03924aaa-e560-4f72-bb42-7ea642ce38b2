import { i18next } from '@scorescast/translations';
import React from 'react';
import { View } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { MatchInfoCard } from '../MatchInfoCard/MatchInfoCard';
import { matchStatsStyles } from './MatchStats.styles';

export const MatchStats = () => {
  const { spacings } = useTheme();
  const styles = matchStatsStyles(spacings);
  return (
    <View>
      <MatchInfoCard
        centerText={i18next.t('MATCH_STATS.NOT_AVAILABLE')}
        containerStyle={styles.matchDetailsContainer}
      />
    </View>
  );
};
