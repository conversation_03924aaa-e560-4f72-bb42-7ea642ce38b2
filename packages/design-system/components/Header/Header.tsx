import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Icon } from '../Icon/Icon';
import { IconName } from '../Icon/Icon.types';
import { headerStyles } from './Header.styles';
import { useTheme } from '../../context/ThemeContext';

interface HeaderProps {
  title: string;
  withBackButton?: boolean;
  hasFeed?: boolean;
  onBackButtonPress?: () => void;
  onFeedPress?: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  title,
  withBackButton,
  hasFeed,
  onBackButtonPress,
  onFeedPress,
}) => {
  const { colors, typography } = useTheme();
  const styles = headerStyles(colors, typography);
  return (
    <View style={styles.container}>
      {withBackButton && (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={onBackButtonPress}
          style={styles.backButton}
        >
          <Icon name={IconName.ARROW_LEFT} height={21} width={24} isPng />
        </TouchableOpacity>
      )}
      {title && <Text style={styles.title}>{title}</Text>}
      {withBackButton && !hasFeed && <View style={styles.backButton} />}
      {hasFeed && (
        <TouchableOpacity activeOpacity={0.8} onPress={onFeedPress}>
          <Text style={styles.feedText}>FEED</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};
