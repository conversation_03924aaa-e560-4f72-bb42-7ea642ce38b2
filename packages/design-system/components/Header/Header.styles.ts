import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';

export const headerStyles = (
  colors: ColorStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    backButton: {
      width: 24,
      height: 21,
    },
    feedText: {
      ...typography['xSmall-bold'],
      color: colors.inverse900,
    },
    title: {
      ...typography['large-bold'],
      color: colors.inverse900,
    },
  });
