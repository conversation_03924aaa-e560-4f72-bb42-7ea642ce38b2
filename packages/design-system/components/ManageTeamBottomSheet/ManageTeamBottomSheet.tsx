import { i18next } from '@scorescast/translations';
import { Team } from '@scorescast/formatters-constants';
import React, { forwardRef } from 'react';
import { Text, Image, View } from 'react-native';
import { BottomSheet } from '../BottomSheet/BottomSheet';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useTheme } from '../../context/ThemeContext';
import { Button } from '../Button/Button';
import { manageTeamBottomSheetStyles } from './ManageTeamBottomSheet.styles';
import Toggle from '../Toggle/Toggle';

interface TeamWithNotificationStatus extends Team {
  isSubscribedForNotifications: boolean;
}

export const ManageTeamBottomSheet = forwardRef<
  BottomSheetModal,
  {
    team: TeamWithNotificationStatus;
    onToggleNotifications: () => void;
    onShareTeam: () => void;
    onDeleteTeam: () => void;
  }
>(({ team, onToggleNotifications, onShareTeam, onDeleteTeam }, ref) => {
  const { colors, spacings, typography } = useTheme();
  const styles = manageTeamBottomSheetStyles(colors, spacings, typography);

  if (!team) return null;

  return (
    <BottomSheet ref={ref}>
      <View style={styles.container}>
        <Text style={styles.title}>{team?.name}</Text>
        <Image
          source={{
            uri: team?.image,
          }}
          style={styles.image}
        />
        <View style={styles.notificationContainer}>
          <Text style={styles.notificationText}>
            {i18next.t('FOLLOWING.MANAGE_TEAM.NOTIFICATIONS')}
          </Text>
          <Toggle
            isOn={team?.isSubscribedForNotifications}
            onToggle={onToggleNotifications}
          />
        </View>
        <Button
          secondary
          title={i18next.t('FOLLOWING.MANAGE_TEAM.SHARE')}
          onPress={onShareTeam}
          containerStyle={styles.button}
        />
        <Text onPress={onDeleteTeam} style={styles.linkText}>
          {i18next.t('FOLLOWING.MANAGE_TEAM.DELETE')}
        </Text>
      </View>
    </BottomSheet>
  );
});

ManageTeamBottomSheet.displayName = 'ManageTeamBottomSheet';
