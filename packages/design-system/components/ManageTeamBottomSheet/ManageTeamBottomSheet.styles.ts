import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';
import { ColorStyles } from '../../theme/colors.light';

export const manageTeamBottomSheetStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.xxxxlarge,
      paddingHorizontal: spacings.xxlarge,
    },
    title: {
      ...typography['large-bold'],
      color: colors.inverse900,
      alignSelf: 'center',
    },
    image: {
      width: 220,
      height: 220,
      borderRadius: 10,
      resizeMode: 'contain',
      alignSelf: 'center',
      overflow: 'hidden',
      marginVertical: spacings.xxlarge,
    },
    notificationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: spacings.xxlarge,
    },
    notificationText: {
      ...typography['small'],
      color: colors.inverse900,
      marginRight: spacings.ultralarge,
    },
    button: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    linkText: {
      ...typography['small'],
      color: colors.inverse900,
      alignSelf: 'center',
      marginTop: spacings.xlarge,
      textDecorationLine: 'underline',
    },
  });
