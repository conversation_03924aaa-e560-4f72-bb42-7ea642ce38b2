import React, { useMemo } from 'react';
import LinearGradient from 'react-native-linear-gradient';
import { formatMatchDate, Match } from '@scorescast/formatters-constants';
import { Text, TouchableOpacity } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { matchCardStyles } from './MatchCard.styles';
import { Separator } from '../Separator/Separator';
import { MatchHeader } from '../MatchHeader/MatchHeader';
import { MatchPossibleStatus } from '@scorescast/formatters-constants/src/constants/match';
import { View } from 'react-native';

interface MatchCardProps {
  match: Match;
  onPress: (match: Match) => void;
}

export const MatchCard = ({ match, onPress }: MatchCardProps) => {
  const { colors, spacings, typography, gradientColors } = useTheme();
  const styles = matchCardStyles(colors, spacings, typography);

  const getGradientColors = useMemo(() => {
    if (match.status === MatchPossibleStatus.END) {
      return gradientColors.gradientEnd;
    }
    if (
      ![MatchPossibleStatus.NS, MatchPossibleStatus.END].includes(match.status)
    ) {
      return gradientColors.gradientLive;
    }
    return gradientColors.gradientUpcoming;
  }, [match.status, gradientColors]);

  return (
    <View style={styles.gradientContainer}>
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={getGradientColors}
        style={styles.gradient}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.container}
          onPress={() => onPress(match)}
        >
          <MatchHeader match={match} />
          <Separator containerStyle={styles.separator} />
          <Text style={styles.time}>
            {formatMatchDate(match.matchTime, match.status)}
          </Text>
        </TouchableOpacity>
      </LinearGradient>
    </View>
  );
};
