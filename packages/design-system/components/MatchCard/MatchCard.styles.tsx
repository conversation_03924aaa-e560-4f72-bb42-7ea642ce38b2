import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const matchCardStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    gradientContainer: {
      backgroundColor: 'transparent',
      shadowColor: colors.shadowColor,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.1,
      shadowRadius: 5,
      elevation: 8,
    },
    container: {
      flex: 1,
      paddingVertical: spacings.xxlarge,
      paddingHorizontal: spacings.xxlarge,
    },
    gradient: {
      borderRadius: 20,
      marginVertical: spacings.large,
    },
    matchTimeInfo: {
      ...typography['xSmall'],
      color: colors.inverse900,
      textAlign: 'center',
    },
    separator: {
      marginVertical: spacings.large,
    },
    time: {
      ...typography['xSmall'],
      color: colors.inverse900,
      textAlign: 'center',
      marginTop: spacings.medium,
    },
  });
