import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';

export const matchHeaderLightStyles = (spacings: SpacingStyles) =>
  StyleSheet.create({
    matchHeader: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: spacings.xxxlarge,
      paddingHorizontal: spacings.large,
      borderRadius: 12,
    },
    teamContainer: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    frontTeam: {
      transform: [{ scaleX: 1.2 }, { scaleY: 1.2 }],
    },
    backTeam: {
      position: 'absolute',
      right: -100,
      top: -20,
      opacity: 0.5,
      transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }],
    },
  });
