import React, { useMemo } from 'react';
import { View, StyleProp, ViewStyle } from 'react-native';
import {
  Match,
  AgentEventSideSelection,
} from '@scorescast/formatters-constants';
import { TeamAvatar } from '../TeamAvatar/TeamAvatar';
import { useTheme } from '../../context/ThemeContext';
import { matchHeaderLightStyles } from './MatchHeaderLight.styles';
import { useAgentStore } from '@scorescast/http-client';

interface MatchHeaderProps {
  match: Match;
  selectedTeam: { value: string; id: string };
  containerStyle?: StyleProp<ViewStyle>;
}

export const MatchHeaderLight = ({
  match,
  containerStyle,
  selectedTeam,
}: MatchHeaderProps) => {
  const { usTeamImage } = useAgentStore();
  const { spacings } = useTheme();
  const styles = matchHeaderLightStyles(spacings);

  const computeScore = useMemo(() => {
    const homeScore = match?.fullTime?.homeGoals + match?.extraTime?.homeGoals;
    const awayScore = match?.fullTime?.awayGoals + match?.extraTime?.awayGoals;
    return { homeScore, awayScore };
  }, [match]);

  return (
    <View style={[styles.matchHeader, containerStyle]}>
      <View style={styles.teamContainer}>
        <View style={styles.frontTeam}>
          <TeamAvatar
            disabled
            team={{
              name:
                selectedTeam.id === AgentEventSideSelection.US
                  ? match.usTeamName
                  : match.themTeamName,
              image:
                selectedTeam.id === AgentEventSideSelection.US
                  ? usTeamImage
                  : match?.themTeamImage,
            }}
          />
        </View>
        <View style={styles.backTeam}>
          <TeamAvatar
            disabled
            team={{
              name:
                selectedTeam.id !== AgentEventSideSelection.THEM
                  ? match.themTeamName
                  : match.usTeamName,
              image:
                selectedTeam.id === AgentEventSideSelection.US
                  ? match?.themTeamImage
                  : usTeamImage,
            }}
          />
        </View>
      </View>
    </View>
  );
};
