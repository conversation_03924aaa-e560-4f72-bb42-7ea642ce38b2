import React from 'react';
import { Svg } from 'react-native-svg';
import { IconsDark, IconsLight } from './Icons';
import {
  ImageSourcePropType,
  ImageStyle,
  StyleProp,
  ViewStyle,
  Image,
  Appearance,
} from 'react-native';
import { IconName as types } from './Icon.types';

export type Icons = types;

export type IconProps = {
  name?: Icons;
  color?: string;
  width?: number | string;
  height?: number | string;
  styles?: StyleProp<ViewStyle> | StyleProp<ImageStyle>;
  viewBox?: string;
  isPng?: boolean;
  isWebImage?: boolean;
  webImageUrl?: string;
};

export const Icon: React.FC<IconProps> = ({
  name,
  color = 'transparent',
  width = 24,
  height = 24,
  styles,
  viewBox,
  isPng = false,
  isWebImage = false,
  webImageUrl,
}) => {
  const IconSource =
    Appearance.getColorScheme() === 'dark'
      ? IconsDark[name!]
      : IconsLight[name!];

  if (isWebImage && webImageUrl) {
    return (
      <Image
        source={{ uri: webImageUrl }}
        style={[styles, { width, height, resizeMode: 'contain' }] as ImageStyle}
      />
    );
  }

  if (isPng) {
    return (
      <Image
        source={IconSource as ImageSourcePropType}
        style={[styles, { width, height, resizeMode: 'contain' }] as ImageStyle}
      />
    );
  }

  return (
    <Svg style={styles && styles} viewBox={viewBox}>
      {IconSource && <IconSource width={width} height={height} fill={color} />}
    </Svg>
  );
};
