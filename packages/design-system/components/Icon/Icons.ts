/* eslint-disable @typescript-eslint/no-require-imports */
import { SvgProps } from 'react-native-svg';
import { IconName, LottiesName } from './Icon.types';
import { Appearance } from 'react-native';

type IconPaths = {
  [key in IconName]: React.FC<SvgProps>;
};

export const IconsDark: IconPaths = {
  logo: require('../../assets/dark/png/logo.png'),
  backgroundImage1: require('../../assets/dark/jpg/image-background-0.jpg'),
  backgroundImage2: require('../../assets/dark/jpg/image-background-1.jpg'),
  backgroundImage3: require('../../assets/dark/jpg/image-background-2.jpg'),
  backgroundImage4: require('../../assets/dark/jpg/image-background-3.jpg'),
  backgroundImage5: require('../../assets/dark/jpg/image-background-4.jpg'),
  onboarding1: require('../../assets/dark/png/onboarding-1.png'),
  onboarding2: require('../../assets/dark/png/onboarding-2.png'),
  onboarding3: require('../../assets/dark/png/onboarding-3.png'),
  facebook: require('../../assets/dark/png/facebook.png'),
  google: require('../../assets/dark/png/google.png'),
  apple: require('../../assets/dark/png/apple.png'),
  homeInactive: require('../../assets/dark/png/home-inactive.png'),
  homeActive: require('../../assets/dark/png/home-active.png'),
  ongoingInactive: require('../../assets/dark/png/ongoing-inactive.png'),
  ongoingActive: require('../../assets/dark/png/ongoing-active.png'),
  profileInactive: require('../../assets/dark/png/profile-inactive.png'),
  profileActive: require('../../assets/dark/png/profile-active.png'),
  arrowRight: require('../../assets/dark/png/arrow-right.png'),
  arrowLeft: require('../../assets/dark/png/arrow-left.png'),
  qrCode: require('../../assets/dark/png/qr.png'),
  defaultAvatar: require('../../assets/dark/png/default-avatar.png'),
  editBtn: require('../../assets/dark/png/edit-btn.png'),
  notificationIcon: require('../../assets/dark/png/notifications.png'),
  settingsIcon: require('../../assets/dark/png/settings.png'),
  logoutIcon: require('../../assets/dark/png/logout.png'),
  arrowRightLight: require('../../assets/dark/png/arrow-right-light.png'),
  arrowRightRed: require('../../assets/dark/png/arrow-right-red.png'),
  plus: require('../../assets/dark/png/plus.png'),
  human: require('../../assets/dark/png/human.png'),
  delete: require('../../assets/dark/png/delete.png'),
  shotBall: require('../../assets/dark/png/shot-ball.png'),
  shotFreekick: require('../../assets/dark/png/shot-freekick.png'),
  saveHand: require('../../assets/dark/png/save_hand.png'),
  shotOwnGoal: require('../../assets/dark/png/shot-own-goal.png'),
  shotPenalty: require('../../assets/dark/png/shot-penalty.png'),
  foul: require('../../assets/dark/png/foul.png'),
  feedGoal: require('../../assets/dark/png/feed-goal.png'),
  feedYellowCard: require('../../assets/dark/png/feed-yellow.png'),
  feedSecondYellowCard: require('../../assets/dark/png/feed-second-yellow.png'),
  feedRedCard: require('../../assets/dark/png/feed-red.png'),
  feedPenalty: require('../../assets/dark/png/feed-penalty.png'),
  feedMissPenalty: require('../../assets/dark/png/feed-miss-penalty.png'),
  feedOwnGoal: require('../../assets/dark/png/feed-own-goal.png'),
  feedAssist: require('../../assets/dark/png/feed-assist.png'),
  feedTime: require('../../assets/dark/png/feed-time.png'),
  feedTimeBlack: require('../../assets/dark/png/feed-time-black.png'),
  feedFault: require('../../assets/dark/png/feed-fault.png'),
  feedPenaltySaved: require('../../assets/dark/png/feed-penalty-saved.png'),
  defaultTeamLogo1: require('../../assets/dark/jpg/default-team-logo-1.jpg'),
  defaultTeamLogo2: require('../../assets/dark/jpg/default-team-logo-2.jpg'),
  defaultTeamLogo3: require('../../assets/dark/jpg/default-team-logo-3.jpg'),
  defaultTeamLogo4: require('../../assets/dark/jpg/default-team-logo-4.jpg'),
  frame: require('../../assets/dark/png/frame.png'),
};

export const IconsLight: IconPaths = {
  logo: require('../../assets/light/png/logo.png'),
  backgroundImage1: require('../../assets/light/jpg/image-background-0.jpg'),
  backgroundImage2: require('../../assets/light/jpg/image-background-1.jpg'),
  backgroundImage3: require('../../assets/light/jpg/image-background-2.jpg'),
  backgroundImage4: require('../../assets/light/jpg/image-background-3.jpg'),
  backgroundImage5: require('../../assets/light/jpg/image-background-4.jpg'),
  onboarding1: require('../../assets/light/png/onboarding-1.png'),
  onboarding2: require('../../assets/light/png/onboarding-2.png'),
  onboarding3: require('../../assets/light/png/onboarding-3.png'),
  facebook: require('../../assets/light/png/facebook.png'),
  google: require('../../assets/light/png/google.png'),
  apple: require('../../assets/light/png/apple.png'),
  homeInactive: require('../../assets/light/png/home-inactive.png'),
  homeActive: require('../../assets/light/png/home-active.png'),
  ongoingInactive: require('../../assets/light/png/ongoing-inactive.png'),
  ongoingActive: require('../../assets/light/png/ongoing-active.png'),
  profileInactive: require('../../assets/light/png/profile-inactive.png'),
  profileActive: require('../../assets/light/png/profile-active.png'),
  arrowRight: require('../../assets/light/png/arrow-right.png'),
  arrowLeft: require('../../assets/light/png/arrow-left.png'),
  qrCode: require('../../assets/light/png/qr.png'),
  defaultAvatar: require('../../assets/light/png/default-avatar.png'),
  editBtn: require('../../assets/light/png/edit-btn.png'),
  notificationIcon: require('../../assets/light/png/notifications.png'),
  settingsIcon: require('../../assets/light/png/settings.png'),
  logoutIcon: require('../../assets/light/png/logout.png'),
  arrowRightLight: require('../../assets/light/png/arrow-right-light.png'),
  arrowRightRed: require('../../assets/light/png/arrow-right-red.png'),
  plus: require('../../assets/light/png/plus.png'),
  human: require('../../assets/light/png/human.png'),
  delete: require('../../assets/light/png/delete.png'),
  shotBall: require('../../assets/light/png/shot-ball.png'),
  shotFreekick: require('../../assets/light/png/shot-freekick.png'),
  saveHand: require('../../assets/light/png/save_hand.png'),
  shotOwnGoal: require('../../assets/light/png/shot-own-goal.png'),
  shotPenalty: require('../../assets/light/png/shot-penalty.png'),
  foul: require('../../assets/light/png/foul.png'),
  feedGoal: require('../../assets/light/png/feed-goal.png'),
  feedYellowCard: require('../../assets/light/png/feed-yellow.png'),
  feedSecondYellowCard: require('../../assets/light/png/feed-second-yellow.png'),
  feedRedCard: require('../../assets/light/png/feed-red.png'),
  feedPenalty: require('../../assets/light/png/feed-penalty.png'),
  feedMissPenalty: require('../../assets/light/png/feed-miss-penalty.png'),
  feedOwnGoal: require('../../assets/light/png/feed-own-goal.png'),
  feedAssist: require('../../assets/light/png/feed-assist.png'),
  feedTime: require('../../assets/light/png/feed-time.png'),
  feedTimeBlack: require('../../assets/light/png/feed-time-black.png'),
  feedFault: require('../../assets/light/png/feed-fault.png'),
  feedPenaltySaved: require('../../assets/light/png/feed-penalty-saved.png'),
  defaultTeamLogo1: require('../../assets/light/jpg/default-team-logo-1.jpg'),
  defaultTeamLogo2: require('../../assets/light/jpg/default-team-logo-2.jpg'),
  defaultTeamLogo3: require('../../assets/light/jpg/default-team-logo-3.jpg'),
  defaultTeamLogo4: require('../../assets/light/jpg/default-team-logo-4.jpg'),
  frame: require('../../assets/light/png/frame.png'),
};

type LottiesProp = {
  [key in LottiesName]: any;
};

const LottiesDark: LottiesProp = {
  loadingLogo: require('../../assets/dark/lottie/loader.json'),
  football: require('../../assets/dark/lottie/football.json'),
  complete: require('../../assets/dark/lottie/complete.json'),
  empty: require('../../assets/dark/lottie/empty.json'),
  noInternet: require('../../assets/dark/lottie/no-internet.json'),
  noMatches: require('../../assets/dark/lottie/no-matches.json'),
  agentCongrats1: require('../../assets/dark/lottie/agent-congrats-1.json'),
  agentCongrats2: require('../../assets/dark/lottie/agent-congrats-2.json'),
  agentCongrats3: require('../../assets/dark/lottie/agent-congrats-3.json'),
  agentCongrats4: require('../../assets/dark/lottie/agent-congrats-4.json'),
  notification: require('../../assets/dark/lottie/notifications.json'),
  clap: require('../../assets/dark/lottie/clap.json'),
  love: require('../../assets/dark/lottie/love.json'),
  like: require('../../assets/dark/lottie/like.json'),
  dislike: require('../../assets/dark/lottie/dislike.json'),
  maintenance: require('../../assets/dark/lottie/maintenance.json'),
  appUpdate: require('../../assets/dark/lottie/update.json'),
  premium: require('../../assets/dark/lottie/premium.json'),
};

const LottiesLight: LottiesProp = {
  loadingLogo: require('../../assets/light/lottie/loader.json'),
  football: require('../../assets/light/lottie/football.json'),
  complete: require('../../assets/light/lottie/complete.json'),
  empty: require('../../assets/light/lottie/empty.json'),
  noInternet: require('../../assets/light/lottie/no-internet.json'),
  noMatches: require('../../assets/light/lottie/no-matches.json'),
  agentCongrats1: require('../../assets/light/lottie/agent-congrats-1.json'),
  agentCongrats2: require('../../assets/light/lottie/agent-congrats-2.json'),
  agentCongrats3: require('../../assets/light/lottie/agent-congrats-3.json'),
  agentCongrats4: require('../../assets/light/lottie/agent-congrats-4.json'),
  notification: require('../../assets/light/lottie/notifications.json'),
  clap: require('../../assets/light/lottie/clap.json'),
  love: require('../../assets/light/lottie/love.json'),
  like: require('../../assets/light/lottie/like.json'),
  dislike: require('../../assets/light/lottie/dislike.json'),
  maintenance: require('../../assets/light/lottie/maintenance.json'),
  appUpdate: require('../../assets/light/lottie/update.json'),
  premium: require('../../assets/light/lottie/premium.json'),
};

export const Lotties =
  Appearance.getColorScheme() === 'dark' ? LottiesDark : LottiesLight;
