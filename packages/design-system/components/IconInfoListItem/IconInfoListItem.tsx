import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { Icon } from '../Icon/Icon';
import { Separator } from '../Separator/Separator';
import { IconName } from '../Icon/Icon.types';
import { useTheme } from '../../context/ThemeContext';
import { iconInfoListItemStyles } from './IconInfoListItem.styles';

interface IconInfoListItemProps {
  image?: string;
  title: string;
  withSeparator?: boolean;
  onPress?: () => void;
}

export const IconInfoListItem: React.FC<IconInfoListItemProps> = ({
  image,
  title,
  withSeparator = false,
  onPress,
}) => {
  const { colors, spacings, typography } = useTheme();
  const styles = iconInfoListItemStyles(colors, spacings, typography);

  const isValidImage =
    image && typeof image === 'string' && image.startsWith('https');

  return (
    <View>
      <View style={styles.itemContainer}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={onPress}
          style={styles.contentContainer}
        >
          <View style={styles.row}>
            {isValidImage ? (
              <Image source={{ uri: image }} style={styles.image} />
            ) : (
              <View style={styles.imagePlaceholder}>
                <Icon name={IconName.FRAME} isPng width={24} height={24} />
              </View>
            )}
            <Text numberOfLines={1} style={styles.title}>
              {title}
            </Text>
          </View>
        </TouchableOpacity>
        <Icon name={IconName.ARROW_RIGHT_LIGHT} isPng width={18} height={18} />
      </View>
      {withSeparator && <Separator containerStyle={styles.separator} />}
    </View>
  );
};
