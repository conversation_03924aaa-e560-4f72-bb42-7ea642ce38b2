import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const iconInfoListItemStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    itemContainer: {
      padding: spacings.xlarge,
      borderRadius: 10,
      flexDirection: 'row',
      justifyContent: 'space-between',
      backgroundColor: colors.neutral700,
      alignItems: 'center',
    },
    contentContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    title: {
      ...typography['small-bold'],
      color: colors.inverse900,
      marginLeft: spacings.large,
      flex: 1,
    },
    image: {
      borderRadius: spacings.medium,
      width: 50,
      height: 50,
      resizeMode: 'cover',
    },
    imagePlaceholder: {
      width: 50,
      height: 50,
      borderRadius: spacings.medium,
      backgroundColor: colors.neutral800,
      justifyContent: 'center',
      alignItems: 'center',
    },
    separator: {
      marginHorizontal: spacings.xxxlarge,
      backgroundColor: colors.inverse900,
    },
    editButton: {
      padding: spacings.small,
    },
  });
