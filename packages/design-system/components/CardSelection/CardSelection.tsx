import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { i18next } from '@scorescast/translations';
import { useTheme } from '../../context/ThemeContext';
import { cardSelectionsStyles } from './CardSelections.styles';

interface CardSelectionProps {
  onSelect: (cardId: string) => void;
  cards: any;
}

export const CardSelection: React.FC<CardSelectionProps> = ({
  onSelect,
  cards,
}) => {
  const [selectedCard, setSelectedCard] = useState<string | null>(null);
  const { colors, spacings, typography } = useTheme();
  const styles = cardSelectionsStyles(colors, spacings, typography);

  const handleSelect = (card) => {
    if (selectedCard === card.id) {
      setSelectedCard(null);
      onSelect('');
    } else {
      setSelectedCard(card.id);
      onSelect(card.id);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {i18next.t('AGENT.FOUL_SELECTION.SELECT_CARD')}
      </Text>
      <View style={styles.cardContainer}>
        {cards.map((card) => (
          <TouchableOpacity
            key={card.id}
            activeOpacity={0.8}
            style={[
              styles.cardWrapper,
              selectedCard !== card.id && styles.notSelectedContainer,
            ]}
            onPress={() => handleSelect(card)}
          >
            {card.overlay && (
              <View
                style={[
                  styles.card,
                  { backgroundColor: card.overlay },
                  styles.overlay,
                  selectedCard === card.id && {
                    backgroundColor: card.selectedOverlayColor,
                  },
                ]}
              />
            )}
            <View
              style={[
                styles.card,
                card.overlay && styles.hasOverlay,
                { backgroundColor: card.color },
                selectedCard === card.id && {
                  backgroundColor: card.selectedColor,
                },
              ]}
            />
            <Text
              style={[
                styles.cardLabel,
                selectedCard === card.id && styles.selectedText,
              ]}
            >
              {card.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default CardSelection;
