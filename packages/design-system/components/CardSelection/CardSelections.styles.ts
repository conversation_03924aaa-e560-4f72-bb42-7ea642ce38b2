import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const cardSelectionsStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    label: {
      color: colors.inverse900,
      ...typography['medium-bold'],
      marginBottom: spacings.xlarge,
      alignSelf: 'flex-start',
    },
    container: {
      marginTop: spacings.xxxxlarge,
    },
    cardContainer: {
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      alignItems: 'center',
      width: '100%',
    },
    cardWrapper: {
      alignItems: 'center',
      marginHorizontal: spacings.large,
    },
    card: {
      width: 50,
      height: 75,
      borderRadius: 5,
    },
    hasOverlay: {
      transform: [{ scale: 0.8 }],
    },
    overlay: {
      transform: [{ scale: 0.8 }],
      position: 'absolute',
      top: -10,
      left: 28,
    },
    cardLabel: {
      marginTop: spacings.medium,
      ...typography['small-bold'],
      color: colors.inverse900,
    },
    selectedText: {
      color: colors.inverse900,
    },
    notSelectedContainer: {
      opacity: 0.3,
    },
  });
