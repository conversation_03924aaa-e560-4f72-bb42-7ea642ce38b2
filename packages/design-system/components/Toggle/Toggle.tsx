import React, { useState } from 'react';
import { TouchableOpacity, Animated } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { toggleStyles } from './Toggle.styles';

interface ToggleProps {
  isOn: boolean;
  onToggle: (value: boolean) => void;
}

const Toggle = ({ isOn, onToggle }: ToggleProps) => {
  const [animation] = useState(new Animated.Value(isOn ? 1 : 0));
  const { colors } = useTheme();
  const styles = toggleStyles(colors);

  const toggleSwitch = () => {
    Animated.timing(animation, {
      toValue: isOn ? 0 : 1,
      duration: 200,
      useNativeDriver: false,
    }).start();
    onToggle(!isOn);
  };

  const translateX = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [2, 22],
  });

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={toggleSwitch}
      style={styles.container}
    >
      <Animated.View
        style={[
          styles.toggleBackground,
          !isOn && { backgroundColor: colors.inverse500 },
        ]}
      >
        <Animated.View
          style={[styles.toggleCircle, { transform: [{ translateX }] }]}
        />
      </Animated.View>
    </TouchableOpacity>
  );
};

export default Toggle;
