import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';

export const toggleStyles = (colors: ColorStyles) =>
  StyleSheet.create({
    container: {
      width: 50,
      height: 30,
      justifyContent: 'center',
    },
    toggleBackground: {
      width: 50,
      height: 30,
      borderRadius: 15,
      padding: 2,
      justifyContent: 'center',
      backgroundColor: colors.secondary900,
    },
    toggleCircle: {
      width: 26,
      height: 26,
      borderRadius: 13,
      backgroundColor: colors.inverse900,
      elevation: 3,
      shadowColor: colors.shadowColor,
      shadowOpacity: 0.2,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 2,
    },
  });
