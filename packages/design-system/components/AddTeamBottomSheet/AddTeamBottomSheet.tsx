import React, { forwardRef, useEffect, useState, useCallback } from 'react';
import { View, Text } from 'react-native';
import { i18next } from '@scorescast/translations';
import { TEAM_CODE_LENGTH } from '@scorescast/formatters-constants/';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useTheme } from '../../context/ThemeContext';
import { BottomSheet } from '../BottomSheet/BottomSheet';
import { Button } from '../Button/Button';
import { CodeReader } from '../CodeReader/CodeReader';
import { addTeamBottomSheetStyles } from './AddTeamBottomSheet.styles';
import { CircleButton } from '../CircleButton/CircleButton';
import { IconName } from '../Icon/Icon.types';

type AddTeamBottomSheetProps = {
  onCodeCompleted: (code: string) => void;
  onQRCodeSearchPressed: () => void;
};

export const AddTeamBottomSheet = forwardRef<
  BottomSheetModal,
  AddTeamBottomSheetProps
>(({ onCodeCompleted, onQRCodeSearchPressed }, ref) => {
  const [code, setCode] = useState('');
  const { spacings, colors, typography } = useTheme();
  const styles = addTeamBottomSheetStyles(spacings, colors, typography);
  const circleButtonDisabled = code.length !== TEAM_CODE_LENGTH;

  const onComplete = useCallback(() => {
    if (code.length === TEAM_CODE_LENGTH) {
      onCodeCompleted(code);
      setCode('');
    }
  }, [code, onCodeCompleted]);

  useEffect(() => {
    onComplete();
  }, [code, onComplete]);

  return (
    <BottomSheet ref={ref}>
      <View style={styles.container}>
        <Text style={styles.title}>
          {i18next.t('ADD_A_TEAM.BOTTOM_SHEET.TITLE')}
        </Text>
        <Text style={styles.subtitle}>
          {i18next.t('ADD_A_TEAM.BOTTOM_SHEET.DESCRIPTION')}
        </Text>
        <Text style={styles.digitCodeTitle}>
          {i18next.t('ADD_A_TEAM.BOTTOM_SHEET.SUB_DESCRIPTION')}
        </Text>
        <CodeReader value={code} setValue={setCode} secondaryTheme autoFocus />
        <CircleButton
          disabled={circleButtonDisabled}
          onPress={onComplete}
          icon={
            circleButtonDisabled
              ? IconName.ARROW_RIGHT
              : IconName.ARROW_RIGHT_LIGHT
          }
          containerStyle={styles.circleButtonContainer}
        />
        <Text style={styles.qrCodeTitle}>
          {i18next.t('ADD_A_TEAM.BOTTOM_SHEET.QR_CODE_DESCRIPTION')}
        </Text>
        <Button
          secondary
          containerStyle={styles.qrCodeButtonContainer}
          iconStyle={styles.qrCodeButtonIcon}
          title={i18next.t('HOME.QR_CODE_BUTTON_TITLE')}
          onPress={onQRCodeSearchPressed}
        />
      </View>
    </BottomSheet>
  );
});

AddTeamBottomSheet.displayName = 'AddTeamBottomSheet';
