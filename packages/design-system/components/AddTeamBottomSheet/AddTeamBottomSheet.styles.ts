import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';

export const addTeamBottomSheetStyles = (
  spacings: SpacingStyles,
  colors: ColorStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacings.large,
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.xxxxlarge,
    },
    title: {
      ...typography['large-bold'],
      textAlign: 'center',
      color: colors.inverse900,
    },
    subtitle: {
      ...typography.small,
      color: colors.inverse900,
      paddingVertical: spacings.xxxlarge,
      textAlign: 'center',
    },
    digitCodeTitle: {
      ...typography['small-bold'],
      color: colors.inverse900,
      textAlign: 'center',
      marginBottom: spacings.xlarge,
    },
    buttonContainer: {
      width: '100%',
      marginTop: spacings.xxxlarge,
    },
    circleButtonContainer: {
      alignSelf: 'center',
      marginVertical: spacings.xlarge,
    },
    qrCodeTitle: {
      ...typography['small-bold'],
      color: colors.inverse900,
      textAlign: 'center',
    },
    qrCodeButtonContainer: {
      marginTop: spacings.xlarge,
      width: '100%',
    },
    qrCodeButtonIcon: {
      paddingRight: spacings.large,
    },
  });
