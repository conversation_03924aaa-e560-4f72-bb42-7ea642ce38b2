import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';

export const codeReaderStyles = (
  colors: ColorStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    cellContainer: {
      width: 56,
      borderRadius: 5,
      height: 70,
      borderWidth: 2,
      borderColor: colors.inverse500,
      backgroundColor: colors.neutral700,
    },
    cell: {
      textAlign: 'center',
      color: colors.inverse900,
      ...typography['large'],
      lineHeight: 64,
    },
    focusCell: {},
    secondaryCellContainer: {
      backgroundColor: colors.inverse400,
    },
  });
