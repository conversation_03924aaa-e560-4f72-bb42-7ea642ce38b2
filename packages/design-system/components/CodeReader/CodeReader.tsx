import React from 'react';
import { Keyboard, Text, View } from 'react-native';
import { TEAM_CODE_LENGTH } from '@scorescast/formatters-constants';

import {
  CodeField,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import { useTheme } from '../../context/ThemeContext';
import { codeReaderStyles } from './CodeReader.styles';

interface CodeReaderProps {
  value: string;
  secondaryTheme?: boolean;
  setValue: (value: string) => void;
  autoFocus?: boolean;
}

export const CodeReader: React.FC<CodeReaderProps> = ({
  value,
  setValue,
  secondaryTheme = false,
  autoFocus,
}) => {
  const ref = useBlurOnFulfill({ value, cellCount: TEAM_CODE_LENGTH });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });

  const { colors, typography } = useTheme();
  const styles = codeReaderStyles(colors, typography);
  return (
    <CodeField
      ref={ref}
      {...props}
      value={value}
      onChangeText={setValue}
      autoFocus={autoFocus}
      cellCount={TEAM_CODE_LENGTH}
      textContentType="oneTimeCode"
      testID="my-code-input"
      onSubmitEditing={() => Keyboard.dismiss()}
      renderCell={({ index, symbol, isFocused }) => (
        <View
          key={index}
          style={[
            styles.cellContainer,
            secondaryTheme && styles.secondaryCellContainer,
          ]}
        >
          <Text
            key={index}
            style={[styles.cell, isFocused && styles.focusCell]}
            onLayout={getCellOnLayoutHandler(index)}
          >
            {symbol || (isFocused ? <Cursor /> : null)}
          </Text>
        </View>
      )}
    />
  );
};
