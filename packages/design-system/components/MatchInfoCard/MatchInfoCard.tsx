import React from 'react';
import { StyleProp, Text, View, ViewStyle } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { matchInfoCardStyles } from './MatchInfoCard.styles';

interface MatchInfoCardProps {
  leftText?: string;
  centerText?: string;
  rightText?: string;
  containerStyle?: StyleProp<ViewStyle>;
}

export const MatchInfoCard: React.FC<MatchInfoCardProps> = ({
  leftText,
  centerText,
  rightText,
  containerStyle,
}) => {
  const { colors, spacings, typography } = useTheme();
  const styles = matchInfoCardStyles(colors, spacings, typography);

  return (
    <View style={[styles.matchInfoCard, containerStyle]}>
      <Text style={styles.text}>{leftText}</Text>
      <Text style={styles.text}>{centerText}</Text>
      <Text style={styles.text}>{rightText}</Text>
    </View>
  );
};
