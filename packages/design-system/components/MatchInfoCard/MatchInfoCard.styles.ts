import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const matchInfoCardStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    matchInfoCard: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      height: 60,
      backgroundColor: colors.neutral800,
      paddingVertical: spacings.xlarge,
      paddingHorizontal: spacings.xxlarge,
      borderRadius: 5,
    },
    text: {
      ...typography['small-semibold'],
      color: colors.inverse900,
    },
  });
