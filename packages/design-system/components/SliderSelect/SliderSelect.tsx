import React, { useCallback, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Pressable,
  Animated,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { sliderSelectStyles } from './SliderSelect.styles';
import { useAgentStore } from '@scorescast/http-client';
import { MatchLocations } from '@scorescast/formatters-constants/src/constants/live-match';

export interface SliderOptions {
  id: string;
  value: string;
}

interface SliderSelectorProps {
  options: SliderOptions[];
  selected: { id: string; value: string };
  isDisabled: boolean;
  onToggle: (option: SliderOptions) => void;
  containerStyle?: StyleProp<ViewStyle>;
}

export const SliderSelect: React.FC<SliderSelectorProps> = ({
  options,
  selected,
  isDisabled,
  onToggle,
  containerStyle,
}) => {
  const { currentMatchLocation } = useAgentStore();
  const { colors, typography } = useTheme();
  const sliderWidth = useRef(0);
  const translateX = useRef(new Animated.Value(0)).current;
  const containerRef = useRef<View>(null);
  const styles = sliderSelectStyles(colors, typography);

  const onSwitchLayout = (event: any) => {
    sliderWidth.current = event.nativeEvent.layout.width / options.length;

    let displayedOptions = options;
    if (currentMatchLocation === MatchLocations.AWAY) {
      displayedOptions = [...options].reverse();
    }

    const selectedIndex = displayedOptions.findIndex(
      (option) => option.id === selected.id
    );

    if (selectedIndex !== -1) {
      Animated.spring(translateX, {
        toValue: selectedIndex * sliderWidth.current,
        useNativeDriver: true,
        friction: 10,
        tension: 100,
      }).start();
    }
  };

  useEffect(() => {
    if (sliderWidth.current > 0) {
      let displayedOptions = options;
      if (currentMatchLocation === MatchLocations.AWAY) {
        displayedOptions = [...options].reverse();
      }

      const selectedIndex = displayedOptions.findIndex(
        (option) => option.id === selected.id
      );

      if (selectedIndex !== -1) {
        Animated.spring(translateX, {
          toValue: selectedIndex * sliderWidth.current,
          useNativeDriver: true,
          friction: 10,
          tension: 100,
        }).start();
      }
    }
  }, [selected, currentMatchLocation, options]);

  const renderOptions = useCallback(() => {
    let displayedOptions = options;
    if (currentMatchLocation === MatchLocations.AWAY) {
      displayedOptions = [...options].reverse();
    }

    return (
      <View style={styles.optionContainer}>
        {displayedOptions.map((option) => (
          <Pressable
            key={option.id}
            onPress={() => {
              if (isDisabled || selected.id === option.id) return;
              onToggle(option);
            }}
            style={[
              styles.option,
              selected.id === option.id && styles.selectedOption,
            ]}
          >
            <Text
              style={[
                styles.text,
                selected.id === option.id
                  ? styles.activeText
                  : styles.inactiveText,
              ]}
            >
              {option.value}
            </Text>
          </Pressable>
        ))}
      </View>
    );
  }, [currentMatchLocation, selected, options, isDisabled, onToggle]);

  return (
    <View
      style={[styles.container, isDisabled && styles.disabled, containerStyle]}
    >
      <View ref={containerRef} onLayout={onSwitchLayout} style={styles.switch}>
        <Animated.View
          style={[
            styles.slider,
            {
              width: sliderWidth.current,
              transform: [
                {
                  translateX: translateX.interpolate({
                    inputRange: [0, sliderWidth.current],
                    outputRange: [0, sliderWidth.current],
                  }),
                },
              ],
            },
          ]}
        />
        {renderOptions()}
      </View>
    </View>
  );
};

SliderSelect.displayName = 'SliderSelect';
