import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';

export const sliderSelectStyles = (
  colors: ColorStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    disabled: {
      opacity: 0.6,
    },
    switch: {
      width: 298,
      height: 48,
      borderRadius: 50,
      backgroundColor: 'transparent',
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.inverse900,
      overflow: 'hidden',
      position: 'relative',
    },
    selectedOption: {
      width: '50%',
      borderRadius: 50,
      backgroundColor: colors.inverse900,
    },
    slider: {
      position: 'absolute',
      height: '100%',
      borderRadius: 50,
    },
    optionContainer: {
      flexDirection: 'row',
      flex: 1,
      alignItems: 'center',
      justifyContent: 'space-between',
      position: 'relative',
      zIndex: 1,
    },
    option: {
      flex: 1,
      height: 48,
      width: '50%',
      alignItems: 'center',
      justifyContent: 'center',
    },
    text: {
      fontSize: 14,
      fontWeight: 'bold',
      textAlign: 'center',
    },
    activeText: {
      ...typography['xSmall-bold'],
      color: colors.inverse100,
    },
    inactiveText: {
      ...typography['xSmall-bold'],
      color: colors.inverse900,
    },
  });
