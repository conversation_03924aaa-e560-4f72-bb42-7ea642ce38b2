import React, { useState, useEffect } from 'react';
import {
  View,
  Animated,
  Easing,
  Text,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { toasterStyles } from './Toaster.styles';
import { useToasterStore } from '@scorescast/http-clients';

export const Toaster = () => {
  const [errorTranslateY] = useState(new Animated.Value(-200));
  const { toaster, removeToaster } = useToasterStore();
  const title = toaster ? toaster.title : null;
  const description = toaster ? toaster.description : null;
  const type = toaster ? toaster.type : null;

  const { colors, spacings, typography } = useTheme();
  const styles = toasterStyles(colors, spacings, typography);

  const animateIn = () => {
    Animated.sequence([
      Animated.timing(errorTranslateY, {
        toValue: 0,
        easing: Easing.inOut(Easing.ease),
        duration: 750,
        useNativeDriver: true,
      }),
      Animated.delay(3500),
      Animated.timing(errorTranslateY, {
        toValue: -200,
        easing: Easing.inOut(Easing.ease),
        duration: 750,
        useNativeDriver: true,
      }),
    ]).start(() => removeToaster());
  };

  const onCloseAnimation = () => {
    Animated.timing(errorTranslateY, {
      toValue: -200,
      easing: Easing.inOut(Easing.ease),
      duration: 750,
      useNativeDriver: true,
    }).start(() => removeToaster());
  };

  useEffect(() => {
    if (
      (title && title.length > 0) ||
      (description && description.length > 0)
    ) {
      animateIn();
    }
  }, [title, description]);

  if (!title || !description) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        { transform: [{ translateY: errorTranslateY }] },
      ]}
    >
      <SafeAreaView>
        <View
          style={[
            styles.contentContainer,
            type === 'error'
              ? {
                  borderColor: colors.red,
                  backgroundColor: colors.red,
                }
              : {
                  borderColor: colors.green,
                  backgroundColor: colors.green,
                },
          ]}
        >
          <View style={styles.textContainer}>
            <Text style={styles.errorTitleText}>{title}</Text>
            <Text style={styles.errorMessageText}>{description}</Text>
          </View>
          <TouchableOpacity
            onPress={() => onCloseAnimation()}
            activeOpacity={0.9}
            style={styles.closeIconWrapper}
          ></TouchableOpacity>
        </View>
      </SafeAreaView>
    </Animated.View>
  );
};
