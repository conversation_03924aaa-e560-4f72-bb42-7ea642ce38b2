import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

import { ColorStyles } from '../../theme/colors.light';
export const toasterStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    closeIconWrapper: {
      position: 'absolute',
      right: 12,
      top: 22,
    },
    container: {
      position: 'absolute',
      top: 0,
      width: '100%',
      zIndex: 9999,
    },
    contentContainer: {
      paddingVertical: spacings.large,
      borderRadius: 8,
      marginHorizontal: spacings.xlarge,
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 0.5,
    },
    errorTitleText: {
      ...typography['small-bold'],
      marginBottom: 5,
      color: colors.inverse900,
    },
    errorMessageText: {
      ...typography['xSmall-bold'],
      color: colors.inverse900,
    },
    textContainer: {
      marginLeft: spacings.xlarge,
      marginRight: spacings.medium,
      flex: 1,
    },
  });
