import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { Icon } from '../Icon/Icon';
import { boxSelectorStyles } from './BoxSelector.styles';

interface SquareSelectorProps {
  options: { label: string; icon: any; id: string }[];
  onSelect: (selected: string) => void;
  containerStyle?: StyleProp<ViewStyle>;
}

export const BoxSelector: React.FC<SquareSelectorProps> = ({
  options,
  onSelect,
  containerStyle,
}) => {
  const { colors, spacings, typography } = useTheme();
  const [selected, setSelected] = useState<string | null>(null);
  const squareSize = 135;
  const styles = boxSelectorStyles(colors, spacings, typography);

  const handleSelect = (label: string) => {
    setSelected(label);
    onSelect(label);
  };

  return (
    <View
      style={[
        options.length % 2 === 0
          ? styles.container
          : styles.containerWithOddNumberOfItems,
        containerStyle,
      ]}
    >
      <View
        style={[
          options.length % 2 === 0
            ? styles.innerContainer
            : styles.innerContainerWithOddNumberOfItems,
        ]}
      >
        {options.map((item) => (
          <TouchableOpacity
            key={item.id}
            activeOpacity={0.8}
            style={[
              styles.square,
              { width: squareSize, height: squareSize },
              selected === item.label && styles.selected,
            ]}
            onPress={() => handleSelect(item.id)}
          >
            <Icon name={item.icon} width={100} height={50} isPng />
            <Text style={styles.label}>{item.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

BoxSelector.displayName = 'BoxSelector';
