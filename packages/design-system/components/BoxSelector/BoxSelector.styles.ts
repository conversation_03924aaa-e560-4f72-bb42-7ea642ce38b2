import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const boxSelectorStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    containerWithOddNumberOfItems: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    innerContainerWithOddNumberOfItems: {
      flexWrap: 'wrap',
      justifyContent: 'center',
      flexDirection: 'row',
      gap: 18,
    },
    innerContainer: {
      flexWrap: 'wrap',
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 18,
    },
    square: {
      backgroundColor: colors.accent100,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 10,
    },
    selected: {
      borderColor: colors.accent100,
      borderWidth: 2,
    },
    icon: {
      width: '50%',
      height: '50%',
    },
    label: {
      marginTop: spacings.large,
      ...typography['xSmall-bold'],
      color: colors.tertiary,
    },
  });
