import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const profileMenuItemsStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    menuItemContainer: {
      padding: spacings.xlarge,
      borderRadius: 10,
      flexDirection: 'row',
      justifyContent: 'space-between',
      backgroundColor: colors.neutral700,
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    title: {
      ...typography['small'],
      color: colors.inverse900,
      marginLeft: spacings.large,
      flex: 1,
    },
    titleHightlighted: {
      color: colors.red,
    },
    separator: {
      marginHorizontal: spacings.xxxlarge,
      backgroundColor: colors.inverse900,
    },
    webIcon: {
      borderRadius: 5,
    },
  });
