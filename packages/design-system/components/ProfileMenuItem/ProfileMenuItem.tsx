import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Icon } from '../Icon/Icon';
import { Separator } from '../Separator/Separator';
import { IconName } from '../Icon/Icon.types';
import { useTheme } from '../../context/ThemeContext';
import { profileMenuItemsStyles } from './ProfileMenuItems.styles';

interface ProfileMenuItemProps {
  icon?: IconName | string;
  isWebIcon?: boolean;
  title: string;
  isHighlighted: boolean;
  withSeparator: boolean;
  hideArrow?: boolean;
  disabled?: boolean;
  onPress?: () => void;
}

export const ProfileMenuItem: React.FC<ProfileMenuItemProps> = ({
  icon,
  title,
  isHighlighted,
  isWebIcon,
  withSeparator,
  hideArrow,
  disabled,
  onPress,
}) => {
  const { colors, spacings, typography } = useTheme();
  const styles = profileMenuItemsStyles(colors, spacings, typography);

  return (
    <View>
      <TouchableOpacity
        activeOpacity={0.8}
        disabled={disabled}
        onPress={onPress}
        style={styles.menuItemContainer}
      >
        <View style={styles.row}>
          {isWebIcon && icon ? (
            <Icon
              isWebImage={true}
              webImageUrl={icon}
              width={45}
              height={45}
              styles={styles.webIcon}
            />
          ) : icon ? (
            <Icon name={icon as IconName} isPng width={17} height={28} />
          ) : null}
          <Text
            numberOfLines={1}
            style={[styles.title, isHighlighted && styles.titleHightlighted]}
          >
            {title}
          </Text>
        </View>
        {!hideArrow && (
          <Icon
            name={
              isHighlighted
                ? IconName.ARROW_RIGHT_RED
                : IconName.ARROW_RIGHT_LIGHT
            }
            isPng
            width={17}
            height={28}
          />
        )}
      </TouchableOpacity>
      {withSeparator && <Separator containerStyle={styles.separator} />}
    </View>
  );
};
