import React from 'react';
import { View, StyleProp, ViewStyle } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { stepperStyles } from './Stepper.styles';

interface Stepper {
  totalSteps: number;
  currentStep: number;
  containerStyles?: StyleProp<ViewStyle>;
}

const Stepper: React.FC<Stepper> = ({
  totalSteps,
  currentStep,
  containerStyles,
}) => {
  const { colors, spacings } = useTheme();
  const styles = stepperStyles(colors, spacings);
  return (
    <View style={[styles.container, containerStyles]}>
      {Array.from({ length: totalSteps }, (_, index) => {
        const isSelected = index + 1 === currentStep;
        return (
          <View
            key={index}
            style={[styles.dot, isSelected && styles.selectedStep]}
          />
        );
      })}
    </View>
  );
};

export default Stepper;
