import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';

export const stepperStyles = (colors: ColorStyles, spacings: SpacingStyles) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    dot: {
      width: 5,
      height: 5,
      borderRadius: 5,
      backgroundColor: colors.inverse900,
      marginHorizontal: spacings.small,
    },
    selectedStep: {
      width: 50,
      height: 5,
      borderRadius: 4,
      backgroundColor: colors.inverse900,
    },
  });
