import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';

export const ImageUploaderStyles = (colors: ColorStyles) =>
  StyleSheet.create({
    container: {
      width: 150,
      height: 150,
      borderRadius: 8,
      overflow: 'hidden',
      justifyContent: 'center',
      alignItems: 'center',
    },
    image: {
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
    },
    loaderContainer: {
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
    loader: {
      width: 40,
      height: 40,
    },
  });
