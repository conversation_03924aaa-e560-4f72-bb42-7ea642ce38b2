import React, { useState, useEffect } from 'react';
import {
  TouchableOpacity,
  Image,
  Platform,
  PermissionsAndroid,
  View,
  ActivityIndicator,
} from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import RNFS from 'react-native-fs';
import ImageResizer from '@bam.tech/react-native-image-resizer';
import { ImgbbApi } from '@scorescast/http-clients';
import { Icon } from '../Icon/Icon';
import { IconName } from '../Icon/Icon.types';
import { useTheme } from '../../context/ThemeContext';
import { ImageUploaderStyles } from './ImageUploader.styles';
type Props = {
  initialImage?: string;
  onImageUploaded: (imageUrl: string) => void;
  isEditAllowed?: boolean;
};

export const ImageUploader: React.FC<Props> = ({
  initialImage,
  onImageUploaded,
  isEditAllowed = true,
}) => {
  const [image, setImage] = useState<string | null>(initialImage || null);

  useEffect(() => {
    if (initialImage) {
      setImage(initialImage);
    }
  }, [initialImage]);
  const [isLoading, setIsLoading] = useState(false);
  const { colors } = useTheme();
  const styles = ImageUploaderStyles(colors);

  const requestPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
          );
          return granted === PermissionsAndroid.RESULTS.GRANTED;
        } else {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
          );
          return granted === PermissionsAndroid.RESULTS.GRANTED;
        }
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  const processAndUploadImage = async (uri: string) => {
    try {
      setIsLoading(true);

      const resizedImage = await ImageResizer.createResizedImage(
        uri,
        300,
        300,
        'JPEG',
        80,
        0,
        undefined,
        false,
        { mode: 'contain', onlyScaleDown: true }
      );

      const base64 = await RNFS.readFile(resizedImage.uri, 'base64');
      const response = await ImgbbApi.uploadImage(base64);

      setImage(response.data.url);
      onImageUploaded(response.data.url);
    } catch (error) {
      console.error('Image processing failed:', error);
      setImage(uri);
      onImageUploaded(uri);
    } finally {
      setIsLoading(false);
    }
  };

  const selectImage = async () => {
    if (!isEditAllowed) return;

    const hasPermission = await requestPermission();
    if (!hasPermission) return;

    launchImageLibrary(
      {
        mediaType: 'photo',
        quality: 0.8,
        selectionLimit: 1,
        maxWidth: 1000,
        maxHeight: 1000,
      },
      (response) => {
        if (response.didCancel || response.errorCode) return;

        const uri = response.assets?.[0]?.uri;
        const type = response.assets?.[0]?.type;

        if (uri && type && !type.includes('webp')) {
          processAndUploadImage(uri);
        } else {
          console.warn('Invalid image format. WEBP is not supported.');
        }
      }
    );
  };

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={selectImage}
      disabled={isLoading || !isEditAllowed}
      style={[styles.container, { backgroundColor: colors.neutral800 }]}
    >
      {isLoading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator
            size="large"
            color={colors.neutral100}
            style={styles.loader}
          />
        </View>
      ) : image ? (
        <Image source={{ uri: image }} style={styles.image} />
      ) : (
        <Icon name={IconName.FRAME} width={130} height={130} isPng />
      )}
    </TouchableOpacity>
  );
};
