import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import Popover from 'react-native-popover-view';
import { REACTIONS } from '@scorescast/formatters-constants';
import { useTheme } from '../../context/ThemeContext';
import { reactionsItemsStyles } from './ReactionsItems.styles';

interface ReactionsItemsProps {
  popoverRef: React.RefObject<Popover>;
  handlePress: (type: string) => void;
}
export const ReactionsItems = ({
  popoverRef,
  handlePress,
}: ReactionsItemsProps) => {
  const { typography, spacings } = useTheme();
  const styles = reactionsItemsStyles(typography, spacings);
  return (
    <View style={styles.container}>
      {REACTIONS.map((reaction, index) => (
        <TouchableOpacity
          key={index}
          activeOpacity={0.8}
          onPress={() => {
            popoverRef.current?.requestClose();
            handlePress(reaction.reactionType);
          }}
        >
          <Text style={styles.reaction}>{reaction.emoji}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};
