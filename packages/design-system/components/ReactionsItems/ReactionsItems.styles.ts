import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const reactionsItemsStyles = (
  typography: TypographyStyles,
  spacings: SpacingStyles
) =>
  StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'row',
      marginVertical: spacings.small,
    },
    reaction: {
      ...typography['xLarge'],
      marginHorizontal: spacings.medium,
    },
  });
