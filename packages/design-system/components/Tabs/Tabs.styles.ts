import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';
import { ColorStyles } from '../../theme/colors.light';

export const tabsStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'flex-start',
    },
    scrollContainer: {
      flexDirection: 'row',
      flexWrap: 'nowrap',
    },
    tab: {
      paddingVertical: spacings.small,
      marginHorizontal: spacings.xxlarge,
      alignItems: 'center',
      justifyContent: 'center',
      borderBottomWidth: 2,
      borderBottomColor: 'transparent',
    },
    activeTab: {
      borderBottomWidth: 2,
      borderBottomColor: colors.inverse900,
    },
    tabText: {
      ...typography['small'],
    },
    activeTabText: {
      color: colors.inverse900,
      ...typography['small-bold'],
    },
    inactiveTabText: {
      color: colors.inverse900,
    },
  });
