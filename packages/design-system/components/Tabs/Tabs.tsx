import React, { useCallback, useState } from 'react';
import {
  Text,
  TouchableOpacity,
  View,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { tabsStyles } from './Tabs.styles';

interface TabsProps {
  tabs: string[];
  containerStyle?: StyleProp<ViewStyle>;
  onTabPress: (tab: string) => void;
}

export const Tabs = ({ tabs, containerStyle, onTabPress }: TabsProps) => {
  const [activeTab, setActiveTab] = useState(tabs[0]);
  const { colors, spacings, typography } = useTheme();
  const styles = tabsStyles(colors, spacings, typography);

  const handleTabPress = useCallback(
    (tab: string) => {
      setActiveTab(tab);
      onTabPress?.(tab);
    },
    [onTabPress]
  );

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.scrollContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab}
            activeOpacity={0.8}
            style={[styles.tab, activeTab === tab && styles.activeTab]}
            onPress={() => handleTabPress(tab)}
          >
            <Text
              numberOfLines={1}
              style={[
                styles.tabText,
                activeTab === tab
                  ? styles.activeTabText
                  : styles.inactiveTabText,
              ]}
            >
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};
