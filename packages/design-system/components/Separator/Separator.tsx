import React from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { separatorStyles } from './Separator.styles';

interface SeparatorProps {
  containerStyle?: StyleProp<ViewStyle>;
}

export const Separator: React.FC<SeparatorProps> = ({ containerStyle }) => {
  const { colors } = useTheme();
  const styles = separatorStyles(colors);

  return <View style={[styles.separator, containerStyle]} />;
};
