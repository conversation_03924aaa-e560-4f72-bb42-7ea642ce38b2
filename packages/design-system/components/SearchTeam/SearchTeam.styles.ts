import { Dimensions, StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';
import { ColorStyles } from '../../theme/colors.light';

export const searchTeamStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    codeReaderTitle: {
      ...typography['medium-semibold'],
      paddingTop: Dimensions.get('window').height * 0.1,
      paddingBottom: spacings.xlarge,
      color: colors.inverse900,
      textAlign: 'center',
    },
    circleButtonContainer: {
      alignSelf: 'center',
      marginTop: spacings.xlarge,
    },
    separatorContainer: {
      marginVertical: spacings.xxxxxlarge,
    },
    qrReaderTitle: {
      ...typography['medium-semibold'],
      color: colors.inverse900,
      textAlign: 'center',
    },
    qrCodeButtonContainer: {
      marginTop: spacings.xlarge,
      alignItems: 'center',
      justifyContent: 'center',
    },
    qrCodeButtonIcon: {
      paddingRight: spacings.large,
    },
  });
