import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Separator,
  useTheme,
} from '@scorescast/design-system';
import { TEAM_CODE_LENGTH } from '@scorescast/formatters-constants';
import { i18next } from '@scorescast/translations';
import React, { useCallback, useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import { searchTeamStyles } from './SearchTeam.styles';

interface SearchTeamProps {
  onCodeCompleted: (code: string) => void;
  onQRCodeSearchPressed: () => void;
}

export const SearchTeam = ({
  onCodeCompleted,
  onQRCodeSearchPressed,
}: SearchTeamProps) => {
  const { colors, spacings, typography } = useTheme();
  const [code, setCode] = useState('');

  const styles = searchTeamStyles(colors, spacings, typography);
  const circleButtonDisabled = code.length !== TEAM_CODE_LENGTH;

  const onComplete = useCallback(() => {
    if (code.length === TEAM_CODE_LENGTH) {
      onCodeCompleted(code);
      setCode('');
    }
  }, [code, onCodeCompleted]);

  useEffect(() => {
    onComplete();
  }, [code, onComplete]);

  return (
    <View>
      <Header title={i18next.t('HOME.TITLE')} />
      <Text style={styles.codeReaderTitle}>
        {i18next.t('HOME.CODE_READER_TITLE')}
      </Text>
      <CodeReader value={code} setValue={setCode} />
      <CircleButton
        disabled={circleButtonDisabled}
        onPress={onComplete}
        icon={
          circleButtonDisabled
            ? IconName.ARROW_RIGHT
            : IconName.ARROW_RIGHT_LIGHT
        }
        containerStyle={styles.circleButtonContainer}
      />
      <Separator containerStyle={styles.separatorContainer} />
      <Text style={styles.qrReaderTitle}>
        {i18next.t('HOME.QR_READER_TITLE')}
      </Text>
      <Button
        secondary
        containerStyle={styles.qrCodeButtonContainer}
        iconStyle={styles.qrCodeButtonIcon}
        title={i18next.t('HOME.QR_CODE_BUTTON_TITLE')}
        onPress={onQRCodeSearchPressed}
      />
    </View>
  );
};
