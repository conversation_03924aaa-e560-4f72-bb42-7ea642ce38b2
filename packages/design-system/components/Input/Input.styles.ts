import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const InputStyles = (
  colors: ColorStyles,
  spacing: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      marginBottom: 16,
    },
    label: {
      color: colors.inverse900,
      ...typography['small-bold'],
      marginBottom: spacing.medium,
    },
    input: {
      height: 65,
      borderRadius: 10,
      borderWidth: 0,
      paddingLeft: 30,
      ...typography['small-bold'],
      color: colors.inverse900,
      backgroundColor: colors.neutral700,
    },
    inputDisabled: {
      backgroundColor: colors.inverse400,
      color: colors.inverse500,
    },
    errorText: {
      ...typography['xxSmall'],
      marginTop: spacing.small,
    },
  });
