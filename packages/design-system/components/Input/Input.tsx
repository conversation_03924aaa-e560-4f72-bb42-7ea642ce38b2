import React, { useState, useEffect } from 'react';
import {
  Text,
  View,
  TextInput,
  TextInputProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { InputStyles } from './Input.styles';
interface InputProps extends TextInputProps {
  label?: string;
  value: string;
  onChangeText: (text: string) => void;
  validator?: (value: string) => boolean;
  errorMessage?: string;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  validateOnChange?: boolean;
}

export const Input: React.FC<InputProps> = ({
  label,
  value,
  onChangeText,
  validator,
  errorMessage = 'Invalid input',
  containerStyle,
  inputStyle,
  labelStyle,
  validateOnChange = true,
  ...rest
}) => {
  const { colors, spacings, typography } = useTheme();
  const styles = InputStyles(colors, spacings, typography);
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [localValue, setLocalValue] = useState(value);

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const validate = (text: string) => {
    if (!validator) return true;
    const valid = validator(text);
    setIsValid(valid);
    return valid;
  };

  const handleChangeText = (text: string) => {
    setLocalValue(text);
    if (validateOnChange) {
      validate(text);
    }
    onChangeText(text);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && <Text style={[styles.label, labelStyle]}>{label}</Text>}
      <TextInput
        style={[
          styles.input,
          rest.editable === false && styles.inputDisabled,
          inputStyle,
        ]}
        value={localValue}
        onChangeText={handleChangeText}
        placeholderTextColor="rgba(255, 255, 255, 0.5)"
        {...rest}
      />
      {isValid === false && errorMessage && (
        <Text style={[styles.errorText, { color: colors.red }]}>
          {errorMessage}
        </Text>
      )}
    </View>
  );
};

export default Input;
