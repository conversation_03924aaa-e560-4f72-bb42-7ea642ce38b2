import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';

export const agentConfirmPlayEventStyles = (
  spacings: SpacingStyles,
  colors: ColorStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacings.large,
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.xxxxlarge,
    },
    title: {
      ...typography['large-bold'],
      textAlign: 'center',
      color: colors.inverse900,
    },
    subtitle: {
      ...typography.small,
      color: colors.inverse900,
      paddingTop: spacings.xxxlarge,
      textAlign: 'center',
    },
    buttonContainer: {
      width: '100%',
    },
    containerWithSecondAction: {
      flexDirection: 'column',
      justifyContent: 'center',
    },
    actionsContainer: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'center',
      paddingVertical: spacings.xxlarge,
    },
    cardAwardedSection: {
      flexDirection: 'row',
      justifyContent: 'space-evenly',
    },
    cardAwardedContainer: {
      alignItems: 'center',
    },
    cardAwardedIcon: {
      width: 30,
      height: 40,
      borderRadius: 5,
    },
    cardAwardedText: {
      color: colors.inverse900,
      ...typography['small-bold'],
      marginTop: spacings.medium,
    },
    playerNumberContainer: {
      width: 42,
      height: 42,
      borderRadius: 25,
      backgroundColor: colors.inverse900,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacings.medium,
    },
    playerNumberText: {
      ...typography['small-bold'],
      color: colors.inverse100,
    },
    playerName: {
      ...typography['small-bold'],
      color: colors.inverse900,
    },
    actionLabel: {
      ...typography['small-bold'],
      color: colors.inverse900,
      paddingRight: spacings.xxlarge,
    },
    actionRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    actionRowWhenSecondAction: {
      marginTop: spacings.xlarge,
      justifyContent: 'center',
    },
    playerContainerWithCardPresent: {
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
    },
    playerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'center',
    },
  });
