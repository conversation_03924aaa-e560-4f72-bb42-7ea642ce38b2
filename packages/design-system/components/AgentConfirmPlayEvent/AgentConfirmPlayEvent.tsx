import React, { forwardRef } from 'react';
import { View, Text } from 'react-native';
import { Button } from '../Button/Button';
import { BottomSheet } from '../BottomSheet/BottomSheet';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { agentConfirmPlayEventStyles } from './AgentConfirmPlayEvent.styles';
import { useTheme } from '../../context/ThemeContext';

type AgentConfirmPlayEventProps = {
  onBtnPress: () => void;
  title: string;
  firstAction: { label: string; number: string | null; name: string | null };
  secondAction?: { label: string; number: string | null; name: string | null };
  cardAwarded?: any;
  buttonText: string;
  infoText: string;
};

export const AgentConfirmPlayEvent = forwardRef<
  BottomSheetModal,
  AgentConfirmPlayEventProps
>(
  (
    {
      onBtnPress,
      title,
      firstAction,
      secondAction,
      buttonText,
      cardAwarded,
      infoText,
    },
    ref
  ) => {
    const { spacings, colors, typography } = useTheme();
    const styles = agentConfirmPlayEventStyles(spacings, colors, typography);

    return (
      <BottomSheet ref={ref}>
        <View style={[styles.container]}>
          <Text style={styles.title}>{title}</Text>
          {infoText && (
            <View>
              <Text style={styles.subtitle}>{infoText}</Text>
            </View>
          )}
          <View
            style={[
              styles.actionsContainer,
              cardAwarded && styles.cardAwardedSection,
              secondAction && styles.containerWithSecondAction,
            ]}
          >
            {cardAwarded && (
              <View style={styles.cardAwardedContainer}>
                <View
                  style={[
                    styles.cardAwardedIcon,
                    { backgroundColor: cardAwarded.selectedColor },
                  ]}
                />
                <Text style={styles.cardAwardedText}>{cardAwarded.label}</Text>
              </View>
            )}
            {firstAction?.number && firstAction?.name && (
              <View style={[styles.actionRow]}>
                {/* if SECOND ACTION, we need to display Player or Assist label before player details*/}
                {secondAction?.number && secondAction.name && (
                  <Text style={styles.actionLabel}>{firstAction.label}</Text>
                )}
                <View
                  style={[
                    styles.playerContainer,
                    cardAwarded && styles.playerContainerWithCardPresent,
                  ]}
                >
                  <View
                    style={[
                      styles.playerNumberContainer,
                      cardAwarded && { marginRight: 0 },
                    ]}
                  >
                    <Text style={styles.playerNumberText}>
                      {firstAction.number}
                    </Text>
                  </View>
                  <Text
                    style={[
                      styles.playerName,
                      cardAwarded && { marginTop: spacings.medium },
                    ]}
                  >
                    {firstAction.name}
                  </Text>
                </View>
              </View>
            )}
            {secondAction?.number && secondAction.name && (
              <View
                style={[
                  styles.actionRow,
                  secondAction?.name && styles.actionRowWhenSecondAction,
                ]}
              >
                <Text style={styles.actionLabel}>{secondAction.label}</Text>
                <View style={styles.playerContainer}>
                  <View style={styles.playerNumberContainer}>
                    <Text style={styles.playerNumberText}>
                      {secondAction.number}
                    </Text>
                  </View>
                  <Text style={styles.playerName}>{secondAction.name}</Text>
                </View>
              </View>
            )}
          </View>
          <Button
            secondary
            title={buttonText}
            containerStyle={styles.buttonContainer}
            onPress={onBtnPress}
          />
        </View>
      </BottomSheet>
    );
  }
);

AgentConfirmPlayEvent.displayName = 'AgentConfirmPlayEvent';

export default AgentConfirmPlayEvent;
