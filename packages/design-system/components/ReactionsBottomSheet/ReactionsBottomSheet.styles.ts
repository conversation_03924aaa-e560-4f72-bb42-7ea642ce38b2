import { Dimensions, StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';

export const reactionsBottomSheetStyles = (
  spacings: SpacingStyles,
  colors: ColorStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacings.medium,
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.xxlarge,
    },
    title: {
      ...typography['large-bold'],
      textAlign: 'center',
      color: colors.inverse900,
      paddingBottom: spacings.medium,
    },
    scrollViewContainer: {
      paddingHorizontal: spacings.large,
      marginVertical: spacings.small,
      maxHeight: Dimensions.get('window').height * 0.5,
    },
    reactionContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacings.small,
    },
    reactionUserName: {
      ...typography['small-bold'],
      color: colors.inverse900,
      marginLeft: spacings.large,
    },
    reactionUserReaction: {
      ...typography['xSmall'],
      color: colors.inverse900,
      marginLeft: spacings.large,
    },
  });
