import React, { forwardRef } from 'react';
import { View, Text } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { i18next } from '@scorescast/translations';
import { Reaction } from '@scorescast/formatters-constants';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useTheme } from '../../context/ThemeContext';
import { BottomSheet } from '../BottomSheet/BottomSheet';
import { reactionsBottomSheetStyles } from './ReactionsBottomSheet.styles';
import { UserAvatar } from '../UserAvatar/UserAvatar';

type ReactionsBottomSheetProps = {
  reactions: Reaction[];
};

export const ReactionsBottomSheet = forwardRef<
  BottomSheetModal,
  ReactionsBottomSheetProps
>(({ reactions }, ref) => {
  const { spacings, colors, typography } = useTheme();
  const styles = reactionsBottomSheetStyles(spacings, colors, typography);

  if (!reactions || reactions?.length === 0) {
    return null;
  }

  return (
    <BottomSheet ref={ref}>
      <View style={styles.container}>
        <Text style={styles.title}>{i18next.t('REACTIONS_SHEET.TITLE')}</Text>
        <ScrollView
          style={styles.scrollViewContainer}
          showsVerticalScrollIndicator={false}
        >
          {reactions.map((reaction) => (
            <View key={reaction.id} style={styles.reactionContainer}>
              <UserAvatar
                firstName={reaction.firstName}
                lastName={reaction.lastName}
                reaction={reaction}
              />
              <View>
                <Text style={styles.reactionUserName}>
                  {reaction.firstName} {reaction.lastName.charAt(0)}.
                </Text>
                <Text style={styles.reactionUserReaction}>
                  {i18next.t(`REACTIONS_SHEET.LABELS.${reaction.reactionType}`)}
                </Text>
              </View>
            </View>
          ))}
        </ScrollView>
      </View>
    </BottomSheet>
  );
});

ReactionsBottomSheet.displayName = 'ReactionsBottomSheet';
