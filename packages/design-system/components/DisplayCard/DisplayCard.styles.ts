import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const DisplayCardStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    card: {
      backgroundColor: colors.neutral700,
      borderRadius: spacings.large,
      padding: spacings.medium,
      flexDirection: 'column',
      alignItems: 'center',
      position: 'relative',
      paddingBottom: spacings.xxlarge,
    },
    image: {
      width: spacings.ultralarge,
      height: spacings.ultralarge,
      borderRadius: spacings.medium,
      marginRight: spacings.medium,
      marginTop: spacings.xxlarge,
    },
    placeholder: {
      width: spacings.ultralarge,
      height: spacings.ultralarge,
      borderRadius: spacings.medium,
      marginRight: spacings.medium,
      marginTop: spacings.xxlarge,
    },

    info: {
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: spacings.xxlarge,
      marginBottom: spacings.large,
      paddingHorizontal: spacings.medium,
    },
    title: {
      color: colors.inverse900,
      ...typography['large-bold'],
      marginBottom: spacings.medium,
      textAlign: 'center',
      alignSelf: 'stretch',
    },
    website: {
      color: colors.inverse900,
      ...typography['small-bold'],
      textAlign: 'center',
      alignSelf: 'stretch',
    },
    editButton: {
      position: 'absolute',
      top: 8,
      right: 8,
      padding: 8,
    },
  });
