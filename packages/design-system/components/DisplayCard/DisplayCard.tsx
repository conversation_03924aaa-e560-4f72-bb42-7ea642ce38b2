import React from 'react';
import { View, Text, Image, Pressable } from 'react-native';
import { IconName } from '../Icon/Icon.types';
import { Icon } from '../Icon/Icon';
import { DisplayCardStyles } from './DisplayCard.styles';
import { useTheme } from '../../context/ThemeContext';

interface ClubCardProps {
  image: string | null;
  title: string;
  website?: string;
  onEditPress?: () => void;
  onPress?: () => void;
}

export const DisplayCard: React.FC<ClubCardProps> = ({
  image,
  title,
  website,
  onEditPress,
  onPress,
}) => {
  const { colors, spacings, typography } = useTheme();
  const styles = DisplayCardStyles(colors, spacings, typography);

  return (
    <Pressable style={styles.card} onPress={onPress}>
      {onEditPress && (
        <Pressable style={styles.editButton} onPress={onEditPress}>
          <Icon name={IconName.EDIT_BTN} width={35} height={35} isPng />
        </Pressable>
      )}

      {image ? (
        <Image source={{ uri: image }} style={styles.image} />
      ) : (
        <View style={styles.placeholder}>
          <Icon name={IconName.FRAME} width={72} height={72} isPng />
        </View>
      )}
      <View style={styles.info}>
        {title ? (
          <Text style={styles.title} numberOfLines={2} ellipsizeMode="tail">
            {title}
          </Text>
        ) : null}

        {website ? (
          <Text style={styles.website} numberOfLines={1} ellipsizeMode="tail">
            {website}
          </Text>
        ) : null}
      </View>
    </Pressable>
  );
};
