import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const premiumBottomSheetStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacings.large,
      paddingBottom: spacings.xxlarge,
      alignItems: 'center',
    },
    title: {
      ...typography['large-bold'],
      color: colors.inverse900,
      paddingBottom: spacings.medium,
    },
    lottie: {
      width: 200,
      height: 200,
    },
    description: {
      ...typography['small'],
      color: colors.inverse900,
      textAlign: 'center',
      paddingTop: spacings.medium,
    },
    bold: {
      ...typography['small-bold'],
    },
    subDescription: {
      ...typography['small'],
      color: colors.inverse900,
      textAlign: 'center',
      paddingTop: spacings.medium,
      paddingBottom: spacings.xxlarge,
    },
  });
