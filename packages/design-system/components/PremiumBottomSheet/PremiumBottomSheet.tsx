import React, { forwardRef } from 'react';
import { View, Text } from 'react-native';
import { i18next } from '@scorescast/translations';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useTheme } from '../../context/ThemeContext';
import { BottomSheet } from '../BottomSheet/BottomSheet';
import { premiumBottomSheetStyles } from './PremiumBottomSheet.styles';
import LottieView from 'lottie-react-native';
import { Lotties } from '../Icon/Icons';
import { Button } from '../Button/Button';

type PremiumBottomSheetProps = {
  onCallbackPressed: () => void;
};

export const PremiumBottomSheet = forwardRef<
  BottomSheetModal,
  PremiumBottomSheetProps
>(({ onCallbackPressed }, ref) => {
  const { spacings, colors, typography } = useTheme();
  const styles = premiumBottomSheetStyles(colors, spacings, typography);

  return (
    <BottomSheet ref={ref}>
      <View style={styles.container}>
        <LottieView
          source={Lotties.premium}
          autoPlay
          loop
          style={styles.lottie}
        />
        <Text style={styles.title}>
          {i18next.t('PREMIUM_BOTTOM_SHEET.TITLE')}
        </Text>
        <Text style={styles.description}>
          {i18next.t('PREMIUM_BOTTOM_SHEET.DESCRIPTION')}
          <Text style={styles.bold}>
            {i18next.t('PREMIUM_BOTTOM_SHEET.BOLD_DESCRIPTION')}
          </Text>
        </Text>
        <Text style={styles.description}>
          {i18next.t('PREMIUM_BOTTOM_SHEET.SUB_DESCRIPTION')}
        </Text>
        <Text style={styles.subDescription}>
          {i18next.t('PREMIUM_BOTTOM_SHEET.SUB_DESCRIPTION_2')} {'\n'}
          <Text style={styles.bold}>
            {i18next.t('PREMIUM_BOTTOM_SHEET.SUB_DESCRIPTION_3')}
          </Text>
        </Text>
        <Button
          secondary
          title={i18next.t('PREMIUM_BOTTOM_SHEET.BUTTON')}
          onPress={onCallbackPressed}
        />
      </View>
    </BottomSheet>
  );
});

PremiumBottomSheet.displayName = 'PremiumBottomSheet';
