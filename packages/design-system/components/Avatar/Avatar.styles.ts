import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const avatarStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    name: {
      color: colors.inverse900,
      ...typography['medium-semibold'],
      paddingTop: spacings.large,
    },
    email: {
      color: colors.inverse850,
      ...typography['small'],
      paddingTop: spacings.small,
    },
  });
