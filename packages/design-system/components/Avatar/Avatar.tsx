import React from 'react';
import { View, Text, StyleProp, ViewStyle } from 'react-native';
import { Icon } from '../Icon/Icon';
import { IconName } from '../Icon/Icon.types';
import { avatarStyles } from './Avatar.styles';
import { useTheme } from '../../context/ThemeContext';
import { User } from '@scorescast/formatters-constants';

interface AvatarProps {
  user: User;
  containerStyle?: StyleProp<ViewStyle>;
  withName?: boolean;
  withEmail?: boolean;
}

export const Avatar = ({
  user,
  containerStyle,
  withName = false,
  withEmail = false,
}: AvatarProps) => {
  const { colors, spacings, typography } = useTheme();
  const styles = avatarStyles(colors, spacings, typography);

  return (
    <View style={containerStyle}>
      <Icon name={IconName.DEFAULT_AVATAR} width={100} height={100} isPng />
      {withName && (
        <Text style={styles.name}>
          {user.firstName} {user.lastName}
        </Text>
      )}
      {withEmail && <Text style={styles.email}>{user.email}</Text>}
    </View>
  );
};
