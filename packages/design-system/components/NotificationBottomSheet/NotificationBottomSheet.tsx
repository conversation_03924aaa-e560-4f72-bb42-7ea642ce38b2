import React, { forwardRef } from 'react';
import { View, Text } from 'react-native';
import { i18next } from '@scorescast/translations';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { Button } from '../Button/Button';
import { useTheme } from '../../context/ThemeContext';
import { BottomSheet } from '../BottomSheet/BottomSheet';
import { notificationBottomSheetStyles } from './NotificationBottomSheet.styles';
import LottieView from 'lottie-react-native';
import { Lotties } from '../Icon/Icons';

type NotificationBottomSheetProps = {
  onPressAllow: () => void;
  onPressDeny: () => void;
};

export const NotificationBottomSheet = forwardRef<
  BottomSheetModal,
  NotificationBottomSheetProps
>(({ onPressAllow, onPressDeny }, ref) => {
  const { spacings, colors, typography } = useTheme();
  const styles = notificationBottomSheetStyles(spacings, colors, typography);

  return (
    <BottomSheet ref={ref}>
      <View style={styles.container}>
        <LottieView
          source={Lotties.notification}
          autoPlay
          loop={false}
          style={styles.lottie}
          resizeMode="cover"
        />
        <Text style={styles.title}>
          {i18next.t('NOTIFICATIONS_SHEET.TITLE')}
        </Text>
        <Text style={styles.subtitle}>
          {i18next.t('NOTIFICATIONS_SHEET.DESCRIPTION')}
        </Text>
        <Button
          secondary
          title={i18next.t('NOTIFICATIONS_SHEET.BUTTON')}
          onPress={onPressAllow}
          containerStyle={styles.button}
        />
        <Text numberOfLines={1} style={styles.discard} onPress={onPressDeny}>
          {i18next.t('NOTIFICATIONS_SHEET.DISCARD')}
        </Text>
      </View>
    </BottomSheet>
  );
});

NotificationBottomSheet.displayName = 'NotificationBottomSheet';
