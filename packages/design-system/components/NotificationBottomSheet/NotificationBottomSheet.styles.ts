import { StyleSheet } from 'react-native';
import { SpacingStyles } from '../../theme/spacing';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';

export const notificationBottomSheetStyles = (
  spacings: SpacingStyles,
  colors: ColorStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacings.large,
      paddingTop: spacings.xxlarge,
      paddingBottom: spacings.xxxxlarge,
    },
    title: {
      ...typography['large-bold'],
      textAlign: 'center',
      color: colors.inverse900,
    },
    subtitle: {
      ...typography.small,
      color: colors.inverse900,
      paddingVertical: spacings.xxxlarge,
      textAlign: 'center',
    },
    lottie: {
      width: '60%',
      height: 250,
      alignSelf: 'center',
    },
    button: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    discard: {
      ...typography['small'],
      color: colors.inverse900,
      textDecorationLine: 'underline',
      marginTop: spacings.xlarge,
      textDecorationColor: colors.inverse900,
      alignSelf: 'center',
    },
  });
