import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';

export const bottomSheetStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles
) =>
  StyleSheet.create({
    container: {
      backgroundColor: colors.inverse200,
      borderTopLeftRadius: 50,
      borderTopRightRadius: 50,
    },
    indicator: {
      height: 6,
      width: 55,
      borderRadius: 3,
      backgroundColor: colors.inverse900,
      alignSelf: 'center',
      marginTop: spacings.xlarge,
    },
    keyboardAvoidingView: {
      marginBottom: 300,
    },
  });
