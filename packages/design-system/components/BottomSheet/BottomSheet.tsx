import React, {
  forwardRef,
  PropsWithChildren,
  useCallback,
  useEffect,
  useState,
} from 'react';
import { Keyboard, KeyboardAvoidingView, Platform, View } from 'react-native';
import {
  BottomSheetModal,
  BottomSheetView,
  BottomSheetBackdropProps,
  BottomSheetBackdrop,
} from '@gorhom/bottom-sheet';
import { useTheme } from '../../context/ThemeContext';
import { bottomSheetStyles } from './BottomSheet.styles';

type BottomSheetProps = {
  onClose?: () => void;
};

export const BottomSheet = forwardRef<
  BottomSheetModal,
  PropsWithChildren<BottomSheetProps>
>(({ onClose, children }, ref) => {
  const { colors, spacings } = useTheme();
  const styles = bottomSheetStyles(colors, spacings);

  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    const show = Keyboard.addListener('keyboardDidShow', (e) =>
      setKeyboardHeight(e.endCoordinates.height)
    );
    const hide = Keyboard.addListener('keyboardDidHide', () =>
      setKeyboardHeight(0)
    );

    return () => {
      show.remove();
      hide.remove();
    };
  }, []);

  const renderBackdrop = useCallback((props: BottomSheetBackdropProps) => {
    return (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
      />
    );
  }, []);

  return (
    <BottomSheetModal
      ref={ref}
      onDismiss={onClose}
      enableOverDrag={false}
      backgroundComponent={null}
      enableDynamicSizing={true}
      backdropComponent={renderBackdrop}
      keyboardBehavior="interactive"
      keyboardBlurBehavior="restore"
      handleComponent={null}
    >
      <BottomSheetView style={styles.container}>
        <View style={styles.indicator} />
        <KeyboardAvoidingView
          style={
            keyboardHeight > 0 && Platform.OS === 'ios'
              ? { marginBottom: keyboardHeight }
              : {}
          }
        >
          {children}
        </KeyboardAvoidingView>
      </BottomSheetView>
    </BottomSheetModal>
  );
});

BottomSheet.displayName = 'BottomSheet';
