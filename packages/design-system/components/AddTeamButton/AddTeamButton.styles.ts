import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const addTeamButtonStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
      opacity: 0.4,
    },
    iconContainer: {
      width: 60,
      height: 60,
      borderRadius: 10,
      backgroundColor: colors.neutral600,
      alignItems: 'center',
      justifyContent: 'center',
    },
    icon: {
      width: 20,
      height: 28,
      resizeMode: 'contain',
    },
    title: {
      ...typography['small'],
      color: colors.inverse900,
      paddingTop: spacings.medium,
      flexShrink: 1,
      textAlign: 'center',
    },
  });
