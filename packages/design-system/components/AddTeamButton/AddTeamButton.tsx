import React from 'react';
import { TouchableOpacity, Text, View } from 'react-native';
import { Icon } from '../Icon/Icon';
import { IconName } from '../Icon/Icon.types';
import { useTheme } from '../../context/ThemeContext';
import { addTeamButtonStyles } from './AddTeamButton.styles';
import { i18next } from '@scorescast/translations';

interface AddTeamButtonProps {
  onPress: () => void;
}

export const AddTeamButton = ({ onPress }: AddTeamButtonProps) => {
  const { colors, spacings, typography } = useTheme();
  const styles = addTeamButtonStyles(colors, spacings, typography);
  return (
    <TouchableOpacity
      activeOpacity={0.9}
      style={styles.container}
      onPress={onPress}
    >
      <View style={styles.iconContainer}>
        <Icon name={IconName.PLUS} isPng styles={styles.icon} />
      </View>
      <Text style={styles.title}>{i18next.t('ADD_A_TEAM.TITLE')}</Text>
    </TouchableOpacity>
  );
};
