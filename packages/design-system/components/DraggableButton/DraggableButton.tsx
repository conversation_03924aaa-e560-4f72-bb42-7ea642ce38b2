import React, { useRef, useMemo } from 'react';
import {
  View,
  Animated,
  PanResponder,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { DraggableButtonStyles } from './DraggableButton.styles';
import { Icon } from '../Icon/Icon';
import { IconName } from '../Icon/Icon.types';

const width = 295;
const CIRCLE_SIZE = 45;

interface DraggableButtonProps {
  label: string;
  onPress: () => void;
  disabled?: boolean;
  containerStyle?: StyleProp<ViewStyle>;
}

export const DraggableButton = ({
  label,
  onPress,
  disabled = false,
  containerStyle,
}: DraggableButtonProps) => {
  const { colors, spacings, typography } = useTheme();
  const styles = DraggableButtonStyles(colors, spacings, typography);
  const translateX = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(1)).current;

  const panResponder = useMemo(
    () =>
      PanResponder.create({
        onStartShouldSetPanResponder: () => !disabled,
        onMoveShouldSetPanResponder: () => !disabled,
        onPanResponderGrant: () => {
          if (disabled) return;
          Animated.timing(opacity, {
            toValue: 0.5,
            duration: 200,
            useNativeDriver: true,
          }).start();
        },
        onPanResponderMove: (_, gesture) => {
          if (disabled) return;
          const newX = Math.max(
            0,
            Math.min(width * 0.93 - CIRCLE_SIZE, gesture.dx)
          );
          translateX.setValue(newX);
        },
        onPanResponderRelease: (_, gesture) => {
          if (disabled) return;
          Animated.timing(opacity, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }).start();

          if (gesture.dx > width * 0.7 - CIRCLE_SIZE - 10) {
            onPress();
            Animated.timing(translateX, {
              toValue: width * 0.93 - CIRCLE_SIZE,
              duration: 200,
              useNativeDriver: true,
            }).start(() => {
              setTimeout(() => {
                Animated.spring(translateX, {
                  toValue: 0,
                  friction: 7,
                  tension: 40,
                  useNativeDriver: true,
                }).start();
              }, 100);
            });
          } else {
            Animated.spring(translateX, {
              toValue: 0,
              friction: 7,
              tension: 40,
              useNativeDriver: true,
            }).start();
          }
        },
      }),
    [disabled, onPress]
  );

  return (
    <View style={(styles.viewContainer, containerStyle)}>
      <Animated.View
        style={[styles.container, { opacity }, disabled && styles.disabled]}
      >
        <Animated.Text
          style={[styles.label, { opacity }, disabled && styles.labelDisabled]}
        >
          {label}
        </Animated.Text>

        <Animated.View
          style={[
            styles.circleWrapper,
            {
              transform: [{ translateX }],
            },
          ]}
          {...panResponder.panHandlers}
        >
          <View style={[styles.circle, disabled && styles.circleDisabled]}>
            <Icon
              name={IconName.ARROW_RIGHT_LIGHT}
              isPng
              height={20}
              width={20}
            />
          </View>
        </Animated.View>
      </Animated.View>
    </View>
  );
};
