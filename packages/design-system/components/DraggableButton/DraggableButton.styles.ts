import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const DraggableButtonStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    viewContainer: {
      width: '100%',
      height: 55,
    },
    container: {
      width: 295,
      height: 55,
      backgroundColor: colors.neutral100,
      borderRadius: 50,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
      alignSelf: 'center',
    },
    disabled: {
      backgroundColor: colors.inverse400,
    },
    label: {
      ...typography['medium-semibold'],
      color: colors.inverse100,
    },
    labelDisabled: {
      color: colors.inverse850,
    },
    circleWrapper: {
      position: 'absolute',
      left: spacings.medium,
    },
    circle: {
      width: 45,
      height: 45,
      backgroundColor: colors.neutral900,
      borderRadius: 45 / 2,
      justifyContent: 'center',
      alignItems: 'center',
    },
    circleDisabled: {
      backgroundColor: colors.inverse200,
    },
  });
