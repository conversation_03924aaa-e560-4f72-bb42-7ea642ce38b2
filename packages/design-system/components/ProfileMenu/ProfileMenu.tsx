import React from 'react';
import { View, ViewStyle, StyleProp, ScrollView } from 'react-native';
import { IconName } from '../Icon/Icon.types';
import { useTheme } from '../../context/ThemeContext';
import { profileMenuStyles } from './ProfileMenu.styles';
import { ProfileMenuItem } from '../ProfileMenuItem/ProfileMenuItem';

interface ProfileMenuItem {
  icon?: IconName;
  title: string;
  hideArrow?: boolean;
  withSeparator?: boolean;
  disabled?: boolean;
  isHighlighted: boolean;
  hidden?: boolean;
  onPress?: () => void;
}

interface ProfileMenuItems {
  section: string;
  items: ProfileMenuItem[];
}

interface ProfileMenuProps {
  menuItems: ProfileMenuItems[];
  containerStyle?: StyleProp<ViewStyle>;
}

export const ProfileMenu: React.FC<ProfileMenuProps> = ({
  menuItems,
  containerStyle,
}) => {
  const { colors, spacings } = useTheme();
  const styles = profileMenuStyles(colors, spacings);
  return (
    <ScrollView style={[containerStyle]} showsVerticalScrollIndicator={false}>
      {menuItems.map((section, sectionIndex) => (
        <View
          key={sectionIndex}
          style={[
            section.section === 'single' && styles.singleSection,
            section.section === 'grouped' && styles.groupedSection,
          ]}
        >
          {section.items.map((item, itemIndex) =>
            !item.hidden ? (
              <ProfileMenuItem
                isHighlighted={item.isHighlighted}
                withSeparator={
                  section.section === 'grouped' &&
                  itemIndex !== section.items.length - 1
                }
                hideArrow={item.hideArrow}
                disabled={item.disabled}
                key={item.title}
                icon={item.icon}
                title={item.title}
                onPress={item.onPress}
              />
            ) : null
          )}
        </View>
      ))}
    </ScrollView>
  );
};
