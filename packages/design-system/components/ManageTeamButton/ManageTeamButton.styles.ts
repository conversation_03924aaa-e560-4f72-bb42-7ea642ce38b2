import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const manageTeamButtonStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    container: {
      borderRadius: 10,
      overflow: 'hidden',
      backgroundColor: colors.neutral700,
      position: 'relative',
    },

    buttonContainer: {
      padding: spacings.xlarge,
      borderRadius: 10,
      backgroundColor: colors.neutral700,
      justifyContent: 'flex-start',
      flexDirection: 'row',
    },
    deleteButtonContainer: {
      position: 'absolute',
      right: 0,
      top: 0,
      bottom: 0,
      width: 70,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.neutral100,
      borderRadius: 10,
      opacity: 0,
    },
    deleteButton: {
      padding: spacings.medium,
    },
    teamAvatarContainer: {
      flexDirection: 'row',
      flex: 1,
      alignItems: 'center',
      height: 45,
    },
    teamAvatarIcon: {
      width: 45,
      height: 45,
    },
    teamAvatarName: {
      ...typography['small'],
      color: colors.inverse900,
      paddingTop: 0,
      paddingLeft: spacings.large,
    },
    innerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
  });
