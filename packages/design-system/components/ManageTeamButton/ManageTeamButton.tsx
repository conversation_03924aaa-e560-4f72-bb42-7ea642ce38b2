import React, { useCallback, useRef } from 'react';
import { View, TouchableOpacity, Animated, PanResponder } from 'react-native';
import { Team } from '@scorescast/formatters-constants';
import { TeamAvatar } from '../TeamAvatar/TeamAvatar';
import { useTheme } from '../../context/ThemeContext';
import { manageTeamButtonStyles } from './ManageTeamButton.styles';
import { Icon } from '../Icon/Icon';
import { IconName } from '../Icon/Icon.types';

interface ManageTeamButtonProps {
  team: Team;
  onPressDelete: (team: Team) => void;
  onPressButton: (team: Team) => void;
  disabled?: boolean;
}

export const ManageTeamButton: React.FC<ManageTeamButtonProps> = ({
  team,
  onPressDelete,
  onPressButton,
  disabled = false,
}) => {
  const panX = useRef(new Animated.Value(0)).current;
  const { colors, spacings, typography } = useTheme();
  const styles = manageTeamButtonStyles(colors, spacings, typography);
  const buttonWidth = 70;
  const threshold = 50;

  const showDelete = () => {
    Animated.timing(panX, {
      toValue: -buttonWidth,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const hideDelete = () => {
    Animated.spring(panX, {
      toValue: 0,
      useNativeDriver: false,
    }).start();
  };

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => !disabled,
      onMoveShouldSetPanResponder: () => !disabled,
      onPanResponderMove: (evt, gestureState) => {
        if (gestureState.dx < 0) {
          panX.setValue(gestureState.dx);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (Math.abs(gestureState.dx) > threshold) {
          showDelete();
        } else {
          hideDelete();
        }
      },
    })
  ).current;

  const onPress = useCallback(() => {
    hideDelete();
    onPressButton(team);
  }, [hideDelete, onPressButton, team]);

  const onDelete = useCallback(() => {
    hideDelete();
    onPressDelete(team);
  }, [onPressDelete, hideDelete, team]);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={onPress}
        style={{ overflow: 'hidden' }}
      >
        <Animated.View
          {...panResponder.panHandlers}
          style={[styles.buttonContainer]}
        >
          <TeamAvatar
            disabled
            team={{ name: team.name, image: team.image }}
            containerStyle={styles.teamAvatarContainer}
            imageStyle={styles.teamAvatarIcon}
            nameStyle={styles.teamAvatarName}
          />
        </Animated.View>
      </TouchableOpacity>

      <Animated.View
        style={[
          styles.deleteButtonContainer,
          {
            opacity: panX.interpolate({
              inputRange: [-buttonWidth, -threshold],
              outputRange: [1, 0],
              extrapolate: 'clamp',
            }),
          },
        ]}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={onDelete}
          style={styles.deleteButton}
        >
          <Icon name={IconName.DELETE} isPng width={20} height={20} />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};
