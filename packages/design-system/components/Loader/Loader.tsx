import React from 'react';
import { View } from 'react-native';
import LottieView from 'lottie-react-native';
import { useTheme } from '../../context/ThemeContext';
import { loaderStyles } from './Loader.styles';
import { Lotties } from '../Icon/Icons';

export const Loader = () => {
  const { colors } = useTheme();
  const styles = loaderStyles(colors);

  return (
    <View style={styles.container}>
      <LottieView
        source={Lotties.loadingLogo}
        autoPlay
        loop
        style={styles.lottie}
      />
    </View>
  );
};
