import React from 'react';
import { StyleProp, TouchableOpacity, ViewStyle } from 'react-native';
import { Icon } from '../Icon/Icon';
import { IconName } from '../Icon/Icon.types';
import { circleButtonStyles } from './CircleButton.styles';
import { useTheme } from '../../context/ThemeContext';

interface CircleButtonProps {
  onPress: () => void;
  disabled?: boolean;
  icon: IconName;
  containerStyle?: StyleProp<ViewStyle>;
}

export const CircleButton: React.FC<CircleButtonProps> = ({
  icon,
  onPress,
  disabled,
  containerStyle,
}) => {
  const { colors } = useTheme();
  const styles = circleButtonStyles(colors);
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={onPress}
      style={[styles.container, containerStyle, disabled && styles.disabled]}
      disabled={disabled}
    >
      <Icon name={icon} isPng={true} width={20} height={18} />
    </TouchableOpacity>
  );
};
