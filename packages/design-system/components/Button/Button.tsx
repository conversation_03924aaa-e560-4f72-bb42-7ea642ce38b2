import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleProp,
  ViewStyle,
  View,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { buttonStyles } from './Button.styles';
import { Icon } from '../Icon/Icon';
import { IconName } from '../Icon/Icon.types';

interface ButtonProps {
  title: string;
  disabled?: boolean;
  loading?: boolean;
  containerStyle?: StyleProp<ViewStyle>;
  containerTitleStyle?: StyleProp<ViewStyle>;
  iconStyle?: StyleProp<ViewStyle>;
  icon?: IconName;
  pngDimensions?: number;
  secondary?: boolean;
  tertiary?: boolean;
  shadow?: boolean;
  onPress: () => void;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  disabled,
  loading,
  containerStyle,
  containerTitleStyle,
  iconStyle,
  pngDimensions,
  icon,
  secondary,
  tertiary,
  shadow,
  onPress,
}) => {
  const { colors, spacings, typography } = useTheme();
  const styles = buttonStyles(colors, typography, spacings);

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={[
        styles.container,
        secondary && styles.secondaryContainer,
        tertiary && styles.tertiaryContainer,
        shadow && styles.shadowContainer,
        containerStyle,
        !icon && styles.centerContainer,
        disabled && styles.buttonDisabled,
      ]}
      disabled={disabled}
      onPress={onPress}
    >
      {loading ? (
        <ActivityIndicator size="small" color={colors.secondary900} />
      ) : (
        <>
          {icon && (
            <View style={[styles.iconContainer, iconStyle]}>
              <Icon
                isPng={true}
                name={icon}
                width={pngDimensions ?? 19}
                height={pngDimensions ?? 19}
              />
            </View>
          )}
          <Text
            numberOfLines={1}
            style={[
              styles.title,
              secondary && styles.secondaryTitle,
              tertiary && styles.tertiaryTitle,
              shadow && styles.shadowTitle,
              containerTitleStyle,
              disabled && styles.disabledTitle,
            ]}
          >
            {title}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
};
