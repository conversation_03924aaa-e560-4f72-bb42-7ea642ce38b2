import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { TypographyStyles } from '../../theme/typography';
import { SpacingStyles } from '../../theme/spacing';

export const buttonStyles = (
  colors: ColorStyles,
  typography: TypographyStyles,
  spacings: SpacingStyles
) =>
  StyleSheet.create({
    container: {
      width: 295,
      alignSelf: 'center',
      backgroundColor: colors.neutral700,
      borderRadius: 50,
      height: 55,
      paddingHorizontal: spacings.medium,
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
    },
    secondaryContainer: {
      backgroundColor: colors.neutral100,
    },
    tertiaryContainer: {
      backgroundColor: colors.inverse400,
    },
    shadowContainer: {
      borderColor: colors.neutral100,
      borderWidth: 1,
      backgroundColor: 'transparent',
    },
    centerContainer: {
      justifyContent: 'center',
    },
    iconContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: spacings.xlarge,
    },
    title: {
      color: colors.inverse900,
      ...typography['medium-semibold'],
      textAlign: 'center',
    },
    secondaryTitle: {
      color: colors.inverse100,
    },
    tertiaryTitle: {
      color: colors.inverse900,
    },
    shadowTitle: {
      color: colors.neutral100,
    },
    disabledTitle: {
      color: colors.inverse850,
    },
    buttonDisabled: {
      backgroundColor: colors.inverse400,
    },
  });
