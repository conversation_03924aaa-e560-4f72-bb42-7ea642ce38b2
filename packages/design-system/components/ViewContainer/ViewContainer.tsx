import React, { useRef, useEffect } from 'react';
import { Animated, Dimensions, View } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { viewContainerStyles } from './ViewContainer.styles';
interface ViewContainerProps {
  children: React.ReactNode;
  animated?: boolean;
}

export const ViewContainer: React.FC<ViewContainerProps> = ({
  children,
  animated = false,
}) => {
  const { colors, spacings } = useTheme();
  const styles = viewContainerStyles(colors, spacings);
  const translateX = useRef(
    new Animated.Value(Dimensions.get('window').width)
  ).current;

  useEffect(() => {
    Animated.timing(translateX, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  if (animated) {
    return (
      <Animated.View
        style={[styles.container, { transform: [{ translateX }] }]}
      >
        {children}
      </Animated.View>
    );
  }
  return <View style={styles.container}>{children}</View>;
};
