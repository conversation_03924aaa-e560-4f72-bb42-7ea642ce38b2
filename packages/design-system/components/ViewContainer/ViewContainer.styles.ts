import { Platform, StatusBar, StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';

export const viewContainerStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles
) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacings.xxlarge,
      paddingTop:
        Platform.OS === 'android'
          ? (StatusBar.currentHeight ?? 0 + spacings.large)
          : 0,
      backgroundColor: colors.neutral900,
      flex: 1,
    },
  });
