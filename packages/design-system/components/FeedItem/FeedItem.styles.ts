import { StyleSheet } from 'react-native';
import { ColorStyles } from '../../theme/colors.light';
import { SpacingStyles } from '../../theme/spacing';
import { TypographyStyles } from '../../theme/typography';

export const feedItemStyles = (
  colors: ColorStyles,
  spacings: SpacingStyles,
  typography: TypographyStyles
) =>
  StyleSheet.create({
    feedItemCard: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      height: 60,
      marginVertical: spacings.small,
      borderRadius: 5,
    },
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      paddingHorizontal: spacings.large,
    },
    textContainer: {
      justifyContent: 'center',
      paddingHorizontal: spacings.large,
    },
    sectionHeader: {
      ...typography['medium-bold'],
      color: colors.inverse900,
      paddingVertical: spacings.medium,
    },
    eventTitle: {
      ...typography['xSmall-bold'],
      color: colors.inverse900,
    },
    eventMetadata: {
      ...typography['xxSmall-bold'],
      color: colors.inverse900,
    },
    centerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    centerIcon: {
      marginRight: spacings.medium,
    },
    centerMetadata: {
      marginLeft: spacings.medium,
    },
    popOver: {
      borderRadius: 24,
    },
  });
