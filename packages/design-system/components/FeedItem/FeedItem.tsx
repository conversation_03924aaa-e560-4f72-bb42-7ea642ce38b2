import React, { useCallback, useMemo, useRef } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Popover from 'react-native-popover-view';
import {
  FEED_EVENT_TYPES,
  FEED_EVENT_TYPES_KEYS,
  Reaction as ReactionModel,
} from '@scorescast/formatters-constants';
import { i18next } from '@scorescast/translations';
import { useTheme } from '../../context/ThemeContext';
import { feedItemStyles } from './FeedItem.styles';
import { ReactionsBadge } from '../ReactionsBadge/ReactionsBadge';
import { Icon } from '../Icon/Icon';
import { ReactionsItems } from '../ReactionsItems/ReactionsItems';

interface FeedItem {
  period?: string;
  eventType?: keyof typeof FEED_EVENT_TYPES;
  side?: 'left' | 'right';
  metadata?: Record<string, string>;
  isHeader?: boolean;
  title?: string;
  reactions?: ReactionModel[];
  id?: number;
}

interface FeedItemProps {
  item: FeedItem;
  onReactionPress: (type: string, id: number) => void;
}

export const FeedItem: React.FC<FeedItemProps> = ({
  item,
  onReactionPress,
}) => {
  const { colors, spacings, typography, gradientColors } = useTheme();
  const popoverRef = useRef<Popover>(null);
  const styles = feedItemStyles(colors, spacings, typography);
  const { id, eventType, side, metadata, reactions } = item;
  const isLeft = side === 'left';
  const isCenter = !side;
  const isLastItem = item?.eventType === FEED_EVENT_TYPES_KEYS.END;
  const eventInfo = FEED_EVENT_TYPES[eventType!];

  const metadataText = Object.entries(metadata || {})
    .reverse()
    .map(([key, value]) => {
      const metadataTranslated =
        i18next.t(`MATCH_FEED.FEED_ITEMS.METADATA.${key}`, { value }) || key;
      return `${metadataTranslated} `;
    });

  const onReact = useCallback(
    (type: string) => {
      onReactionPress(type, id!);
    },
    [onReactionPress, id]
  );

  const getGradientColors = useMemo(() => {
    if (item?.side) {
      return gradientColors.gradientFeed;
    }
    if (isLastItem) {
      return gradientColors.gradientFeedDefault;
    }
    return gradientColors.gradientFeedInactive;
  }, [item?.side, isLastItem]);

  if (item.isHeader && item?.title) {
    return <Text style={styles.sectionHeader}>{item.title}</Text>;
  }

  if (!eventInfo || !eventInfo?.labels) {
    return <></>;
  }

  return (
    <Popover
      ref={popoverRef}
      popoverStyle={styles.popOver}
      from={(_, showPopover) => (
        <View>
          <LinearGradient
            start={{ x: item?.side === 'left' ? 0 : 1, y: 0 }}
            end={{ x: item?.side === 'left' ? 1 : 0, y: 0 }}
            colors={getGradientColors}
            style={[
              styles.feedItemCard,
              reactions &&
                reactions.length > 0 && {
                  marginBottom: spacings.xlarge,
                },
            ]}
          >
            <TouchableOpacity
              onLongPress={() => {
                showPopover();
              }}
              activeOpacity={0.9}
              disabled={isCenter}
              style={[
                styles.container,
                {
                  justifyContent: isCenter
                    ? 'center'
                    : isLeft
                      ? 'flex-start'
                      : 'flex-end',
                },
              ]}
            >
              {isLeft && (
                <Icon name={eventInfo?.icon} height={24} width={24} isPng />
              )}
              <View style={styles.textContainer}>
                <View style={isCenter && styles.centerContainer}>
                  {isCenter && (
                    <Icon
                      name={eventInfo?.icon}
                      height={24}
                      width={24}
                      isPng
                      styles={styles.centerIcon}
                    />
                  )}
                  <Text
                    style={[
                      styles.eventTitle,
                      {
                        alignSelf: isLeft
                          ? 'flex-start'
                          : isCenter
                            ? 'center'
                            : 'flex-end',
                      },
                      isLastItem && {
                        color: colors.inverse100,
                      },
                    ]}
                  >
                    {eventInfo?.labels?.title}
                  </Text>
                </View>
                {metadataText && metadataText.length > 0 ? (
                  <Text
                    style={[
                      styles.eventMetadata,
                      {
                        alignSelf: isLeft
                          ? 'flex-start'
                          : isCenter
                            ? 'center'
                            : 'flex-end',
                      },
                      isLastItem && {
                        color: colors.inverse100,
                      },
                      isCenter && styles.centerMetadata,
                    ]}
                  >
                    {metadataText}
                  </Text>
                ) : null}
              </View>
              {!isLeft && !isCenter && (
                <Icon name={eventInfo?.icon} height={24} width={24} isPng />
              )}
            </TouchableOpacity>
          </LinearGradient>
          <ReactionsBadge
            reactions={reactions}
            position={isLeft ? 'left' : 'right'}
          />
        </View>
      )}
    >
      <ReactionsItems popoverRef={popoverRef} handlePress={onReact} />
    </Popover>
  );
};
