export type ColorStyles = {
  primary: string;
  tertiary: string;
  secondary900: string;
  secondary500: string;
  neutral900: string;
  neutral800: string;
  neutral700: string;
  neutral600: string;
  neutral500: string;
  neutral100: string;
  inverse900: string;
  inverse850: string;
  inverse700: string;
  inverse500: string;
  inverse400: string;
  inverse200: string;
  inverse100: string;
  accent900: string;
  accent800: string;
  accent100: string;
  shadowColor: string;
  red: string;
  red25: string;
  yellow: string;
  yellow25: string;
  green: string;
  pink: string;
  purple: string;
  darkGray: string;
  lightBlue: string;
};

export const Colors: ColorStyles = {
  primary: '#FFFFFF',
  tertiary: '#141414',
  secondary900: '#aa8df9',
  secondary500: 'rgba(170, 141, 249, 0.5)',
  neutral900: '#E5DDFD',
  neutral800: '#DAD1F2',
  neutral700: '#FFFFFF',
  neutral600: '#F0EDF7',
  neutral500: '#FFFFFF',
  neutral100: '#341F3D',
  inverse900: '#141414',
  inverse850: 'rgba(20, 20, 20, 0.5)',
  inverse700: '#FFFFFF',
  inverse500: '#8E8293',
  inverse400: '#D2CDD4',
  inverse200: '#F7F7F7',
  inverse100: '#FFFFFF',
  accent900: '#FFEE5A',
  accent800: '#FFFFFF',
  accent100: '#FFFFFF',
  shadowColor: '#00000040',
  red: '#FF0C0C',
  red25: '#770808',
  yellow: '#FFEE5A',
  yellow25: '#e6cf00',
  green: '#81C784',
  pink: '#FFB7E6',
  purple: '#783CC6',
  darkGray: '#A0A0A0',
  lightBlue: '#BAF8FC',
};
export const GradientColors = {
  gradientLive: [Colors.neutral700, Colors.secondary500],
  gradientEnd: [Colors.neutral700, Colors.neutral800],
  gradientUpcoming: [Colors.neutral700, Colors.neutral700],
  gradientFeed: [Colors.secondary500, Colors.neutral700],
  gradientFeedInactive: [Colors.neutral700, Colors.neutral700],
  gradientFeedDefault: [Colors.inverse900, Colors.inverse900],
};

export const UserAvatarsColors = [
  { backgroundColor: Colors.pink, textColor: Colors.inverse900 },
  { backgroundColor: Colors.purple, textColor: Colors.inverse100 },
  { backgroundColor: Colors.darkGray, textColor: Colors.inverse100 },
  { backgroundColor: Colors.lightBlue, textColor: Colors.inverse900 },
];
