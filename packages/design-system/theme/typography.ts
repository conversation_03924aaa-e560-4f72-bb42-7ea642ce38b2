type FontWeightType =
  | 'normal'
  | 'bold'
  | '100'
  | '200'
  | '300'
  | '400'
  | '500'
  | '600'
  | '700'
  | '800'
  | '900';

type FontStyle = {
  fontSize: number;
  fontWeight: FontWeightType;
};

export type TypographyStyles = {
  xxSmall: FontStyle;
  'xxSmall-bold': FontStyle;
  xSmall: FontStyle;
  'xSmall-bold': FontStyle;
  'xSmall-semibold': FontStyle;
  small: FontStyle;
  'small-bold': FontStyle;
  'small-semibold': FontStyle;
  medium: FontStyle;
  'medium-bold': FontStyle;
  'medium-semibold': FontStyle;
  large: FontStyle;
  'large-bold': FontStyle;
  xLarge: FontStyle;
  'xLarge-bold': FontStyle;
  xxLarge: FontStyle;
  'xxLarge-bold': FontStyle;
};

export const Typography: TypographyStyles = {
  xxSmall: { fontSize: 12, fontWeight: 'normal' },
  'xxSmall-bold': { fontSize: 12, fontWeight: '700' },
  xSmall: { fontSize: 15, fontWeight: 'normal' },
  'xSmall-bold': { fontSize: 15, fontWeight: '700' },
  'xSmall-semibold': { fontSize: 15, fontWeight: '500' },
  small: { fontSize: 17, fontWeight: 'normal' },
  'small-bold': { fontSize: 17, fontWeight: '700' },
  'small-semibold': { fontSize: 17, fontWeight: '500' },
  medium: { fontSize: 20, fontWeight: 'normal' },
  'medium-bold': { fontSize: 20, fontWeight: '700' },
  'medium-semibold': { fontSize: 20, fontWeight: '500' },
  large: { fontSize: 25, fontWeight: 'normal' },
  'large-bold': { fontSize: 25, fontWeight: '700' },
  xLarge: { fontSize: 32, fontWeight: 'normal' },
  'xLarge-bold': { fontSize: 32, fontWeight: '700' },
  xxLarge: { fontSize: 36, fontWeight: 'normal' },
  'xxLarge-bold': { fontSize: 36, fontWeight: '700' },
};
