import React, { createContext, useContext } from 'react';
import { useColorScheme } from 'react-native';
import {
  Colors as ColorsLight,
  GradientColors as GradientColorsLight,
  UserAvatarsColors as UserAvatarsColorsLight,
} from '../theme/colors.light';
import {
  Colors as ColorsDark,
  GradientColors as GradientColorsDark,
  UserAvatarsColors as UserAvatarsColorsDark,
} from '../theme/colors.dark';
import { Spacings } from '../theme/spacing';
import { Typography } from '../theme/typography';
import { IconsDark, IconsLight } from '../components/Icon/Icons';

type Theme = {
  isDarkMode: boolean;
  colors: typeof ColorsLight | typeof ColorsDark;
  spacings: typeof Spacings;
  typography: typeof Typography;
  gradientColors: typeof GradientColorsLight | typeof GradientColorsDark;
  userAvatarsColors:
    | typeof UserAvatarsColorsLight
    | typeof UserAvatarsColorsDark;
  icons: typeof IconsDark | typeof IconsLight;
};

const ThemeContext = createContext<Theme>({} as Theme);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const isDarkMode = useColorScheme() === 'dark';

  const theme: Theme = {
    isDarkMode,
    colors: isDarkMode ? ColorsDark : ColorsLight,
    spacings: Spacings,
    typography: Typography,
    gradientColors: isDarkMode ? GradientColorsDark : GradientColorsLight,
    icons: isDarkMode ? IconsDark : IconsLight,
    userAvatarsColors: isDarkMode
      ? UserAvatarsColorsDark
      : UserAvatarsColorsLight,
  };
  return (
    <ThemeContext.Provider value={theme}>{children}</ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);

  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }

  return context;
};
