{"v": "4.8.0", "meta": {"g": "LottieFiles AE 3.4.3", "a": "", "k": "", "d": "", "tc": ""}, "fr": 29.9700012207031, "ip": 0, "op": 45.0000018328876, "w": 270, "h": 600, "nm": "thimbsup", "ddd": 0, "assets": [{"id": "image_0", "w": 160, "h": 160, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "thumbs-up_1f44d.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [100]}, {"t": 45.0000018328876, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [179.5, 579, 0], "to": [0, 0, 0], "ti": [69, 123, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [115.5, 344, 0], "to": [-139, -189, 0], "ti": [0, 0, 0]}, {"t": 42.0000017106951, "s": [125.5, 25, 0]}], "ix": 2}, "a": {"a": 0, "k": [80, 80, 0], "ix": 1}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "n", "pt": {"a": 0, "k": {"i": [[0, 0], [-175, 275], [0, 0]], "o": [[0, 0], [175, -275], [0, 0]], "v": [[82.5, 75], [72.5, -520], [62.5, -1215]], "c": false}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "ip": 12.00000048877, "op": 45.0000018328876, "st": 12.00000048877, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "thumbs-up_1f44d.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 15, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [100]}, {"t": 45.0000018328876, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [153.5, 579, 0], "to": [0, 0, 0], "ti": [-91, 207, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [119.5, 316, 0], "to": [95, -147, 0], "ti": [0, 0, 0]}, {"t": 45.0000018328876, "s": [125.5, 25, 0]}], "ix": 2}, "a": {"a": 0, "k": [80, 80, 0], "ix": 1}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "n", "pt": {"a": 0, "k": {"i": [[0, 0], [-175, 275], [0, 0]], "o": [[0, 0], [175, -275], [0, 0]], "v": [[82.5, 75], [72.5, -520], [62.5, -1215]], "c": false}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "ip": 15.0000006109625, "op": 45.0000018328876, "st": 15.0000006109625, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "thumbs-up_1f44d.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [100]}, {"t": 38.0000015477717, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [153.5, 579, 0], "to": [0, 0, 0], "ti": [-91, 207, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [119.5, 316, 0], "to": [95, -147, 0], "ti": [0, 0, 0]}, {"t": 38.0000015477717, "s": [125.5, 25, 0]}], "ix": 2}, "a": {"a": 0, "k": [80, 80, 0], "ix": 1}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "n", "pt": {"a": 0, "k": {"i": [[0, 0], [-175, 275], [0, 0]], "o": [[0, 0], [175, -275], [0, 0]], "v": [[82.5, 75], [72.5, -520], [62.5, -1215]], "c": false}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "ip": 8.00000032584668, "op": 37.0000015070409, "st": 8.00000032584668, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "thumbs-up_1f44d.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [100]}, {"t": 34.0000013848484, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [153.5, 579, 0], "to": [0, 0, 0], "ti": [137, 119, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [107.5, 316, 0], "to": [-89, -115, 0], "ti": [0, 0, 0]}, {"t": 34.0000013848484, "s": [125.5, 25, 0]}], "ix": 2}, "a": {"a": 0, "k": [80, 80, 0], "ix": 1}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "n", "pt": {"a": 0, "k": {"i": [[0, 0], [-175, 275], [0, 0]], "o": [[0, 0], [175, -275], [0, 0]], "v": [[82.5, 75], [72.5, -520], [62.5, -1215]], "c": false}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "ip": 4.00000016292334, "op": 33.0000013441176, "st": 4.00000016292334, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "thumbs-up_1f44d.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 15, "s": [100]}, {"t": 30.0000012219251, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [173.5, 579, 0], "to": [0, 0, 0], "ti": [137, 119, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [107.5, 316, 0], "to": [-89, -115, 0], "ti": [0, 0, 0]}, {"t": 30.0000012219251, "s": [125.5, 25, 0]}], "ix": 2}, "a": {"a": 0, "k": [80, 80, 0], "ix": 1}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "n", "pt": {"a": 0, "k": {"i": [[0, 0], [-175, 275], [0, 0]], "o": [[0, 0], [175, -275], [0, 0]], "v": [[82.5, 75], [72.5, -520], [62.5, -1215]], "c": false}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "ip": 0, "op": 29.0000011811942, "st": 0, "bm": 0}], "markers": []}