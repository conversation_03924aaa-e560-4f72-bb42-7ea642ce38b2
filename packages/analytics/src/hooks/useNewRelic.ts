import NewRelic from 'newrelic-react-native-agent';
import { useCallback } from 'react';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { useAppStore } from '@scorescast/http-client';
import { request, PERMISSIONS } from 'react-native-permissions';

let appToken;

if (Platform.OS === 'ios') {
  appToken = 'eu01xx9ebfb3b38b6710df162918e5904113b2cef2-NRMA';
} else {
  appToken = 'eu01xxfd5da7b76dab964393283259a1c515205017-NRMA';
}

const agentConfiguration = {
  analyticsEventEnabled: true,
  crashReportingEnabled: true,
  interactionTracingEnabled: true,
  networkRequestEnabled: true,
  networkErrorRequestEnabled: true,
  httpResponseBodyCaptureEnabled: true,
  loggingEnabled: true,
  logLevel: NewRelic.LogLevel.INFO,
  webViewInstrumentation: true,
};

export const useNewRelic = () => {
  const { isTrackingEnabled, setIsTrackingEnabled } = useAppStore();

  const requestTrackingPermission = useCallback(() => {
    try {
      if (Platform.OS === 'android') {
        setIsTrackingEnabled(true);
        return true;
      } else {
        return request(PERMISSIONS.IOS.APP_TRACKING_TRANSPARENCY).then(
          (status: string) => {
            setIsTrackingEnabled(status === 'granted');
            return status === 'granted';
          }
        );
      }
    } catch (e) {
      if (__DEV__) {
        console.log('Request Tracking Permission Error', e);
      }
      return false;
    }
  }, [setIsTrackingEnabled]);

  const initializeNewRelic = useCallback(async () => {
    try {
      const status = await requestTrackingPermission();
      if (status) {
        const appVersion = DeviceInfo.getVersion();
        NewRelic.startAgent(appToken, agentConfiguration);
        NewRelic.setJSAppVersion(appVersion);
      }
    } catch (e) {
      if (__DEV__) {
        console.log('NewRelic Initialize Error', e);
      }
    }
  }, [requestTrackingPermission]);

  const trackEvent = useCallback(
    (eventType: string, eventName: string, attributes: Map<string, any>) => {
      try {
        if (isTrackingEnabled) {
          NewRelic.recordCustomEvent(eventType, eventName, attributes);
        }
      } catch (e) {
        if (__DEV__) {
          console.log('NewRelic Track Event Error', e);
        }
      }
    },
    [isTrackingEnabled]
  );

  const trackError = useCallback(
    (error: Error) => {
      try {
        if (isTrackingEnabled) {
          NewRelic.recordError(error);
        }
      } catch (e) {
        if (__DEV__) {
          console.log('NewRelic Track Error Error', e);
        }
      }
    },
    [isTrackingEnabled]
  );

  return {
    initializeNewRelic,
    trackEvent,
    trackError,
    requestTrackingPermission,
  };
};
