# @scorescast/http-client

A type-safe HTTP client wrapper around the Fetch API for the Scorescast mobile application.

## Features

- 🔒 **Type-safe**: Full TypeScript support with compile-time type checking
- 🎯 **Endpoint-based**: Organized endpoints by controller for better maintainability
- 🔧 **Flexible**: Support for path parameters, query parameters, and custom headers
- 📱 **React Native Ready**: Built-in device info and platform detection
- 🔐 **Authentication**: Automatic token and provider header management
- 📝 **Logging**: Comprehensive request/response logging for debugging

## Installation

```bash
yarn add @scorescast/http-client
```

## Basic Usage

```typescript
import { apiRequest } from '@scorescast/http-client';

// Simple GET request
const userDetails = await apiRequest({
  pathToEndpoint: 'userController.retrieveUserDetails',
});

// POST request with payload
const updatedUser = await apiRequest({
  pathToEndpoint: 'userController.updateUser',
  payload: {
    firstName: 'John',
    lastName: 'Doe',
  },
});

// GET request with query parameters
const teamFollowers = await apiRequest({
  pathToEndpoint: 'teamController.retrieveUsersFollowingTeam',
  queryParams: { teamId: 123 },
});

// Request with path parameters
const response = await apiRequest({
  pathToEndpoint: 'userController.followTeam',
  queryParams: { teamUniqueId: 'team-123' }, // Becomes /api/user/followTeam/team-123
});
```

## API Reference

### `apiRequest<T>(options: ApiRequestOptions<T>)`

The main function to make API requests.

#### Parameters

- `pathToEndpoint` (required): String path to the endpoint in format `"controller.action"`
- `headers` (optional): Additional headers to include in the request
- `payload` (optional): Request body for POST/PATCH/PUT requests
- `queryParams` (optional): Query parameters and path parameters
- `isLoggedIn` (optional): Whether to include authentication headers (default: true)

#### Available Endpoints

The `pathToEndpoint` follows the pattern `"controller.action"`:

**User Controller (`userController`)**
- `userController.updateUser`
- `userController.addVote`
- `userController.addReaction`
- `userController.followTeam`
- `userController.unfollowTeam`
- `userController.retrieveUserDetails`
- `userController.retrieveFollowedTeams`
- `userController.retrieveAgentTeams`
- `userController.retrieveAdminTeams`
- `userController.retrieveAdminClubs`
- `userController.deleteByEmail`

**Team Controller (`teamController`)**
- `teamController.retrieveUsersFollowingTeam`
- `teamController.retrieveTeamDetailsByUniqueId`

**Match Controller (`matchController`)**
- `matchController.claimMatch`
- `matchController.retrieveTeamMatches`
- `matchController.retrieveMatchDetails`

**Management Controllers**
- `manageUserController.*`
- `manageTeamController.*`
- `managePlayerController.*`
- `manageMatchController.*`
- `manageEventController.*`
- `manageClubController.*`

**App Config Controller (`appConfigController`)**
- `appConfigController.getUsersCached`
- `appConfigController.retrieveAppConfig`

## Advanced Usage

### Custom Headers

```typescript
const response = await apiRequest({
  pathToEndpoint: 'manageTeamController.create',
  payload: teamData,
  headers: {
    'Custom-Header': 'custom-value',
  },
});
```

### Path Parameters

Path parameters are automatically extracted from `queryParams`:

```typescript
// For endpoint: /api/user/deleteByEmail/{email}
const response = await apiRequest({
  pathToEndpoint: 'userController.deleteByEmail',
  queryParams: { email: '<EMAIL>' }, // Becomes /api/user/deleteByEmail/<EMAIL>
});
```

### Query Parameters

Remaining parameters after path parameter extraction become query parameters:

```typescript
// For endpoint: /api/match/retrieveTeamMatches
const response = await apiRequest({
  pathToEndpoint: 'matchController.retrieveTeamMatches',
  queryParams: { 
    uniqueId: 'team-123',
    statuses: ['ACTIVE', 'PENDING'] 
  }, // Becomes /api/match/retrieveTeamMatches?uniqueId=team-123&statuses=ACTIVE&statuses=PENDING
});
```

### Unauthenticated Requests

```typescript
const response = await apiRequest({
  pathToEndpoint: 'appConfigController.retrieveAppConfig',
  isLoggedIn: false, // Won't include auth headers
});
```

## Configuration

The client uses the following default configuration:

- **Base URL**: `https://scorescast.app:1402`
- **Default Headers**: Platform, User-Agent, Content-Type, Accept
- **Authentication**: Automatic Provider and Authorization headers from storage

## Error Handling

The client throws errors for:
- Network failures
- HTTP error status codes (4xx, 5xx)
- Invalid endpoint paths
- Missing required parameters

```typescript
try {
  const response = await apiRequest({
    pathToEndpoint: 'userController.retrieveUserDetails',
  });
} catch (error) {
  console.error('API request failed:', error);
}
```

## Logging

The client provides comprehensive logging for debugging:

- 🚀 Request details (method, URL, headers, payload)
- 📥 Response details (status, headers)
- ✅ Success responses with data
- ❌ Error responses with details
- 💥 Network failures and exceptions
