import type {
  UpdateUserDTO,
  AddVoteDTO,
  AddReactionDTO,
  AddTeamDTO,
  AddPlayerDTO,
  AddMatchDTO,
  SummaryDTO,
  AddEventDTO,
  AddClubDTO,
  ResponseDTO,
  ResponseDTOWithData,
} from '../types/api-types';

// Define endpoint structure with proper typing
interface EndpointConfig<TPayload = null, TResponse = any> {
  path: string;
  method: 'GET' | 'POST' | 'PATCH' | 'DELETE' | 'PUT';
  payloadType: TPayload extends null ? null : string;
  responseType: string;
}

export const apiEndpoints = {
  userController: {
    updateUser: {
      path: 'api/user/updateUser',
      method: 'POST',
      payloadType: 'UpdateUserDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<UpdateUserDTO, ResponseDTO>,
    addVote: {
      path: 'api/user/addVote',
      method: 'POST',
      payloadType: 'AddVoteDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<AddVoteDTO, ResponseDTO>,
    addReaction: {
      path: 'api/user/addReaction',
      method: 'POST',
      payloadType: 'AddReactionDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<AddReactionDTO, ResponseDTO>,
    unfollowTeam: {
      path: 'api/user/unfollowTeam/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
    followTeam: {
      path: 'api/user/followTeam/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
    retrieveUserDetails: {
      path: 'api/user/retrieveUserDetails',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
    retrieveFollowedTeams: {
      path: 'api/user/retrieveFollowedTeams',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
    retrieveAgentTeams: {
      path: 'api/user/retrieveAgentTeams',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
    retrieveAdminTeams: {
      path: 'api/user/retrieveAdminTeams',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
    retrieveAdminClubs: {
      path: 'api/user/retrieveAdminClubs',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
    deleteByEmail: {
      path: 'api/user/deleteByEmail/{email}',
      method: 'DELETE',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
  },
  manageUserController: {
    evictUserCache: {
      path: 'api/manage/user/evictUserCache',
      method: 'POST',
      payloadType: null,
      responseType: 'void',
    } satisfies EndpointConfig<null, void>,
    revokeTeamAdmin: {
      path: 'api/manage/user/revokeTeamAdmin/{userEmail}/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
    revokeAgent: {
      path: 'api/manage/user/revokeAgent/{userEmail}/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
    grantTeamAdmin: {
      path: 'api/manage/user/grantTeamAdmin/{userEmail}/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
    grantClubAdmin: {
      path: 'api/manage/user/grantClubAdmin/{userEmail}/{clubName}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
    grantAgent: {
      path: 'api/manage/user/grantAgent/{userEmail}/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
    retrieveFollowersOfTeam: {
      path: 'api/manage/user/retrieveFollowersOfTeam',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
    retrieveAgentsOfTeam: {
      path: 'api/manage/user/retrieveAgentsOfTeam',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
    retrieveAdminsOfTeam: {
      path: 'api/manage/user/retrieveAdminsOfTeam',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
  },
  manageTeamController: {
    create: {
      path: 'api/manage/team/create',
      method: 'POST',
      payloadType: 'AddTeamDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<AddTeamDTO, ResponseDTO>,
    update: {
      path: 'api/manage/team/update/{id}',
      method: 'PATCH',
      payloadType: 'AddTeamDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<AddTeamDTO, ResponseDTO>,
    deleteById: {
      path: 'api/manage/team/deleteById/{id}',
      method: 'DELETE',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
  },
  managePlayerController: {
    create: {
      path: 'api/manage/player/create',
      method: 'POST',
      payloadType: 'AddPlayerDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<AddPlayerDTO, ResponseDTO>,
    update: {
      path: 'api/manage/player/update/{id}',
      method: 'PATCH',
      payloadType: 'AddPlayerDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<AddPlayerDTO, ResponseDTO>,
    retrievePlayersForTeam: {
      path: 'api/manage/player/retrievePlayersForTeam',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
    deleteById: {
      path: 'api/manage/player/deleteById/{id}',
      method: 'DELETE',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
  },
  manageMatchController: {
    create: {
      path: 'api/manage/match/create',
      method: 'POST',
      payloadType: 'AddMatchDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<AddMatchDTO, ResponseDTO>,
    addSummary: {
      path: 'api/manage/match/addSummary',
      method: 'POST',
      payloadType: 'SummaryDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<SummaryDTO, ResponseDTO>,
    deleteById: {
      path: 'api/manage/match/deleteById/{id}',
      method: 'DELETE',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
  },
  manageEventController: {
    create: {
      path: 'api/manage/event/create',
      method: 'POST',
      payloadType: 'AddEventDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<AddEventDTO, ResponseDTO>,
  },
  manageClubController: {
    create: {
      path: 'api/manage/club/create',
      method: 'POST',
      payloadType: 'AddClubDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<AddClubDTO, ResponseDTO>,
    update: {
      path: 'api/manage/club/update/{id}',
      method: 'PATCH',
      payloadType: 'AddClubDTO',
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<AddClubDTO, ResponseDTO>,
    deleteById: {
      path: 'api/manage/club/deleteById/{id}',
      method: 'DELETE',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
  },
  matchController: {
    claimMatch: {
      path: 'api/match/claimMatch/{id}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    } satisfies EndpointConfig<null, ResponseDTO>,
    retrieveTeamMatches: {
      path: 'api/match/retrieveTeamMatches',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
    retrieveMatchDetails: {
      path: 'api/match/retrieveMatchDetails',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
  },
  teamController: {
    retrieveUsersFollowingTeam: {
      path: 'api/team/retrieveUsersFollowingTeam',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
    retrieveTeamDetailsByUniqueId: {
      path: 'api/team/retrieveTeamDetailsByUniqueId',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
  },
  appConfigController: {
    getUsersCached: {
      path: 'api/getUsersCached',
      method: 'POST',
      payloadType: null,
      responseType: 'void',
    } satisfies EndpointConfig<null, void>,
    retrieveAppConfig: {
      path: 'api/retrieveAppConfig',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    } satisfies EndpointConfig<null, ResponseDTOWithData>,
  },
};
