export const apiEndpoints = {
  user: {
    updateUser: {
      path: 'api/user/updateUser',
      method: 'POST',
      payloadType: 'UpdateUserDTO',
      responseType: 'ResponseDTO',
    },
    retrieveUserDetails: {
      path: 'api/user/retrieveUserDetails',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveFollowedTeams: {
      path: 'api/user/retrieveFollowedTeams',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveAgentTeams: {
      path: 'api/user/retrieveAgentTeams',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveAdminTeams: {
      path: 'api/user/retrieveAdminTeams',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveAdminClubs: {
      path: 'api/user/retrieveAdminClubs',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    followTeam: {
      path: 'api/user/followTeam',
      method: 'POST',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    unfollowTeam: {
      path: 'api/user/unfollowTeam',
      method: 'POST',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    addReaction: {
      path: 'api/user/addReaction',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    vote: {
      path: 'api/user/vote',
      method: 'POST',
      payloadType: 'AddVoteDTO',
      responseType: 'ResponseDTO',
    },
    deleteByEmail: {
      path: 'api/user/deleteByEmail/{email}',
      method: 'DELETE',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
  },
  team: {
    retrieveUsersFollowingTeam: {
      path: 'api/team/retrieveUsersFollowingTeam',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveTeamDetailsByUniqueId: {
      path: 'api/team/retrieveTeamDetailsByUniqueId',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
  },
  match: {
    retrieveTeamMatches: {
      path: 'api/match/retrieveTeamMatches',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveMatchDetails: {
      path: 'api/match/retrieveMatchDetails',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    claimMatch: {
      path: 'api/match/claimMatch',
      method: 'POST',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
  },
  manage: {
    user: {
      revokeAgent: {
        path: 'api/manage/user/revokeAgent/{userEmail}/{teamUniqueId}',
        method: 'PATCH',
        payloadType: null,
        responseType: 'ResponseDTO',
      },
      grantTeamAdmin: {
        path: 'api/manage/user/grantTeamAdmin/{userEmail}/{teamUniqueId}',
        method: 'PATCH',
        payloadType: null,
        responseType: 'ResponseDTO',
      },
      grantClubAdmin: {
        path: 'api/manage/user/grantClubAdmin/{userEmail}/{clubName}',
        method: 'PATCH',
        payloadType: null,
        responseType: 'ResponseDTO',
      },
      grantAgent: {
        path: 'api/manage/user/grantAgent/{userEmail}/{teamUniqueId}',
        method: 'PATCH',
        payloadType: null,
        responseType: 'ResponseDTO',
      },
      retrieveFollowersOfTeam: {
        path: 'api/manage/user/retrieveFollowersOfTeam',
        method: 'GET',
        payloadType: null,
        responseType: 'ResponseDTOWithData',
      },
      retrieveAgentsOfTeam: {
        path: 'api/manage/user/retrieveAgentsOfTeam',
        method: 'GET',
        payloadType: null,
        responseType: 'ResponseDTOWithData',
      },
      retrieveAdminsOfTeam: {
        path: 'api/manage/user/retrieveAdminsOfTeam',
        method: 'GET',
        payloadType: null,
        responseType: 'ResponseDTOWithData',
      },
    },
    team: {
      update: {
        path: 'api/manage/team/update/{id}',
        method: 'PATCH',
        payloadType: 'AddTeamDTO',
        responseType: 'ResponseDTO',
      },
      create: {
        path: 'api/manage/team/create',
        method: 'POST',
        payloadType: 'AddTeamDTO',
        responseType: 'ResponseDTO',
      },
      deleteById: {
        path: 'api/manage/team/deleteById/{id}',
        method: 'DELETE',
        payloadType: null,
        responseType: 'ResponseDTO',
      },
    },
    player: {
      update: {
        path: 'api/manage/player/update/{id}',
        method: 'PATCH',
        payloadType: 'AddPlayerDTO',
        responseType: 'ResponseDTO',
      },
      create: {
        path: 'api/manage/player/create',
        method: 'POST',
        payloadType: 'AddPlayerDTO',
        responseType: 'ResponseDTO',
      },
      retrievePlayersForTeam: {
        path: 'api/manage/player/retrievePlayersForTeam',
        method: 'GET',
        payloadType: null,
        responseType: 'ResponseDTO',
      },
      deleteById: {
        path: 'api/manage/player/deleteById/{id}',
        method: 'DELETE',
        payloadType: null,
        responseType: 'ResponseDTO',
      },
    },
    club: {
      update: {
        path: 'api/manage/club/update/{id}',
        method: 'PATCH',
        payloadType: 'AddClubDTO',
        responseType: 'ResponseDTO',
      },
      create: {
        path: 'api/manage/club/create',
        method: 'POST',
        payloadType: 'AddClubDTO',
        responseType: 'ResponseDTO',
      },
      deleteById: {
        path: 'api/manage/club/deleteById/{id}',
        method: 'DELETE',
        payloadType: null,
        responseType: 'ResponseDTO',
      },
    },
    match: {
      create: {
        path: 'api/manage/match/create',
        method: 'POST',
        payloadType: 'AddMatchDTO',
        responseType: 'ResponseDTO',
      },
      update: {
        path: 'api/manage/match/update/{id}',
        method: 'PATCH',
        payloadType: 'AddMatchDTO',
        responseType: 'ResponseDTO',
      },
      deleteById: {
        path: 'api/manage/match/deleteById/{id}',
        method: 'DELETE',
        payloadType: null,
        responseType: 'ResponseDTO',
      },
    },
    event: {
      create: {
        path: 'api/manage/event/create',
        method: 'POST',
        payloadType: 'AddEventDTO',
        responseType: 'ResponseDTO',
      },
    },
    summary: {
      create: {
        path: 'api/manage/summary/create',
        method: 'POST',
        payloadType: 'SummaryDTO',
        responseType: 'ResponseDTO',
      },
    },
  },
  app: {
    retrieveAppConfig: {
      path: 'api/retrieveAppConfig',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
  },
};
