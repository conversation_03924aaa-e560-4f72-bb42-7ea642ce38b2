export const apiEndpoints = {
  userController: {
    updateUser: {
      path: 'api/user/updateUser',
      method: 'POST',
      payloadType: 'UpdateUserDTO',
      responseType: 'ResponseDTO',
    },
    addVote: {
      path: 'api/user/addVote',
      method: 'POST',
      payloadType: 'AddVoteDTO',
      responseType: 'ResponseDTO',
    },
    addReaction: {
      path: 'api/user/addReaction',
      method: 'POST',
      payloadType: 'AddReactionDTO',
      responseType: 'ResponseDTO',
    },
    unfollowTeam: {
      path: 'api/user/unfollowTeam/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    followTeam: {
      path: 'api/user/followTeam/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    retrieveUserDetails: {
      path: 'api/user/retrieveUserDetails',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveFollowedTeams: {
      path: 'api/user/retrieveFollowedTeams',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveAgentTeams: {
      path: 'api/user/retrieveAgentTeams',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveAdminTeams: {
      path: 'api/user/retrieveAdminTeams',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveAdminClubs: {
      path: 'api/user/retrieveAdminClubs',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    deleteByEmail: {
      path: 'api/user/deleteByEmail/{email}',
      method: 'DELETE',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
  },
  manageUserController: {
    evictUserCache: {
      path: 'api/manage/user/evictUserCache',
      method: 'POST',
      payloadType: null,
      responseType: null,
    },
    revokeTeamAdmin: {
      path: 'api/manage/user/revokeTeamAdmin/{userEmail}/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    revokeAgent: {
      path: 'api/manage/user/revokeAgent/{userEmail}/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    grantTeamAdmin: {
      path: 'api/manage/user/grantTeamAdmin/{userEmail}/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    grantClubAdmin: {
      path: 'api/manage/user/grantClubAdmin/{userEmail}/{clubName}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    grantAgent: {
      path: 'api/manage/user/grantAgent/{userEmail}/{teamUniqueId}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    retrieveFollowersOfTeam: {
      path: 'api/manage/user/retrieveFollowersOfTeam',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveAgentsOfTeam: {
      path: 'api/manage/user/retrieveAgentsOfTeam',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveAdminsOfTeam: {
      path: 'api/manage/user/retrieveAdminsOfTeam',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
  },
  manageTeamController: {
    create: {
      path: 'api/manage/team/create',
      method: 'POST',
      payloadType: 'AddTeamDTO',
      responseType: 'ResponseDTO',
    },
    update: {
      path: 'api/manage/team/update/{id}',
      method: 'PATCH',
      payloadType: 'AddTeamDTO',
      responseType: 'ResponseDTO',
    },
    deleteById: {
      path: 'api/manage/team/deleteById/{id}',
      method: 'DELETE',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
  },
  managePlayerController: {
    create: {
      path: 'api/manage/player/create',
      method: 'POST',
      payloadType: 'AddPlayerDTO',
      responseType: 'ResponseDTO',
    },
    update: {
      path: 'api/manage/player/update/{id}',
      method: 'PATCH',
      payloadType: 'AddPlayerDTO',
      responseType: 'ResponseDTO',
    },
    retrievePlayersForTeam: {
      path: 'api/manage/player/retrievePlayersForTeam',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    deleteById: {
      path: 'api/manage/player/deleteById/{id}',
      method: 'DELETE',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
  },
  manageMatchController: {
    create: {
      path: 'api/manage/match/create',
      method: 'POST',
      payloadType: 'AddMatchDTO',
      responseType: 'ResponseDTO',
    },
    addSummary: {
      path: 'api/manage/match/addSummary',
      method: 'POST',
      payloadType: 'SummaryDTO',
      responseType: 'ResponseDTO',
    },
    deleteById: {
      path: 'api/manage/match/deleteById/{id}',
      method: 'DELETE',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
  },
  manageEventController: {
    create: {
      path: 'api/manage/event/create',
      method: 'POST',
      payloadType: 'AddEventDTO',
      responseType: 'ResponseDTO',
    },
  },
  manageClubController: {
    create: {
      path: 'api/manage/club/create',
      method: 'POST',
      payloadType: 'AddClubDTO',
      responseType: 'ResponseDTO',
    },
    update: {
      path: 'api/manage/club/update/{id}',
      method: 'PATCH',
      payloadType: 'AddClubDTO',
      responseType: 'ResponseDTO',
    },
    deleteById: {
      path: 'api/manage/club/deleteById/{id}',
      method: 'DELETE',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
  },
  matchController: {
    claimMatch: {
      path: 'api/match/claimMatch/{id}',
      method: 'PATCH',
      payloadType: null,
      responseType: 'ResponseDTO',
    },
    retrieveTeamMatches: {
      path: 'api/match/retrieveTeamMatches',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveMatchDetails: {
      path: 'api/match/retrieveMatchDetails',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
  },
  teamController: {
    retrieveUsersFollowingTeam: {
      path: 'api/team/retrieveUsersFollowingTeam',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
    retrieveTeamDetailsByUniqueId: {
      path: 'api/team/retrieveTeamDetailsByUniqueId',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
  },
  appConfigController: {
    getUsersCached: {
      path: 'api/getUsersCached',
      method: 'POST',
      payloadType: null,
      responseType: null,
    },
    retrieveAppConfig: {
      path: 'api/retrieveAppConfig',
      method: 'GET',
      payloadType: null,
      responseType: 'ResponseDTOWithData',
    },
  },
};
