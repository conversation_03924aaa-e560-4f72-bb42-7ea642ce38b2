export const apiEndpoints = {
  user: {
    updateUser: {
      path: 'api/user/updateUser',
      method: 'POST',
    },
    retrieveUserDetails: {
      path: 'api/user/retrieveUserDetails',
      method: 'GET',
    },
    retrieveFollowedTeams: {
      path: 'api/user/retrieveFollowedTeams',
      method: 'GET',
    },
    retrieveAgentTeams: {
      path: 'api/user/retrieveAgentTeams',
      method: 'GET',
    },
    retrieveAdminTeams: {
      path: 'api/user/retrieveAdminTeams',
      method: 'GET',
    },
    retrieveAdminClubs: {
      path: 'api/user/retrieveAdminClubs',
      method: 'GET',
    },
    followTeam: {
      path: 'api/user/followTeam',
      method: 'POST',
    },
    unfollowTeam: {
      path: 'api/user/unfollowTeam',
      method: 'POST',
    },
    addReaction: {
      path: 'api/user/addReaction',
      method: 'GET',
    },
    deleteByEmail: {
      path: 'api/user/deleteByEmail/{email}',
      method: 'DELETE',
    },
  },
  team: {
    retrieveUsersFollowingTeam: {
      path: 'api/team/retrieveUsersFollowingTeam',
      method: 'GET',
    },
    retrieveTeamDetailsByUniqueId: {
      path: 'api/team/retrieveTeamDetailsByUniqueId',
      method: 'GET',
    },
  },
  match: {
    retrieveTeamMatches: {
      path: 'api/match/retrieveTeamMatches',
      method: 'GET',
    },
    retrieveMatchDetails: {
      path: 'api/match/retrieveMatchDetails',
      method: 'GET',
    },
    claimMatch: {
      path: 'api/match/claimMatch',
      method: 'POST',
    },
  },
  manage: {
    user: {
      revokeAgent: {
        path: 'api/manage/user/revokeAgent/{userEmail}/{teamUniqueId}',
        method: 'PATCH',
      },
      grantTeamAdmin: {
        path: 'api/manage/user/grantTeamAdmin/{userEmail}/{teamUniqueId}',
        method: 'PATCH',
      },
      grantClubAdmin: {
        path: 'api/manage/user/grantClubAdmin/{userEmail}/{clubName}',
        method: 'PATCH',
      },
      grantAgent: {
        path: 'api/manage/user/grantAgent/{userEmail}/{teamUniqueId}',
        method: 'PATCH',
      },
      retrieveFollowersOfTeam: {
        path: 'api/manage/user/retrieveFollowersOfTeam',
        method: 'GET',
      },
      retrieveAgentsOfTeam: {
        path: 'api/manage/user/retrieveAgentsOfTeam',
        method: 'GET',
      },
      retrieveAdminsOfTeam: {
        path: 'api/manage/user/retrieveAdminsOfTeam',
        method: 'GET',
      },
    },
    team: {
      update: {
        path: 'api/manage/team/update/{id}',
        method: 'PATCH',
      },
      create: {
        path: 'api/manage/team/create',
        method: 'POST',
      },
      deleteById: {
        path: 'api/manage/team/deleteById/{id}',
        method: 'DELETE',
      },
    },
    player: {
      update: {
        path: 'api/manage/player/update/{id}',
        method: 'PATCH',
      },
      create: {
        path: 'api/manage/player/create',
        method: 'POST',
      },
      retrievePlayersForTeam: {
        path: 'api/manage/player/retrievePlayersForTeam',
        method: 'GET',
      },
      deleteById: {
        path: 'api/manage/player/deleteById/{id}',
        method: 'DELETE',
      },
    },
    club: {
      update: {
        path: 'api/manage/club/update/{id}',
        method: 'PATCH',
      },
      create: {
        path: 'api/manage/club/create',
        method: 'POST',
      },
      deleteById: {
        path: 'api/manage/club/deleteById/{id}',
        method: 'DELETE',
      },
    },
    match: {
      create: {
        path: 'api/manage/match/create',
        method: 'POST',
      },
      update: {
        path: 'api/manage/match/update/{id}',
        method: 'PATCH',
      },
      deleteById: {
        path: 'api/manage/match/deleteById/{id}',
        method: 'DELETE',
      },
    },
    event: {
      create: {
        path: 'api/manage/event/create',
        method: 'POST',
      },
    },
  },
  app: {
    retrieveAppConfig: {
      path: 'api/retrieveAppConfig',
      method: 'GET',
    },
  },
};
