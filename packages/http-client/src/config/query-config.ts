import { QueryClient } from '@tanstack/react-query';

// Base configuration for TanStack Query
export const queryConfig = {
  queries: {
    // Data never goes stale by default
    staleTime: Infinity,
    // Cache data for 24 hours
    gcTime: 1000 * 60 * 60 * 24,
    // Don't refetch on window focus by default (we'll handle this manually)
    refetchOnWindowFocus: false,
    // Don't refetch on reconnect by default
    refetchOnReconnect: false,
    // Don't refetch on mount by default
    refetchOnMount: false,
    // Retry failed requests 3 times
    retry: 3,
    // Retry delay function (exponential backoff)
    retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
  },
  mutations: {
    // Retry failed mutations once
    retry: 1,
    // Retry delay for mutations
    retryDelay: 1000,
  },
};

// Create the query client with our base configuration
export const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: queryConfig,
  });
};

// Default query client instance
export const queryClient = createQueryClient();
