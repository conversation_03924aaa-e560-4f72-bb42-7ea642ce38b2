import { isQAEnvironment } from '../storage/storage';

interface EnvironmentConfig {
  protocol: string;
  domain: string;
  port?: number;
}

interface ConfigType {
  production: EnvironmentConfig;
  qa: EnvironmentConfig;
  getCurrentEnvironment: () => EnvironmentConfig;
  getBaseUrl: () => string;
  getFullUrl: (endpoint: string, queryParams?: string) => string;
}

const Config: ConfigType = {
  production: {
    protocol: 'https',
    domain: 'scorescast.app',
    // No port for production
  },
  qa: {
    protocol: 'https',
    domain: 'scorescast.app',
    port: 1402,
  },
  getCurrentEnvironment() {
    return isQAEnvironment() ? this.qa : this.production;
  },
  getBaseUrl() {
    const env = this.getCurrentEnvironment();
    return `${env.protocol}://${env.domain}${env.port ? `:${env.port}` : ''}`;
  },
  getFullUrl(endpoint: string, queryParams?: string) {
    const baseUrl = this.getBaseUrl();
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${baseUrl}${cleanEndpoint}${queryParams ? queryParams : ''}`;
  },
};

export default Config;
