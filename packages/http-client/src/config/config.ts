interface ConfigType {
  protocol: string;
  domain: string;
  port?: number;
  getBaseUrl: () => string;
  getFullUrl: (endpoint: string, queryParams?: string) => string;
}

const Config: ConfigType = {
  protocol: 'https',
  domain: 'scorescast.app',
  port: 1402,
  getBaseUrl() {
    return `${this.protocol}://${this.domain}${this.port ? `:${this.port}` : ''}`;
  },
  getFullUrl(endpoint: string, queryParams?: string) {
    const baseUrl = this.getBaseUrl();
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${baseUrl}${cleanEndpoint}${queryParams ? queryParams : ''}`;
  },
};

export default Config;
