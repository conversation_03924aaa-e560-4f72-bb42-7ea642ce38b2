import { create } from 'zustand';
import { addEventListener, fetch } from '@react-native-community/netinfo';
import { Config } from '@scorescast/formatters-constants';

interface AppStore {
  isConnected: boolean | null;
  setIsConnected: (status: boolean) => void;
  config: typeof Config | null;
  setConfig: (config: typeof Config) => void;
  isTrackingEnabled: boolean;
  setIsTrackingEnabled: (status: boolean) => void;
}

export const useAppStore = create<AppStore>((set) => ({
  isConnected: null,
  setIsConnected: (status) => set({ isConnected: status }),
  config: null,
  setConfig: (config) => set({ config }),
  isTrackingEnabled: false,
  setIsTrackingEnabled: (status) => set({ isTrackingEnabled: status }),
}));

export const startNetworkListener = () => {
  addEventListener((state) => {
    useAppStore.getState().setIsConnected(state.isConnected!);
  });
};

export const refreshNetworkConnectivity = () => {
  fetch().then((state) => {
    useAppStore.getState().setIsConnected(state.isConnected!);
  });
};
