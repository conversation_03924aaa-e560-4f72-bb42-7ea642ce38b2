import { create } from 'zustand';
import { Toaster } from '@scorescast/formatters-constants';

interface ToasterStore {
  toaster: typeof Toaster | null;
  addToaster: (toaster: typeof Toaster) => void;
  removeToaster: () => void;
}

export const useToasterStore = create<ToasterStore>((set, get) => ({
  toaster: null,
  addToaster: (toaster) => {
    const currentToaster = get().toaster;
    if (JSON.stringify(currentToaster) !== JSON.stringify(toaster)) {
      set({ toaster });
    }
  },
  removeToaster: () => {
    setTimeout(() => {
      set({ toaster: null });
    }, 0);
  },
}));
