import { create } from 'zustand';
import { MatchPossibleTeams } from '@scorescast/formatters-constants';

interface AgentStore {
  sideSelection: string;
  usTeamImage: string;
  currentMatchLocation: any | null;
  setCurrentMatchLocation: (currentMatchLocation: any) => void;
  setUsTeamImage: (usTeamImage: string) => void;
  setSideSelection: (sideSelection: string) => void;
}

export const useAgentStore = create<AgentStore>((set) => ({
  sideSelection: MatchPossibleTeams[0],
  usTeamImage: '',
  currentMatchLocation: null,
  setCurrentMatchLocation: (currentMatchLocation) =>
    set({ currentMatchLocation }),
  setUsTeamImage: (usTeamImage) => set({ usTeamImage }),
  setSideSelection: (sideSelection) => set({ sideSelection }),
}));
