import { create } from 'zustand';
import { storage, STORAGE_KEYS } from '../storage/storage';

interface NotificationsStore {
  notifications: boolean | null;
  setNotifications: (status: boolean) => void;
  initializeNotificationsStore: () => void;
}

export const useNotificationsStore = create<NotificationsStore>((set) => ({
  notifications: null,
  setNotifications: (status) => {
    storage.set(STORAGE_KEYS.NOTIFICATIONS, status);
    set({ notifications: status });
  },
  initializeNotificationsStore: () => {
    const notifications = storage.getBoolean(STORAGE_KEYS.NOTIFICATIONS);
    set({ notifications });
  },
}));
