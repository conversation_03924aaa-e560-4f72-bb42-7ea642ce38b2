import { 
  API_ENVIRONMENT, 
  setApiEnvironment, 
  getApiEnvironment, 
  isQAEnvironment, 
  isProductionEnvironment 
} from '../storage/storage';
import Config from '../config/config';

/**
 * Environment switcher utility for development and testing
 */
export class EnvironmentSwitcher {
  /**
   * Switch to QA environment
   */
  static switchToQA(): void {
    setApiEnvironment(API_ENVIRONMENT.QA);
    console.log('🔧 [Environment] Switched to QA environment');
    console.log('📍 [Environment] Base URL:', Config.getBaseUrl());
  }

  /**
   * Switch to Production environment
   */
  static switchToProduction(): void {
    setApiEnvironment(API_ENVIRONMENT.PRODUCTION);
    console.log('🔧 [Environment] Switched to Production environment');
    console.log('📍 [Environment] Base URL:', Config.getBaseUrl());
  }

  /**
   * Get current environment info
   */
  static getCurrentEnvironmentInfo(): {
    environment: API_ENVIRONMENT;
    baseUrl: string;
    config: ReturnType<typeof Config.getCurrentEnvironment>;
  } {
    const environment = getApiEnvironment();
    const config = Config.getCurrentEnvironment();
    const baseUrl = Config.getBaseUrl();

    return {
      environment,
      baseUrl,
      config,
    };
  }

  /**
   * Log current environment status
   */
  static logCurrentEnvironment(): void {
    const info = this.getCurrentEnvironmentInfo();
    console.log('🌍 [Environment] Current Status:', {
      environment: info.environment,
      baseUrl: info.baseUrl,
      isQA: isQAEnvironment(),
      isProduction: isProductionEnvironment(),
      config: info.config,
    });
  }

  /**
   * Toggle between QA and Production
   */
  static toggle(): void {
    if (isQAEnvironment()) {
      this.switchToProduction();
    } else {
      this.switchToQA();
    }
  }
}

// Global functions for easy access in development
if (__DEV__) {
  // Make environment switcher available globally for debugging
  (global as any).switchToQA = () => EnvironmentSwitcher.switchToQA();
  (global as any).switchToProduction = () => EnvironmentSwitcher.switchToProduction();
  (global as any).toggleEnvironment = () => EnvironmentSwitcher.toggle();
  (global as any).logEnvironment = () => EnvironmentSwitcher.logCurrentEnvironment();
  
  console.log('🔧 [Environment] Development utilities available:');
  console.log('   - switchToQA()');
  console.log('   - switchToProduction()');
  console.log('   - toggleEnvironment()');
  console.log('   - logEnvironment()');
}
