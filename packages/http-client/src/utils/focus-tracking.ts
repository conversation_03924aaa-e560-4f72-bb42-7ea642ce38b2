import { useEffect, useRef, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';

// App online/offline tracking
export const useAppOnlineTrack = () => {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    // In React Native, we assume online by default
    // You can integrate with NetInfo for actual network status
    // import NetInfo from '@react-native-community/netinfo';
    
    // For now, we'll use a simple implementation
    // const unsubscribe = NetInfo.addEventListener(state => {
    //   setIsOnline(state.isConnected ?? false);
    // });
    
    // return unsubscribe;
    
    // Placeholder implementation - always online
    setIsOnline(true);
  }, []);

  return isOnline;
};

// App focus/background tracking
export const useAppFocusTrack = () => {
  const [isAppFocused, setIsAppFocused] = useState(AppState.currentState === 'active');

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      setIsAppFocused(nextAppState === 'active');
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, []);

  return isAppFocused;
};

// Screen focus tracking using React Navigation
export const useScreenFocusTrack = () => {
  const [isScreenFocused, setIsScreenFocused] = useState(false);

  useFocusEffect(() => {
    setIsScreenFocused(true);
    
    return () => {
      setIsScreenFocused(false);
    };
  });

  return isScreenFocused;
};

// Screen unfocus tracking (opposite of focus)
export const useScreenUnfocusTrack = () => {
  const isScreenFocused = useScreenFocusTrack();
  return !isScreenFocused;
};

// Combined focus tracking - returns true only when both app and screen are focused
export const useCombinedFocusTrack = () => {
  const isAppFocused = useAppFocusTrack();
  const isScreenFocused = useScreenFocusTrack();
  const isOnline = useAppOnlineTrack();

  return {
    isAppFocused,
    isScreenFocused,
    isOnline,
    isFocused: isAppFocused && isScreenFocused,
    canRefetch: isAppFocused && isScreenFocused && isOnline,
  };
};

// Hook to track when focus changes and execute callback
export const useFocusChangeEffect = (
  onFocus?: () => void,
  onUnfocus?: () => void,
  deps: React.DependencyList = []
) => {
  const { isFocused } = useCombinedFocusTrack();
  const previousFocused = useRef(isFocused);

  useEffect(() => {
    if (isFocused && !previousFocused.current) {
      // Just gained focus
      onFocus?.();
    } else if (!isFocused && previousFocused.current) {
      // Just lost focus
      onUnfocus?.();
    }
    
    previousFocused.current = isFocused;
  }, [isFocused, onFocus, onUnfocus, ...deps]);

  return isFocused;
};
