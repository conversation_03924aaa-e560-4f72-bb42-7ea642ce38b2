import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { storage, STORAGE_KEYS } from '../storage/storage';

export const getAPIHeaders = async (
  isLoggedIn: boolean = true,
  customHeaders: Record<string, string> = {}
): Promise<Record<string, string>> => {
  const userAgent = await DeviceInfo.getUserAgent();

  const headers: Record<string, string> = {
    Platform: Platform.OS,
    'User-Agent': userAgent,
    'Content-Type': 'application/json',
    Accept: 'application/json',
    ...customHeaders,
  };

  if (isLoggedIn) {
    const storagedLoginType = storage.getString(STORAGE_KEYS.LOGIN_TYPE) ?? '';
    const storagedLoginToken = storage.getString(STORAGE_KEYS.TOKEN) ?? '';

    headers['Provider'] = storagedLoginType;
    headers['Authorization'] = storagedLoginToken;
  }
  return headers;
};
