// Request DTOs (Payload Types)
export interface UpdateUserDTO {
  firstName: string;
  lastName: string;
}

export interface AddVoteDTO {
  matchId: number;
  playerId: number;
  latitude: number;
  longitude: number;
}

export interface AddReactionDTO {
  eventId: number;
  reaction: string;
}

export interface AddTeamDTO {
  name: string;
  image: string;
  clubName: string;
  ageGroup: string;
}

export interface AddPlayerDTO {
  jerseyName: string;
  number: string;
  teamId: number;
}

export interface AddMatchDTO {
  usTeamId: number;
  themTeamName: string;
  location: string;
  matchType: string;
  matchSetup: string;
  description: string;
  matchTime: number;
}

export interface SummaryDTO {
  matchId: number;
  summary: string;
}

export interface AddEventDTO {
  playerId: number;
  matchId: number;
  who: string;
  action: string;
  outcome: string;
  period: string;
  timestamp: number;
  latitude: number;
  longitude: number;
}

export interface AddClubDTO {
  name: string;
  crest: string;
  website: string;
  adminEmail: string;
}

// Response DTOs
export interface ResponseDTO {
  status: 'SUCCESS' | 'FAILURE';
}

export interface ResponseDTOWithData {
  status: 'SUCCESS' | 'FAILURE';
  data: any;
}
