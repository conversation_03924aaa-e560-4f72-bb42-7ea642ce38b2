import { i18next } from '@scorescast/translations';

// ImgBB API configuration
const IMGBB_API_KEY = 'your-imgbb-api-key'; // This should come from environment variables
const IMGBB_UPLOAD_URL = 'https://api.imgbb.com/1/upload';

interface ImgbbResponse {
  data: {
    id: string;
    title: string;
    url_viewer: string;
    url: string;
    display_url: string;
    width: number;
    height: number;
    size: number;
    time: number;
    expiration: number;
    image: {
      filename: string;
      name: string;
      mime: string;
      extension: string;
      url: string;
    };
    thumb: {
      filename: string;
      name: string;
      mime: string;
      extension: string;
      url: string;
    };
    medium: {
      filename: string;
      name: string;
      mime: string;
      extension: string;
      url: string;
    };
    delete_url: string;
  };
  success: boolean;
  status: number;
}

export const ImgbbApi = {
  uploadImage: async (base64Image: string): Promise<ImgbbResponse> => {
    try {
      const formData = new FormData();
      formData.append('key', IMGBB_API_KEY);
      formData.append('image', base64Image);
      formData.append('expiration', '15552000'); // 6 months in seconds

      const response = await fetch(IMGBB_UPLOAD_URL, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error?.message || 'Upload failed');
      }

      return result;
    } catch (error) {
      console.error('ImgBB upload error:', error);
      throw new Error(i18next.t('API_ERRORS.IMAGE_UPLOAD_FAILED') || 'Image upload failed');
    }
  },
};
