import * as React from 'react';
import {
  useQuery,
  useMutation,
  UseQueryOptions,
  UseMutationOptions,
  QueryKey,
} from '@tanstack/react-query';
import { apiRequest } from '../core/api-client';
import { useCombinedFocusTrack } from '../utils/focus-tracking';
import { apiEndpoints } from '../config/endpoints';

// Type to extract nested endpoint paths
type EndpointPath = {
  [K in keyof typeof apiEndpoints]: {
    [P in keyof (typeof apiEndpoints)[K]]: `${K & string}.${P & string}`;
  }[keyof (typeof apiEndpoints)[K]];
}[keyof typeof apiEndpoints];

// Base query options with focus tracking and callbacks
interface BaseQueryOptions<TData = unknown, TError = Error> {
  pathToEndpoint: EndpointPath;
  queryParams?: Record<string, any>; // Allow any type for more flexibility
  headers?: Record<string, string>;
  isLoggedIn?: boolean;
  enabled?: boolean;
  refetchOnFocus?: boolean;
  onSuccess?: (data: TData) => void;
  onError?: (error: TError) => void;
  onSettled?: (data: TData | undefined, error: TError | null) => void;
  queryOptions?: Omit<UseQueryOptions<TData, TError>, 'queryKey' | 'queryFn'>;
}

// Base mutation options with callbacks
interface BaseMutationOptions<
  TData = unknown,
  TError = Error,
  TVariables = unknown,
> {
  pathToEndpoint: EndpointPath;
  headers?: Record<string, string>;
  isLoggedIn?: boolean;
  onSuccess?: (data: TData, variables: TVariables) => void;
  onError?: (error: TError, variables: TVariables) => void;
  onSettled?: (
    data: TData | undefined,
    error: TError | null,
    variables: TVariables
  ) => void;
  mutationOptions?: Omit<
    UseMutationOptions<TData, TError, TVariables>,
    'mutationFn'
  >;
}

// Base query hook with focus tracking
export function useBaseQuery<TData = unknown, TError = Error>(
  queryKey: QueryKey,
  options: BaseQueryOptions<TData, TError>
) {
  const { canRefetch } = useCombinedFocusTrack();

  const {
    pathToEndpoint,
    queryParams,
    headers,
    isLoggedIn = true,
    enabled = true,
    refetchOnFocus = false,
    onSuccess,
    onError,
    onSettled,
    queryOptions = {},
  } = options;

  const result = useQuery<TData, TError>({
    queryKey,
    queryFn: async () => {
      const response = await apiRequest({
        pathToEndpoint,
        queryParams,
        headers,
        isLoggedIn,
      });

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(response);
      }

      return response;
    },
    enabled: enabled && (refetchOnFocus ? canRefetch : true),
    ...queryOptions,
  });

  // Handle callbacks using useEffect-like pattern for TanStack Query v5
  React.useEffect(() => {
    if (result.isError && onError) {
      onError(result.error);
    }
  }, [result.isError, result.error, onError]);

  React.useEffect(() => {
    if (onSettled) {
      onSettled(result.data, result.error);
    }
  }, [result.data, result.error, onSettled]);

  return result;
}

// Base mutation hook with callbacks
export function useBaseMutation<
  TData = unknown,
  TError = Error,
  TVariables = unknown,
>(options: BaseMutationOptions<TData, TError, TVariables>) {
  const {
    pathToEndpoint,
    headers,
    isLoggedIn = true,
    onSuccess,
    onError,
    onSettled,
    mutationOptions = {},
  } = options;

  return useMutation<TData, TError, TVariables>({
    mutationFn: async (variables: TVariables) => {
      // Extract payload and queryParams from variables if they exist
      const payload = (variables as any)?.payload;
      const queryParams = (variables as any)?.queryParams;
      const customHeaders = (variables as any)?.headers || headers;

      const response = await apiRequest({
        pathToEndpoint,
        payload,
        queryParams,
        headers: customHeaders,
        isLoggedIn,
      });

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(response, variables);
      }

      return response;
    },
    ...mutationOptions,
    // Override callbacks to include our custom ones
    onSuccess: (data, variables, context) => {
      onSuccess?.(data, variables);
      mutationOptions.onSuccess?.(data, variables, context);
    },
    onError: (error, variables, context) => {
      onError?.(error, variables);
      mutationOptions.onError?.(error, variables, context);
    },
    onSettled: (data, error, variables, context) => {
      onSettled?.(data, error, variables);
      mutationOptions.onSettled?.(data, error, variables, context);
    },
  });
}

// Utility to create query keys consistently
export const createQueryKey = (
  controller: string,
  action: string,
  params?: Record<string, any>
) => {
  const baseKey = [controller, action];
  return params ? [...baseKey, params] : baseKey;
};
