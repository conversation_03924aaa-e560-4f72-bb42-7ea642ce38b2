import { useBaseMutation } from './base-hooks';
import type { AddClubDTO } from '../types/api-types';

// Mutation hooks for manage club controller
export const useCreateClub = (options?: {
  onSuccess?: (data: any, variables: { payload: AddClubDTO }) => void;
  onError?: (error: Error, variables: { payload: AddClubDTO }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: AddClubDTO }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: AddClubDTO }>({
    pathToEndpoint: 'manageClubController.create',
    ...options,
  });
};

export const useUpdateClub = (options?: {
  onSuccess?: (data: any, variables: { payload: AddClubDTO; queryParams: { id: number } }) => void;
  onError?: (error: Error, variables: { payload: AddClubDTO; queryParams: { id: number } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: AddClubDTO; queryParams: { id: number } }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: AddClubDTO; queryParams: { id: number } }>({
    pathToEndpoint: 'manageClubController.update',
    ...options,
  });
};

export const useDeleteClubById = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { id: number } }) => void;
  onError?: (error: Error, variables: { queryParams: { id: number } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { id: number } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { id: number } }>({
    pathToEndpoint: 'manageClubController.deleteById',
    ...options,
  });
};
