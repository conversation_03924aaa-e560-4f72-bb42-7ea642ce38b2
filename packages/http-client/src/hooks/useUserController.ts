import { useBaseQuery, useBaseMutation, createQ<PERSON>y<PERSON><PERSON> } from './base-hooks';
import type { UpdateUserDTO, AddVoteDTO, AddReactionDTO } from '../types/api-types';

// Query hooks for user controller
export const useRetrieveUserDetails = (options?: {
  enabled?: boolean;
  refetchOnFocus?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  onSettled?: (data: any | undefined, error: Error | null) => void;
}) => {
  return useBaseQuery(
    createQueryKey('userController', 'retrieveUserDetails'),
    {
      pathToEndpoint: 'userController.retrieveUserDetails',
      ...options,
    }
  );
};

export const useRetrieveFollowedTeams = (options?: {
  enabled?: boolean;
  refetchOnFocus?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  onSettled?: (data: any | undefined, error: Error | null) => void;
}) => {
  return useBaseQuery(
    createQuery<PERSON>ey('userController', 'retrieveFollowedTeams'),
    {
      pathToEndpoint: 'userController.retrieveFollowedTeams',
      ...options,
    }
  );
};

export const useRetrieveAgentTeams = (options?: {
  enabled?: boolean;
  refetchOnFocus?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  onSettled?: (data: any | undefined, error: Error | null) => void;
}) => {
  return useBaseQuery(
    createQueryKey('userController', 'retrieveAgentTeams'),
    {
      pathToEndpoint: 'userController.retrieveAgentTeams',
      ...options,
    }
  );
};

export const useRetrieveAdminTeams = (options?: {
  enabled?: boolean;
  refetchOnFocus?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  onSettled?: (data: any | undefined, error: Error | null) => void;
}) => {
  return useBaseQuery(
    createQueryKey('userController', 'retrieveAdminTeams'),
    {
      pathToEndpoint: 'userController.retrieveAdminTeams',
      ...options,
    }
  );
};

export const useRetrieveAdminClubs = (options?: {
  enabled?: boolean;
  refetchOnFocus?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  onSettled?: (data: any | undefined, error: Error | null) => void;
}) => {
  return useBaseQuery(
    createQueryKey('userController', 'retrieveAdminClubs'),
    {
      pathToEndpoint: 'userController.retrieveAdminClubs',
      ...options,
    }
  );
};

// Mutation hooks for user controller
export const useUpdateUser = (options?: {
  onSuccess?: (data: any, variables: { payload: UpdateUserDTO }) => void;
  onError?: (error: Error, variables: { payload: UpdateUserDTO }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: UpdateUserDTO }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: UpdateUserDTO }>({
    pathToEndpoint: 'userController.updateUser',
    ...options,
  });
};

export const useAddVote = (options?: {
  onSuccess?: (data: any, variables: { payload: AddVoteDTO }) => void;
  onError?: (error: Error, variables: { payload: AddVoteDTO }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: AddVoteDTO }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: AddVoteDTO }>({
    pathToEndpoint: 'userController.addVote',
    ...options,
  });
};

export const useAddReaction = (options?: {
  onSuccess?: (data: any, variables: { payload: AddReactionDTO }) => void;
  onError?: (error: Error, variables: { payload: AddReactionDTO }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: AddReactionDTO }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: AddReactionDTO }>({
    pathToEndpoint: 'userController.addReaction',
    ...options,
  });
};

export const useFollowTeam = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { teamUniqueId: string } }) => void;
  onError?: (error: Error, variables: { queryParams: { teamUniqueId: string } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { teamUniqueId: string } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { teamUniqueId: string } }>({
    pathToEndpoint: 'userController.followTeam',
    ...options,
  });
};

export const useUnfollowTeam = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { teamUniqueId: string } }) => void;
  onError?: (error: Error, variables: { queryParams: { teamUniqueId: string } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { teamUniqueId: string } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { teamUniqueId: string } }>({
    pathToEndpoint: 'userController.unfollowTeam',
    ...options,
  });
};

export const useDeleteByEmail = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { email: string } }) => void;
  onError?: (error: Error, variables: { queryParams: { email: string } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { email: string } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { email: string } }>({
    pathToEndpoint: 'userController.deleteByEmail',
    ...options,
  });
};
