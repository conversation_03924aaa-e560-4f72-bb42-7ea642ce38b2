import { useBaseQuery, createQ<PERSON><PERSON><PERSON><PERSON> } from './base-hooks';

// Query hooks for team controller
export const useRetrieveUsersFollowingTeam = (
  queryParams?: { teamId?: number },
  options?: {
    enabled?: boolean;
    refetchOnFocus?: boolean;
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
    onSettled?: (data: any | undefined, error: Error | null) => void;
  }
) => {
  return useBaseQuery(
    createQueryKey('teamController', 'retrieveUsersFollowingTeam', queryParams),
    {
      pathToEndpoint: 'teamController.retrieveUsersFollowingTeam',
      queryParams,
      ...options,
    }
  );
};

export const useRetrieveTeamDetailsByUniqueId = (
  queryParams?: { uniqueId?: string },
  options?: {
    enabled?: boolean;
    refetchOnFocus?: boolean;
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
    onSettled?: (data: any | undefined, error: Error | null) => void;
  }
) => {
  return useBaseQuery(
    createQueryKey('teamController', 'retrieveTeamDetailsByUniqueId', queryParams),
    {
      pathToEndpoint: 'teamController.retrieveTeamDetailsByUniqueId',
      queryParams,
      ...options,
    }
  );
};
