import { SocialLoginType } from '@scorescast/formatters-constants';
import { useAuthStore } from '../stores/useAuthStore';
import {
  useRetrieveUserDetails,
  useRetrieveAdminTeams,
  useRetrieveAdminClubs,
  useUpdateUser,
  useAddReaction,
  useDeleteByEmail,
} from './useUserController';
import { useBaseMutation } from './base-hooks';

export const useUser = () => {
  const { setToken, setUser, clearToken } = useAuthStore();

  // Login mutation using our new API client
  const loginMutation = useBaseMutation<
    any,
    Error,
    { type: typeof SocialLoginType; token: string }
  >({
    pathToEndpoint: 'userController.retrieveUserDetails',
    onSuccess: (response, variables) => {
      if (response?.data) {
        setToken(variables.type, variables.token, response.data);
      }
    },
    onError: (error) => {
      console.error('loginUser error', error);
    },
  });

  // Fetch user mutation
  const fetchUserMutation = useBaseMutation<any, Error, Record<string, never>>({
    pathToEndpoint: 'userController.retrieveUserDetails',
    onSuccess: (response) => {
      if (!response?.error && response?.data) {
        setUser(response.data);
      }
    },
  });

  // Delete user mutation
  const deleteUserMutation = useBaseMutation<
    any,
    Error,
    { queryParams: { email: string } }
  >({
    pathToEndpoint: 'userController.deleteByEmail',
    onSuccess: () => {
      clearToken();
    },
    onError: (error) => {
      console.error('deleteUser error', error);
    },
  });

  // React to event mutation
  const reactEventMutation = useBaseMutation<
    any,
    Error,
    { payload: { eventId: number; reaction: string } }
  >({
    pathToEndpoint: 'userController.addReaction',
    onError: (error) => {
      console.error('reactEvent error', error);
    },
  });

  // Query hooks for admin data
  const {
    data: adminForClubs,
    isLoading: isRetrieveAdminForClubsLoading,
    refetch: refetchRetrieveAdminForClubs,
    error: errorRetrieveAdminForClubs,
  } = useRetrieveAdminClubs({
    enabled: true,
  });

  const {
    data: adminForTeams,
    isLoading: isRetrieveAdminForTeamsLoading,
    refetch: refetchRetrieveAdminForTeams,
    error: errorRetrieveAdminForTeams,
  } = useRetrieveAdminTeams({
    enabled: true,
  });

  // Helper functions that match the old API
  const loginUser = async (params: {
    type: typeof SocialLoginType;
    token: string;
  }) => {
    return loginMutation.mutate({
      type: params.type,
      token: params.token,
    });
  };

  const fetchUser = async () => {
    return fetchUserMutation.mutate({} as Record<string, never>);
  };

  const deleteUser = async (params: { email: string }) => {
    return deleteUserMutation.mutate({
      queryParams: { email: params.email },
    });
  };

  const reactEvent = async (params: {
    eventId: number;
    reactionType: string;
  }) => {
    return reactEventMutation.mutate({
      payload: {
        eventId: params.eventId,
        reaction: params.reactionType,
      },
    });
  };

  return {
    // Admin data
    adminForClubs,
    isRetrieveAdminForClubsLoading,
    refetchRetrieveAdminForClubs,
    errorRetrieveAdminForClubs,
    isErrorRetrieveAdminForClubs: !!errorRetrieveAdminForClubs,

    adminForTeams,
    isRetrieveAdminForTeamsLoading,
    refetchRetrieveAdminForTeams,
    errorRetrieveAdminForTeams,
    isErrorRetrieveAdminForTeams: !!errorRetrieveAdminForTeams,

    // Login functionality
    isLoginLoading: loginMutation.isPending,
    userError: loginMutation.error,
    loginUser,

    // Fetch user functionality
    fetchUser,
    isFetchUserError: fetchUserMutation.isError,
    fetchUserError: fetchUserMutation.error,
    isFetchUserLoading: fetchUserMutation.isPending,

    // Delete user functionality
    deleteUser,
    isDeleteUserLoading: deleteUserMutation.isPending,
    deleteUserError: deleteUserMutation.error,
    isDeleteUserError: deleteUserMutation.isError,

    // React event functionality
    reactEvent,
    isReactEventLoading: reactEventMutation.isPending,
    reactEventError: reactEventMutation.error,
    isReactEventError: reactEventMutation.isError,
  };
};
