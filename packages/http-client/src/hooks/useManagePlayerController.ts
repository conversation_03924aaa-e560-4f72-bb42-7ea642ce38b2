import { useBaseQuery, useBaseMutation, createQ<PERSON><PERSON><PERSON><PERSON> } from './base-hooks';
import type { AddPlayerDTO } from '../types/api-types';

// Query hooks for manage player controller
export const useRetrievePlayersForTeam = (
  queryParams?: { teamId?: number },
  options?: {
    enabled?: boolean;
    refetchOnFocus?: boolean;
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
    onSettled?: (data: any | undefined, error: Error | null) => void;
  }
) => {
  return useBaseQuery(
    createQueryKey('managePlayerController', 'retrievePlayersForTeam', queryParams),
    {
      pathToEndpoint: 'managePlayerController.retrievePlayersForTeam',
      queryParams,
      ...options,
    }
  );
};

// Mutation hooks for manage player controller
export const useCreatePlayer = (options?: {
  onSuccess?: (data: any, variables: { payload: AddPlayerDTO }) => void;
  onError?: (error: Error, variables: { payload: AddPlayerDTO }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: AddPlayerDTO }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: AddPlayerDTO }>({
    pathToEndpoint: 'managePlayerController.create',
    ...options,
  });
};

export const useUpdatePlayer = (options?: {
  onSuccess?: (data: any, variables: { payload: AddPlayerDTO; queryParams: { id: number } }) => void;
  onError?: (error: Error, variables: { payload: AddPlayerDTO; queryParams: { id: number } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: AddPlayerDTO; queryParams: { id: number } }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: AddPlayerDTO; queryParams: { id: number } }>({
    pathToEndpoint: 'managePlayerController.update',
    ...options,
  });
};

export const useDeletePlayerById = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { id: number } }) => void;
  onError?: (error: Error, variables: { queryParams: { id: number } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { id: number } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { id: number } }>({
    pathToEndpoint: 'managePlayerController.deleteById',
    ...options,
  });
};
