import { useBaseQuery, useBaseMutation, createQuery<PERSON>ey } from './base-hooks';

// Query hooks for match controller
export const useRetrieveTeamMatches = (
  queryParams?: { uniqueId?: string; statuses?: string[] },
  options?: {
    enabled?: boolean;
    refetchOnFocus?: boolean;
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
    onSettled?: (data: any | undefined, error: Error | null) => void;
  }
) => {
  return useBaseQuery(
    createQueryKey('matchController', 'retrieveTeamMatches', queryParams),
    {
      pathToEndpoint: 'matchController.retrieveTeamMatches',
      queryParams,
      ...options,
    }
  );
};

export const useRetrieveMatchDetails = (
  queryParams?: { matchId?: number },
  options?: {
    enabled?: boolean;
    refetchOnFocus?: boolean;
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
    onSettled?: (data: any | undefined, error: Error | null) => void;
  }
) => {
  return useBaseQuery(
    createQueryKey('matchController', 'retrieveMatchDetails', queryParams),
    {
      pathToEndpoint: 'matchController.retrieveMatchDetails',
      queryParams,
      ...options,
    }
  );
};

// Mutation hooks for match controller
export const useClaimMatch = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { id: number } }) => void;
  onError?: (error: Error, variables: { queryParams: { id: number } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { id: number } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { id: number } }>({
    pathToEndpoint: 'matchController.claimMatch',
    ...options,
  });
};
