import { useBaseMutation } from './base-hooks';
import type { AddEventDTO } from '../types/api-types';

// Mutation hooks for manage event controller
export const useCreateEvent = (options?: {
  onSuccess?: (data: any, variables: { payload: AddEventDTO }) => void;
  onError?: (error: Error, variables: { payload: AddEventDTO }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: AddEventDTO }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: AddEventDTO }>({
    pathToEndpoint: 'manageEventController.create',
    ...options,
  });
};
