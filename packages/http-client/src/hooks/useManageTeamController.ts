import { useBaseMutation } from './base-hooks';
import type { AddTeamDTO } from '../types/api-types';

// Mutation hooks for manage team controller
export const useCreateTeam = (options?: {
  onSuccess?: (data: any, variables: { payload: AddTeamDTO }) => void;
  onError?: (error: Error, variables: { payload: AddTeamDTO }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: AddTeamDTO }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: AddTeamDTO }>({
    pathToEndpoint: 'manageTeamController.create',
    ...options,
  });
};

export const useUpdateTeam = (options?: {
  onSuccess?: (data: any, variables: { payload: AddTeamDTO; queryParams: { id: number } }) => void;
  onError?: (error: Error, variables: { payload: AddTeamDTO; queryParams: { id: number } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: AddTeamDTO; queryParams: { id: number } }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: AddTeamDTO; queryParams: { id: number } }>({
    pathToEndpoint: 'manageTeamController.update',
    ...options,
  });
};

export const useDeleteTeamById = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { id: number } }) => void;
  onError?: (error: Error, variables: { queryParams: { id: number } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { id: number } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { id: number } }>({
    pathToEndpoint: 'manageTeamController.deleteById',
    ...options,
  });
};
