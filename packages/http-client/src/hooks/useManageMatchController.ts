import { useBaseMutation } from './base-hooks';
import type { AddMatchDTO, SummaryDTO } from '../types/api-types';

// Mutation hooks for manage match controller
export const useCreateMatch = (options?: {
  onSuccess?: (data: any, variables: { payload: AddMatchDTO }) => void;
  onError?: (error: Error, variables: { payload: AddMatchDTO }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: AddMatchDTO }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: AddMatchDTO }>({
    pathToEndpoint: 'manageMatchController.create',
    ...options,
  });
};

export const useAddSummary = (options?: {
  onSuccess?: (data: any, variables: { payload: SummaryDTO }) => void;
  onError?: (error: Error, variables: { payload: SummaryDTO }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { payload: SummaryDTO }) => void;
}) => {
  return useBaseMutation<any, Error, { payload: SummaryDTO }>({
    pathToEndpoint: 'manageMatchController.addSummary',
    ...options,
  });
};

export const useDeleteMatchById = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { id: number } }) => void;
  onError?: (error: Error, variables: { queryParams: { id: number } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { id: number } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { id: number } }>({
    pathToEndpoint: 'manageMatchController.deleteById',
    ...options,
  });
};
