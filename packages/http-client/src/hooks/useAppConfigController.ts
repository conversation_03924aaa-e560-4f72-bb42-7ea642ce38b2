import { useBaseQuery, useBaseMutation, createQ<PERSON>y<PERSON><PERSON> } from './base-hooks';

// Query hooks for app config controller
export const useRetrieveAppConfig = (options?: {
  enabled?: boolean;
  refetchOnFocus?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  onSettled?: (data: any | undefined, error: Error | null) => void;
}) => {
  return useBaseQuery(
    createQueryKey('appConfigController', 'retrieveAppConfig'),
    {
      pathToEndpoint: 'appConfigController.retrieveAppConfig',
      ...options,
    }
  );
};

// Mutation hooks for app config controller
export const useGetUsersCached = (options?: {
  onSuccess?: (data: any, variables: {}) => void;
  onError?: (error: Error, variables: {}) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: {}) => void;
}) => {
  return useBaseMutation<any, Error, {}>({
    pathToEndpoint: 'appConfigController.getUsersCached',
    ...options,
  });
};
