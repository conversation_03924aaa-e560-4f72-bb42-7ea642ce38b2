import { useBaseQuery, useBaseMutation, createQuery<PERSON>ey } from './base-hooks';

// Query hooks for manage user controller
export const useRetrieveFollowersOfTeam = (
  queryParams?: { teamId?: number },
  options?: {
    enabled?: boolean;
    refetchOnFocus?: boolean;
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
    onSettled?: (data: any | undefined, error: Error | null) => void;
  }
) => {
  return useBaseQuery(
    createQueryKey('manageUserController', 'retrieveFollowersOfTeam', queryParams),
    {
      pathToEndpoint: 'manageUserController.retrieveFollowersOfTeam',
      queryParams,
      ...options,
    }
  );
};

export const useRetrieveAgentsOfTeam = (
  queryParams?: { teamId?: number },
  options?: {
    enabled?: boolean;
    refetchOnFocus?: boolean;
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
    onSettled?: (data: any | undefined, error: Error | null) => void;
  }
) => {
  return useBaseQuery(
    createQueryKey('manageUserController', 'retrieveAgentsOfTeam', queryParams),
    {
      pathToEndpoint: 'manageUserController.retrieveAgentsOfTeam',
      queryParams,
      ...options,
    }
  );
};

export const useRetrieveAdminsOfTeam = (
  queryParams?: { teamId?: number },
  options?: {
    enabled?: boolean;
    refetchOnFocus?: boolean;
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
    onSettled?: (data: any | undefined, error: Error | null) => void;
  }
) => {
  return useBaseQuery(
    createQueryKey('manageUserController', 'retrieveAdminsOfTeam', queryParams),
    {
      pathToEndpoint: 'manageUserController.retrieveAdminsOfTeam',
      queryParams,
      ...options,
    }
  );
};

// Mutation hooks for manage user controller
export const useEvictUserCache = (options?: {
  onSuccess?: (data: any, variables: {}) => void;
  onError?: (error: Error, variables: {}) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: {}) => void;
}) => {
  return useBaseMutation<any, Error, {}>({
    pathToEndpoint: 'manageUserController.evictUserCache',
    ...options,
  });
};

export const useRevokeTeamAdmin = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
  onError?: (error: Error, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { userEmail: string; teamUniqueId: string } }>({
    pathToEndpoint: 'manageUserController.revokeTeamAdmin',
    ...options,
  });
};

export const useRevokeAgent = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
  onError?: (error: Error, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { userEmail: string; teamUniqueId: string } }>({
    pathToEndpoint: 'manageUserController.revokeAgent',
    ...options,
  });
};

export const useGrantTeamAdmin = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
  onError?: (error: Error, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { userEmail: string; teamUniqueId: string } }>({
    pathToEndpoint: 'manageUserController.grantTeamAdmin',
    ...options,
  });
};

export const useGrantClubAdmin = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { userEmail: string; clubName: string } }) => void;
  onError?: (error: Error, variables: { queryParams: { userEmail: string; clubName: string } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { userEmail: string; clubName: string } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { userEmail: string; clubName: string } }>({
    pathToEndpoint: 'manageUserController.grantClubAdmin',
    ...options,
  });
};

export const useGrantAgent = (options?: {
  onSuccess?: (data: any, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
  onError?: (error: Error, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
  onSettled?: (data: any | undefined, error: Error | null, variables: { queryParams: { userEmail: string; teamUniqueId: string } }) => void;
}) => {
  return useBaseMutation<any, Error, { queryParams: { userEmail: string; teamUniqueId: string } }>({
    pathToEndpoint: 'manageUserController.grantAgent',
    ...options,
  });
};
