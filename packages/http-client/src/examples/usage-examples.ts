import { apiRequest } from '../core/api-client';
import {
  EnvironmentSwitcher,
  API_ENVIRONMENT,
  setApiEnvironment,
} from '../index';

// Example usage of the new API client

// 1. GET request without payload (retrieve user details)
export async function getUserDetails() {
  try {
    const response = await apiRequest({
      pathToEndpoint: 'userController.retrieveUserDetails',
    });
    return response;
  } catch (error) {
    console.error('Failed to get user details:', error);
    throw error;
  }
}

// 2. POST request with payload (update user)
export async function updateUser(userData: {
  firstName: string;
  lastName: string;
}) {
  try {
    const response = await apiRequest({
      pathToEndpoint: 'userController.updateUser',
      payload: userData,
    });
    return response;
  } catch (error) {
    console.error('Failed to update user:', error);
    throw error;
  }
}

// 3. GET request with query parameters (retrieve team followers)
export async function getTeamFollowers(teamId: number) {
  try {
    const response = await apiRequest({
      pathToEndpoint: 'teamController.retrieveUsersFollowingTeam',
      queryParams: { teamId },
    });
    return response;
  } catch (error) {
    console.error('Failed to get team followers:', error);
    throw error;
  }
}

// 4. PATCH request with path parameters (follow team)
export async function followTeam(teamUniqueId: string) {
  try {
    const response = await apiRequest({
      pathToEndpoint: 'userController.followTeam',
      queryParams: { teamUniqueId }, // This will be used as path parameter
    });
    return response;
  } catch (error) {
    console.error('Failed to follow team:', error);
    throw error;
  }
}

// 5. DELETE request with path parameters (delete user by email)
export async function deleteUserByEmail(email: string) {
  try {
    const response = await apiRequest({
      pathToEndpoint: 'userController.deleteByEmail',
      queryParams: { email }, // This will be used as path parameter
    });
    return response;
  } catch (error) {
    console.error('Failed to delete user:', error);
    throw error;
  }
}

// 6. POST request with payload and custom headers (create team)
export async function createTeam(teamData: {
  name: string;
  image: string;
  clubName: string;
  ageGroup: string;
}) {
  try {
    const response = await apiRequest({
      pathToEndpoint: 'manageTeamController.create',
      payload: teamData,
      headers: {
        'Custom-Header': 'custom-value',
      },
    });
    return response;
  } catch (error) {
    console.error('Failed to create team:', error);
    throw error;
  }
}

// 7. GET request with multiple query parameters (retrieve team matches)
export async function getTeamMatches(uniqueId: string, statuses?: string[]) {
  try {
    const queryParams: Record<string, any> = { uniqueId };
    if (statuses && statuses.length > 0) {
      queryParams.statuses = statuses;
    }

    const response = await apiRequest({
      pathToEndpoint: 'matchController.retrieveTeamMatches',
      queryParams,
    });
    return response;
  } catch (error) {
    console.error('Failed to get team matches:', error);
    throw error;
  }
}

// 8. PATCH request with path parameters and payload (update team)
export async function updateTeam(
  teamId: number,
  teamData: {
    name: string;
    image: string;
    clubName: string;
    ageGroup: string;
  }
) {
  try {
    const response = await apiRequest({
      pathToEndpoint: 'manageTeamController.update',
      queryParams: { id: teamId }, // This will be used as path parameter
      payload: teamData,
    });
    return response;
  } catch (error) {
    console.error('Failed to update team:', error);
    throw error;
  }
}

// 9. Environment switching examples
export function environmentExamples() {
  // Switch to QA environment (includes port 1402)
  EnvironmentSwitcher.switchToQA();

  // Switch to Production environment (no port)
  EnvironmentSwitcher.switchToProduction();

  // Toggle between environments
  EnvironmentSwitcher.toggle();

  // Get current environment info
  const envInfo = EnvironmentSwitcher.getCurrentEnvironmentInfo();
  console.log('Current environment:', envInfo);

  // Log current environment status
  EnvironmentSwitcher.logCurrentEnvironment();

  // Manual environment setting
  setApiEnvironment(API_ENVIRONMENT.QA);
  setApiEnvironment(API_ENVIRONMENT.PRODUCTION);
}

// 10. Example of making requests in different environments
export async function environmentAwareRequest() {
  // Switch to QA for testing
  EnvironmentSwitcher.switchToQA();

  try {
    const qaResponse = await apiRequest({
      pathToEndpoint: 'userController.retrieveUserDetails',
    });
    console.log('QA Response:', qaResponse);
  } catch (error) {
    console.error('QA Request failed:', error);
  }

  // Switch to Production for live data
  EnvironmentSwitcher.switchToProduction();

  try {
    const prodResponse = await apiRequest({
      pathToEndpoint: 'userController.retrieveUserDetails',
    });
    console.log('Production Response:', prodResponse);
  } catch (error) {
    console.error('Production Request failed:', error);
  }
}
