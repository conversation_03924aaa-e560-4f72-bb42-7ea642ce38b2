import React from 'react';
import { View, Text, Button } from 'react-native';
import {
  useRetrieveUserDetails,
  useUpdateUser,
  useRetrieveTeamMatches,
  useCreateTeam,
  useFollowTeam,
  useCombinedFocusTrack,
} from '../hooks';

// Example component showing how to use query hooks
export const UserDetailsExample = () => {
  const { canRefetch, isAppFocused, isScreenFocused } = useCombinedFocusTrack();

  const {
    data: userDetails,
    isLoading,
    error,
    refetch,
  } = useRetrieveUserDetails({
    enabled: true, // Always enabled
    refetchOnFocus: false, // We control refetch manually
    onSuccess: (data) => {
      console.log('✅ User details loaded successfully:', data);
    },
    onError: (error) => {
      console.error('❌ Failed to load user details:', error);
    },
    onSettled: (data, error) => {
      console.log('🏁 User details request settled', { data, error });
    },
  });

  const handleManualRefetch = () => {
    if (canRefetch) {
      refetch();
    } else {
      console.log('⚠️ Cannot refetch - app/screen not focused or offline');
    }
  };

  return (
    <View>
      <Text>Focus Status: App={isAppFocused ? '✅' : '❌'} Screen={isScreenFocused ? '✅' : '❌'}</Text>
      <Text>Can Refetch: {canRefetch ? '✅' : '❌'}</Text>
      
      {isLoading && <Text>Loading user details...</Text>}
      {error && <Text>Error: {error.message}</Text>}
      {userDetails && <Text>User: {JSON.stringify(userDetails, null, 2)}</Text>}
      
      <Button title="Manual Refetch" onPress={handleManualRefetch} />
    </View>
  );
};

// Example component showing how to use mutation hooks
export const UserActionsExample = () => {
  const updateUserMutation = useUpdateUser({
    onSuccess: (data, variables) => {
      console.log('✅ User updated successfully:', data);
      console.log('📝 Update variables:', variables);
      // You can trigger additional actions here
      // e.g., invalidate related queries, show success toast, etc.
    },
    onError: (error, variables) => {
      console.error('❌ Failed to update user:', error);
      console.log('📝 Failed variables:', variables);
      // Handle error - show error toast, etc.
    },
    onSettled: (data, error, variables) => {
      console.log('🏁 Update user request settled');
      // Always runs after success or error
    },
  });

  const followTeamMutation = useFollowTeam({
    onSuccess: (data, variables) => {
      console.log('✅ Team followed successfully:', data);
      // Invalidate related queries to refresh data
      // queryClient.invalidateQueries(['userController', 'retrieveFollowedTeams']);
    },
    onError: (error, variables) => {
      console.error('❌ Failed to follow team:', error);
    },
  });

  const handleUpdateUser = () => {
    updateUserMutation.mutate({
      payload: {
        firstName: 'John',
        lastName: 'Doe',
      },
    });
  };

  const handleFollowTeam = () => {
    followTeamMutation.mutate({
      queryParams: {
        teamUniqueId: 'team-123',
      },
    });
  };

  return (
    <View>
      <Button
        title="Update User"
        onPress={handleUpdateUser}
        disabled={updateUserMutation.isPending}
      />
      <Text>Update Status: {updateUserMutation.isPending ? 'Updating...' : 'Ready'}</Text>

      <Button
        title="Follow Team"
        onPress={handleFollowTeam}
        disabled={followTeamMutation.isPending}
      />
      <Text>Follow Status: {followTeamMutation.isPending ? 'Following...' : 'Ready'}</Text>
    </View>
  );
};

// Example showing query with parameters
export const TeamMatchesExample = () => {
  const [teamId, setTeamId] = React.useState<string>('team-123');

  const {
    data: matches,
    isLoading,
    error,
    refetch,
  } = useRetrieveTeamMatches(
    { uniqueId: teamId, statuses: ['ACTIVE', 'PENDING'] },
    {
      enabled: !!teamId, // Only fetch when teamId is available
      refetchOnFocus: true, // Allow refetch when screen comes into focus
      onSuccess: (data) => {
        console.log(`✅ Matches loaded for team ${teamId}:`, data);
      },
      onError: (error) => {
        console.error(`❌ Failed to load matches for team ${teamId}:`, error);
      },
    }
  );

  return (
    <View>
      <Text>Team ID: {teamId}</Text>
      {isLoading && <Text>Loading matches...</Text>}
      {error && <Text>Error: {error.message}</Text>}
      {matches && <Text>Matches: {JSON.stringify(matches, null, 2)}</Text>}
      
      <Button title="Refetch Matches" onPress={() => refetch()} />
      <Button title="Change Team" onPress={() => setTeamId('team-456')} />
    </View>
  );
};

// Example showing complex mutation with multiple parameters
export const CreateTeamExample = () => {
  const createTeamMutation = useCreateTeam({
    onSuccess: (data, variables) => {
      console.log('✅ Team created successfully:', data);
      console.log('📝 Team data:', variables.payload);
      
      // Example of additional actions after successful creation
      // 1. Invalidate team lists to refresh
      // 2. Navigate to the new team page
      // 3. Show success notification
      // 4. Update local state
    },
    onError: (error, variables) => {
      console.error('❌ Failed to create team:', error);
      console.log('📝 Failed team data:', variables.payload);
      
      // Example error handling
      // 1. Show error notification
      // 2. Log error for debugging
      // 3. Reset form if needed
    },
    onSettled: (data, error, variables) => {
      console.log('🏁 Create team request completed');
      
      // Example cleanup actions
      // 1. Hide loading indicators
      // 2. Re-enable form
      // 3. Clear temporary state
    },
  });

  const handleCreateTeam = () => {
    createTeamMutation.mutate({
      payload: {
        name: 'New Team',
        image: 'https://example.com/team-logo.png',
        clubName: 'Example Club',
        ageGroup: 'U18',
      },
    });
  };

  return (
    <View>
      <Button
        title="Create Team"
        onPress={handleCreateTeam}
        disabled={createTeamMutation.isPending}
      />
      
      <Text>
        Status: {createTeamMutation.isPending ? 'Creating...' : 'Ready'}
      </Text>
      
      {createTeamMutation.isError && (
        <Text>Error: {createTeamMutation.error?.message}</Text>
      )}
      
      {createTeamMutation.isSuccess && (
        <Text>✅ Team created successfully!</Text>
      )}
    </View>
  );
};
