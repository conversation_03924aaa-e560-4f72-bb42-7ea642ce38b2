import { MMKV } from 'react-native-mmkv';

export enum STORAGE_KEYS {
  TOKEN = 'token',
  PROVIDER = 'provider',
  APP_LAUNCH = 'app_launch',
  LOGIN_TYPE = 'login_type',
  SELECTED_TEAM_ID = 'selected_team_id',
  NOTIFICATIONS = 'notifications',
  API_ENVIRONMENT = 'api_environment',
}

export enum API_ENVIRONMENT {
  PRODUCTION = 'production',
  QA = 'qa',
}

export const storage = new MMKV();

// Utility functions for API environment management
export const getApiEnvironment = (): API_ENVIRONMENT => {
  const storedEnv = storage.getString(STORAGE_KEYS.API_ENVIRONMENT);
  return storedEnv === API_ENVIRONMENT.QA
    ? API_ENVIRONMENT.QA
    : API_ENVIRONMENT.PRODUCTION;
};

export const setApiEnvironment = (environment: API_ENVIRONMENT): void => {
  storage.set(STORAGE_KEYS.API_ENVIRONMENT, environment);
};

export const isQAEnvironment = (): boolean => {
  return getApiEnvironment() === API_ENVIRONMENT.QA;
};

export const isProductionEnvironment = (): boolean => {
  return getApiEnvironment() === API_ENVIRONMENT.PRODUCTION;
};
