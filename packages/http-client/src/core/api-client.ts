import { apiEndpoints } from '../config/endpoints';
import { getAPIHeaders } from '../helpers/getAPIHeaders';
import Config from '../config/config';

// Type to extract nested endpoint paths
type EndpointPath = {
  [K in keyof typeof apiEndpoints]: {
    [P in keyof (typeof apiEndpoints)[K]]: `${K & string}.${P & string}`;
  }[keyof (typeof apiEndpoints)[K]];
}[keyof typeof apiEndpoints];

// Helper type to get endpoint config from path
type GetEndpointConfig<T extends EndpointPath> =
  T extends `${infer Controller}.${infer Action}`
    ? Controller extends keyof typeof apiEndpoints
      ? Action extends keyof (typeof apiEndpoints)[Controller]
        ? (typeof apiEndpoints)[Controller][Action]
        : never
      : never
    : never;

// Extract payload type from endpoint config
type GetPayloadType<T extends EndpointPath> =
  GetEndpointConfig<T> extends {
    payloadType: infer P;
  }
    ? P extends null
      ? undefined
      : P extends string
        ? any // We'll improve this later with proper type mapping
        : never
    : never;

// Extract response type from endpoint config
type GetResponseType<T extends EndpointPath> =
  GetEndpointConfig<T> extends {
    responseType: infer R;
  }
    ? R extends string
      ? any // We'll improve this later with proper type mapping
      : never
    : never;

// API Request options
interface ApiRequestOptions<T extends EndpointPath> {
  pathToEndpoint: T;
  headers?: Record<string, string>;
  payload?: GetPayloadType<T>;
  queryParams?: Record<string, string | number | boolean>;
  isLoggedIn?: boolean;
}

// Helper function to get nested endpoint config
function getEndpointConfig(pathToEndpoint: EndpointPath): {
  path: string;
  method: 'GET' | 'POST' | 'PATCH' | 'DELETE' | 'PUT';
  payloadType: any;
  responseType: any;
} {
  const [controller, action] = pathToEndpoint.split('.') as [
    keyof typeof apiEndpoints,
    string,
  ];

  const controllerConfig = apiEndpoints[controller];
  if (!controllerConfig) {
    throw new Error(`Controller "${controller}" not found in apiEndpoints`);
  }

  const endpointConfig =
    controllerConfig[action as keyof typeof controllerConfig];
  if (!endpointConfig) {
    throw new Error(
      `Action "${action}" not found in controller "${controller}"`
    );
  }

  return endpointConfig as {
    path: string;
    method: 'GET' | 'POST' | 'PATCH' | 'DELETE' | 'PUT';
    payloadType: any;
    responseType: any;
  };
}

// Helper function to replace path parameters
function replacePathParameters(
  path: string,
  queryParams?: Record<string, string | number | boolean>
): string {
  if (!queryParams) return path;

  let processedPath = path;
  const pathParams: Record<string, string | number | boolean> = {};
  const remainingParams: Record<string, string | number | boolean> = {
    ...queryParams,
  };

  // Extract path parameters (e.g., {id}, {email}, {teamUniqueId})
  const pathParamMatches = path.match(/\{([^}]+)\}/g);

  if (pathParamMatches) {
    pathParamMatches.forEach((match) => {
      const paramName = match.slice(1, -1); // Remove { and }
      if (paramName in queryParams) {
        pathParams[paramName] = queryParams[paramName];
        processedPath = processedPath.replace(
          match,
          String(queryParams[paramName])
        );
        delete remainingParams[paramName];
      }
    });
  }

  return processedPath;
}

// Helper function to build query string
function buildQueryString(
  params: Record<string, string | number | boolean>
): string {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value));
    }
  });

  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : '';
}

// Main API client function
export async function apiRequest<T extends EndpointPath>(
  options: ApiRequestOptions<T>
): Promise<GetResponseType<T>> {
  const {
    pathToEndpoint,
    headers: customHeaders = {},
    payload,
    queryParams,
    isLoggedIn = true,
  } = options;

  try {
    // Get endpoint configuration
    const endpointConfig = getEndpointConfig(pathToEndpoint);

    // Process path parameters and build final path
    const processedPath = replacePathParameters(
      endpointConfig.path,
      queryParams
    );

    // Build query string for remaining parameters (for GET requests mainly)
    const remainingParams = { ...queryParams };

    // Remove path parameters from remaining params
    const pathParamMatches = endpointConfig.path.match(/\{([^}]+)\}/g);
    if (pathParamMatches) {
      pathParamMatches.forEach((match) => {
        const paramName = match.slice(1, -1);
        delete remainingParams[paramName];
      });
    }

    const queryString =
      Object.keys(remainingParams).length > 0
        ? buildQueryString(remainingParams)
        : '';

    // Build full URL
    const fullUrl = Config.getFullUrl(processedPath, queryString);

    // Get headers
    const defaultHeaders = await getAPIHeaders(isLoggedIn, customHeaders);

    // Prepare request options
    const requestOptions: RequestInit = {
      method: endpointConfig.method,
      headers: defaultHeaders,
    };

    // Add body for POST, PATCH, PUT requests
    if (['POST', 'PATCH', 'PUT'].includes(endpointConfig.method) && payload) {
      requestOptions.body = JSON.stringify(payload);
    }

    console.log('🚀 [API Request]', {
      pathToEndpoint,
      method: endpointConfig.method,
      url: fullUrl,
      headers: defaultHeaders,
      payload,
    });

    // Make the request
    const response = await fetch(fullUrl, requestOptions);

    console.log('📥 [API Response]', {
      pathToEndpoint,
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
    });

    // Handle response
    if (!response.ok) {
      let errorBody;
      try {
        errorBody = await response.text();
        console.error('❌ [API Error]', {
          pathToEndpoint,
          status: response.status,
          errorBody,
        });
      } catch (e) {
        console.error('❌ [API Error] Could not read error response body:', e);
      }

      throw new Error(
        `API request failed: ${response.status} ${response.statusText}`
      );
    }

    // Parse response
    const responseData = await response.json();

    console.log('✅ [API Success]', {
      pathToEndpoint,
      responseData,
    });

    return responseData;
  } catch (error) {
    console.error('💥 [API Request Failed]', {
      pathToEndpoint,
      error,
    });
    throw error;
  }
}
