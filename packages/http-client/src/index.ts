// Main API client
export { apiRequest } from './core/api-client';

// Types
export type * from './types/api-types';

// Endpoints configuration
export { apiEndpoints } from './config/endpoints';

// Utilities
export { getAPIHeaders } from './helpers/getAPIHeaders';
export {
  storage,
  STORAGE_KEYS,
  API_ENVIRONMENT,
  getApiEnvironment,
  setApiEnvironment,
  isQAEnvironment,
  isProductionEnvironment,
} from './storage/storage';
export { default as Config } from './config/config';

// Development utilities
export { EnvironmentSwitcher } from './utils/environment-switcher';
