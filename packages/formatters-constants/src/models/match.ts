import { MatchPossibleStatus } from '../constants/match';
import { Reaction } from './reactions';
type Event = {
  eventType: string;
  metadata: Record<string, any>;
  period: string;
  side?: 'left' | 'right';
  id: number;
  reactions?: Reaction[];
};

type Feed = {
  END?: Event[];
  ET1?: Event[];
  ET2?: Event[];
  P1?: Event[];
  P2?: Event[];
  P3?: Event[];
  P4?: Event[];
  PENS?: Event[];
};

interface MatchResult {
  homeGoals: number;
  awayGoals: number;
}

export type Claim = 'NOT_CLAIMED' | 'CLAIMED_BY_YOU' | 'CLAIMED' | 'CAN_CLAIM';

export enum MatchSetup {
  'P1' = 'P1',
  'P2' = 'P2',
  'P4' = 'P4',
}

export interface Match {
  id: string;
  usTeamName: string;
  themTeamName: string;
  themTeamImage: string;
  description: string;
  matchTime: Date;
  status: MatchPossibleStatus;
  fullTime: MatchResult;
  penalty: MatchResult;
  extraTime: MatchResult;
  claimStatus: Claim;
  location: 'HOME' | 'AWAY';
  feeds: Feed;
  matchSetup: 'P2' | 'P4' | any;
}

export interface TeamMatchesResponse {
  data: Match[];
  status: string;
}
