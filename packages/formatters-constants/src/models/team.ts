export interface Team {
  name: string;
  image?: string;
  teamUniqueId?: string;
  clubId?: string;
  id: number;
}

export interface CreateTeamData {
  name: string;
  shortName: string;
  image: string;
  clubName: string;
  ageGroup: string;
}

export interface UpdateTeamData {
  id: number;
  name: string;
  image: string;
}

export interface FullTeam {
  id: number;
  jerseyName: string;
  number: string;
  person: {
    dob: Date;
    federation: string;
    federationId: string;
    firstName: string;
    id: string;
    lastName: string;
  };
  team: {
    club: any;
    id: number;
    image: string;
    name: string;
    shortName: string;
    teamUniqueId: string;
  };

  name: string;
  image?: string;
  teamUniqueId?: string;
  clubId?: string;
}

export interface TeamsResponse {
  data: Team[];
  status: string;
}
