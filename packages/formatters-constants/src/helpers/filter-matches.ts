import { MatchPossibleStatus } from '../constants/match';
import { TeamMatchesTabsKey } from '../constants/tabs';
import { Match } from '../models/match';

export function filterMatchesByStatus(
  matches: Match[],
  selectedTab: TeamMatchesTabsKey
): Match[] {
  const now = new Date().getTime();

  return matches
    .filter((match) => {
      if (selectedTab === TeamMatchesTabsKey.ALL) return true;
      if (selectedTab === TeamMatchesTabsKey.LIVE)
        return ![MatchPossibleStatus.NS, MatchPossibleStatus.END].includes(
          match?.status
        );
      if (selectedTab === TeamMatchesTabsKey.UPCOMING)
        return match?.status === MatchPossibleStatus.NS;
      if (selectedTab === TeamMatchesTabsKey.PAST)
        return match?.status === MatchPossibleStatus.END;
      return false;
    })
    .sort(
      (a, b) =>
        Math.abs(new Date(a.matchTime).getTime() - now) -
        Math.abs(new Date(b.matchTime).getTime() - now)
    );
}
