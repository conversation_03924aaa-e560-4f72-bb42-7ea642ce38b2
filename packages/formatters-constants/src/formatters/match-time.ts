import { MatchPossibleStatus } from '../constants/match';
import { i18next } from '@scorescast/translations';

function mapMatchStatus(status: MatchPossibleStatus) {
  switch (status) {
    case MatchPossibleStatus.NS:
      return i18next.t('MATCH_TIME.UPCOMING');
    case MatchPossibleStatus.P1:
      return i18next.t('MATCH_TIME.P1');
    case MatchPossibleStatus['P1_BREAK']:
      return i18next.t('MATCH_TIME.P1_BREAK');
    case MatchPossibleStatus.P2:
      return i18next.t('MATCH_TIME.P2');
    case MatchPossibleStatus.P3:
      return i18next.t('MATCH_TIME.P3');
    case MatchPossibleStatus['P3_BREAK']:
      return i18next.t('MATCH_TIME.P3_BREAK');
    case MatchPossibleStatus.P4:
      return i18next.t('MATCH_TIME.P4');
    case MatchPossibleStatus.HT:
      return i18next.t('MATCH_TIME.HT');
    case MatchPossibleStatus.END:
      return i18next.t('MATCH_TIME.PAST');
    case MatchPossibleStatus.ET1:
      return i18next.t('MATCH_TIME.ET1');
    case MatchPossibleStatus['ETHT']:
      return i18next.t('MATCH_TIME.ETHT');
    case MatchPossibleStatus.ET2:
      return i18next.t('MATCH_TIME.ET2');
    case MatchPossibleStatus.AET:
      return i18next.t('MATCH_TIME.AET');
    case MatchPossibleStatus.PENS:
      return i18next.t('MATCH_TIME.PENS');
    default:
      return status;
  }
}

export function formatMatchDate(
  dateString: Date,
  scheduled: MatchPossibleStatus
) {
  const date = new Date(dateString);

  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();

  const formattedDate = `${day}.${month}.${year}`;
  let hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const ampm = hours >= 12 ? 'PM' : 'AM';

  hours = hours % 12;
  hours = hours ? hours : 12;

  const formattedTime = `${hours}:${minutes} ${ampm}`;

  if (scheduled === MatchPossibleStatus.NS) {
    return `${formattedDate} | ${formattedTime} | ${i18next.t('MATCH_TIME.UPCOMING')}`;
  } else if (scheduled === MatchPossibleStatus.END) {
    return `${formattedDate} | ${formattedTime}`;
  } else {
    return `${formattedDate} | ${formattedTime} | ${i18next.t('MATCH_TIME.LIVE')}`;
  }
}

export function formatMatchTime(
  dateString: Date,
  scheduled: MatchPossibleStatus
) {
  const date = new Date(dateString);
  let hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, '0');

  hours = hours % 12;
  hours = hours ? hours : 12;

  const formattedTime = `${hours}:${minutes}`;
  if (scheduled === MatchPossibleStatus.NS) {
    return formattedTime;
  } else if (scheduled === MatchPossibleStatus.END) {
    return i18next.t('MATCH_TIME.PAST');
  } else if (
    ![MatchPossibleStatus.NS, MatchPossibleStatus.END].includes(scheduled)
  ) {
    return mapMatchStatus(scheduled);
  }

  return '';
}
