export enum ClaimStatuses {
  NOT_CLAIMED = 'NOT_CLAIMED',
  CLAIMED_BY_YOU = 'CLAIMED_BY_YOU',
  CLAIMED = 'CLAIMED',
  CAN_CLAIM = 'CAN_CLAIM',
}

export enum MatchPossibleStatus {
  'NS' = 'NS',
  'P1' = 'P1',
  'P1_BREAK' = 'P1_BREAK',
  'P2' = 'P2',
  'HT' = 'HT',
  'P3' = 'P3',
  'P3_BREAK' = 'P3_BREAK',
  'P4_BREAK' = 'P4_BREAK',
  'P4' = 'P4',
  'FT' = 'FT',
  'ET' = 'ET',
  'ET1' = 'ET1',
  'ETHT' = 'ETHT',
  'ET2' = 'ET2',
  'AET' = 'AET',
  'PENS' = 'PENS',
  'END' = 'END',
}

export enum MatchEventTypes {
  KICKOFF = 'KICKOFF',
  P1_END = 'P1END',
  P1_END__1_PERIODS = 'P1_END__1_PERIODS',
  P1_END__4_PERIODS = 'P1_END__4_PERIODS',
  P2_END__4_PERIODS = 'P2_END__4_PERIODS',
  P2_KICKOFF = 'P2_KICKOFF',
  P3_KICKOFF = 'P3_KICKOFF',
  P4_KICKOFF = 'P4_KICKOFF',
  P2_END = 'P2END',
  P3_END = 'P3END',
  P4_END = 'P4END',
  END_GAME = 'END_GAME',
  ET_KICKOFF = 'ET_KICKOFF',
  ET_KICKOFF__1_PERIODS = 'ET_KICKOFF__1_PERIODS',
  ET1_END = 'ET1_END',
  ET_END = 'ET_END',
  ET2_KICKOFF = 'ET2_KICKOFF',
  ET2_END = 'ET2_END',
  PENS_START = 'PENS_START',
  GOAL_VIEW = 'GOAL_VIEW',
  FREEKICK = 'FREEKICK',
  OWN_GOAL = 'OWN_GOAL',
  SHOT = 'SHOT',
  PENALTY = 'PENALTY',
  PENALTY_GOAL = 'PENALTY_GOAL',
  PENALTY_SAVE = 'PENALTY_SAVE',
  PENALTY_MISS = 'PENALTY_MISS',
  FOUL_VIEW = 'FOUL_VIEW',
  FOUL_FREEKICK = 'FOUL_FREEKICK',
  FOUL_PENALTY = 'FOUL_PENALTY',
  FOUL_CARD = 'FOUL_CARD',
  PASS = 'PASS',
  YELLOW_CARD = 'YELLOW_CARD',
  SECOND_YELLOW_CARD = 'SECOND_YELLOW_CARD',
  RED_CARD = 'RED_CARD',
  CARDS = 'CARDS',
}
