import { IconName } from '@scorescast/design-system';
import { MatchEventTypes } from './match';
import { i18next } from '@scorescast/translations';
import { ColorStyles } from '@scorescast/design-system';

export enum AgentEventSideSelection {
  US = 'US',
  THEM = 'THEM',
}

export enum MatchLocations {
  AWAY = 'AWAY',
  HOME = 'HOME',
}

export const MatchPossibleEvents = [
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_EVENTS.GOAL'),
    id: MatchEventTypes.GOAL_VIEW,
    icon: IconName.SHOT_BALL,
  },
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_EVENTS.FOUL'),
    id: MatchEventTypes.FOUL_VIEW,
    icon: IconName.FOUL,
  },
];

export const MatchPossibleTeams = [
  {
    value: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_TEAMS.US'),
    id: AgentEventSideSelection.US,
  },
  {
    value: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_TEAMS.THEM'),
    id: AgentEventSideSelection.THEM,
  },
];

export const MatchPossibleGoalTypes = [
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_GOAL_TYPES.SHOT'),
    id: MatchEventTypes.SHOT,
    icon: IconName.SHOT_BALL,
  },
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_GOAL_TYPES.FREEKICK'),
    id: MatchEventTypes.FREEKICK,
    icon: IconName.SHOT_FREEKICK,
  },
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_GOAL_TYPES.OWN_GOAL'),
    id: MatchEventTypes.OWN_GOAL,
    icon: IconName.SHOT_OWN_GOAL,
  },
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_GOAL_TYPES.PENALTY'),
    id: MatchEventTypes.PENALTY,
    icon: IconName.SHOT_PENALTY,
  },
];

export const MatchPossibleFoulTypes = [
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_FOUL_TYPES.FOUL_FREEKICK'),
    id: MatchEventTypes.FOUL_FREEKICK,
    icon: IconName.SHOT_FREEKICK,
  },
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_FOUL_TYPES.FOUL_PENALTY'),
    id: MatchEventTypes.FOUL_PENALTY,
    icon: IconName.SHOT_BALL,
  },
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_FOUL_TYPES.CARDS'),
    id: MatchEventTypes.CARDS,
    icon: IconName.FEED_SECOND_YELLOW_CARD,
  },
];

export const MatchPossiblePenaltyTypesWhenFoulPenalty = [
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_PENALTY_TYPES.GOAL'),
    id: MatchEventTypes.PENALTY_GOAL,
    icon: IconName.SHOT_BALL,
  },
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_PENALTY_TYPES.MISS'),
    id: MatchEventTypes.PENALTY_MISS,
    icon: IconName.SHOT_OWN_GOAL,
  },
];

export const MatchPossiblePenaltyTypes = [
  ...MatchPossiblePenaltyTypesWhenFoulPenalty,
  {
    label: i18next.t('AGENT.LIVE_MATCH.POSSIBLE_PENALTY_TYPES.SAVE'),
    id: MatchEventTypes.PENALTY_SAVE,
    icon: IconName.SAVE_HAND,
  },
];

export const getMatchPossibilitesCards = (colors: typeof ColorStyles) => [
  {
    id: 'YELLOW_CARD',
    label: i18next.t('AGENT.FOUL_SELECTION.POSSIBLE_CARDS.YELLOW_CARD'),
    selectedColor: colors.yellow,
    color: colors.yellow25,
  },
  {
    id: 'RED_CARD',
    label: i18next.t('AGENT.FOUL_SELECTION.POSSIBLE_CARDS.RED_CARD'),
    selectedColor: colors.red,
    color: colors.red25,
  },
  {
    id: 'SECOND_YELLOW_CARD',
    label: i18next.t('AGENT.FOUL_SELECTION.POSSIBLE_CARDS.SECOND_YELLOW_CARD'),
    selectedColor: colors.yellow,
    color: colors.yellow25,
    selectedOverlayColor: colors.red,
    overlay: colors.red25,
  },
];
