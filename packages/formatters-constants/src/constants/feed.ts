import { i18next } from '@scorescast/translations';
import { IconName } from '@scorescast/design-system';

export const FEED_PERIOD_LABELS = {
  P1: i18next.t('MATCH_FEED.PERIOD_1'),
  P2: i18next.t('MATCH_FEED.PERIOD_2'),
  P3: i18next.t('MATCH_FEED.PERIOD_3'),
  P4: i18next.t('MATCH_FEED.PERIOD_4'),
  ET1: i18next.t('MATCH_FEED.EXTRA_TIME_1'),
  ET2: i18next.t('MATCH_FEED.EXTRA_TIME_2'),
  PENS: i18next.t('MATCH_FEED.PENALTIES'),
};

export const FEED_EVENT_TYPES = {
  FOUL: {
    icon: IconName.FEED_FAULT,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.FOUL.TITLE'),
    },
  },
  YELLOW_CARD: {
    icon: IconName.FEED_YELLOW_CARD,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.YELLOW_CARD.TITLE'),
    },
  },
  RED_CARD: {
    icon: IconName.FEED_RED_CARD,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.RED_CARD.TITLE'),
    },
  },
  SECOND_YELLOW_CARD: {
    icon: IconName.FEED_SECOND_YELLOW_CARD,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.SECOND_YELLOW_CARD.TITLE'),
    },
  },
  PENALTY_CONCEDED: {
    icon: IconName.FEED_FAULT,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.PENALTY_CONCEDED.TITLE'),
    },
  },
  PENALTY_GOAL: {
    icon: IconName.FEED_PENALTY,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.PENALTY_GOAL.TITLE'),
    },
  },
  MISS_PENALTY: {
    icon: IconName.FEED_MISS_PENALTY,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.MISS_PENALTY.TITLE'),
    },
  },
  SAVED_PENALTY: {
    icon: IconName.FEED_PENALTY_SAVED,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.SAVED_PENALTY.TITLE'),
    },
  },
  GOAL: {
    icon: IconName.FEED_GOAL,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.GOAL.TITLE'),
    },
  },
  OWN_GOAL: {
    icon: IconName.FEED_OWN_GOAL,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.OWN_GOAL.TITLE'),
    },
  },
  ASSIST: {
    icon: IconName.FEED_ASSIST,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.ASSIST.TITLE'),
    },
  },
  KICKOFF: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.KICKOFF.TITLE'),
    },
  },
  END_P1: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.END_P1.TITLE'),
    },
  },
  KICKOFF_P2: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.KICKOFF_P2.TITLE'),
    },
  },
  END_P2: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.END_P2.TITLE'),
    },
  },
  KICKOFF_P3: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.KICKOFF_P3.TITLE'),
    },
  },
  END_P3: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.END_P3.TITLE'),
    },
  },
  KICKOFF_P4: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.KICKOFF_P4.TITLE'),
    },
  },
  END_P4: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.END_P4.TITLE'),
    },
  },
  KICKOFF_ET1: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.KICKOFF_ET1.TITLE'),
    },
  },
  END_ET1: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.END_ET1.TITLE'),
    },
  },
  KICKOFF_ET2: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.KICKOFF_ET2.TITLE'),
    },
  },
  END_ET2: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.END_ET2.TITLE'),
    },
  },
  KICKOFF_PENS: {
    icon: IconName.FEED_TIME,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.KICKOFF_PENS.TITLE'),
    },
  },
  END: {
    icon: IconName.FEED_TIME_BLACK,
    labels: {
      title: i18next.t('MATCH_FEED.FEED_ITEMS.END.TITLE'),
    },
  },
};

export const FEED_EVENT_TYPES_KEYS = {
  FOUL: 'FOUL',
  YELLOW_CARD: 'YELLOW_CARD',
  RED_CARD: 'RED_CARD',
  SECOND_YELLOW_CARD: 'SECOND_YELLOW_CARD',
  PENALTY_CONCEDED: 'PENALTY_CONCEDED',
  PENALTY_GOAL: 'PENALTY_GOAL',
  MISS_PENALTY: 'MISS_PENALTY',
  SAVED_PENALTY: 'SAVED_PENALTY',
  GOAL: 'GOAL',
  OWN_GOAL: 'OWN_GOAL',
  ASSIST: 'ASSIST',
  KICKOFF: 'KICKOFF',
  END_P1: 'END_P1',
  KICKOFF_P2: 'KICKOFF_P2',
  END_P2: 'END_P2',
  KICKOFF_P3: 'KICKOFF_P3',
  END_P3: 'END_P3',
  KICKOFF_P4: 'KICKOFF_P4',
  END_P4: 'END_P4',
  KICKOFF_ET1: 'KICKOFF_ET1',
  END_ET1: 'END_ET1',
  KICKOFF_ET2: 'KICKOFF_ET2',
  END_ET2: 'END_ET2',
  KICKOFF_PENS: 'KICKOFF_PENS',
  END: 'END',
};

export const FEED_PERIOD_ORDER = [
  'END',
  'PENS',
  'ET2',
  'ET1',
  'P4',
  'P3',
  'P2',
  'P1',
];
