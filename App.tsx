/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, { useEffect, useRef, useState } from 'react';

import { ThemeProvider, Toaster } from '@scorescast/design-system';
import { i18next, I18nextProvider } from '@scorescast/translations';
import { SocialClientIds } from '@scorescast/formatters-constants';
import {
  NavigationContainer,
  NavigationContainerRef,
} from '@react-navigation/native';
import { RootNavigator } from './src/navigator/RootNavigator';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import {
  BottomSheetModal,
  BottomSheetModalProvider,
} from '@gorhom/bottom-sheet';
import {
  MutationCache,
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query';
import { useAuthStore, startNetworkListener } from '@scorescast/http-client';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { Settings } from 'react-native-fbsdk-next';
import { useNewRelic } from '@scorescast/analytics';
import { useNotifications } from './src/hooks/useNotifications';
import AppLoader from './src/components/AppLoader/AppLoader';
import { useAppsFlyer } from './src/hooks/useAppsFlyer';
import { TeamConfirmation } from './src/components/TeamConfirmation/TeamConfirmation';

const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: () => {},
  }),
  mutationCache: new MutationCache({
    onError: () => {},
  }),
});
function ReactQueryProvider({ children }: React.PropsWithChildren) {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}

function App(): React.JSX.Element {
  const navigationRef = useRef<NavigationContainerRef<any>>(null);
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const [isNavReady, setIsNavReady] = useState(false);
  const initializeToken = useAuthStore((state) => state.initializeToken);
  const { initializeNewRelic } = useNewRelic();
  const { initWonderPush } = useNotifications();
  const { initAppsFlyer } = useAppsFlyer();

  GoogleSignin.configure({
    iosClientId: SocialClientIds.GOOGLE_IOS_CLIEND_ID,
    webClientId: SocialClientIds.GOOGLE_WEB_CLIEND_ID,
  });
  Settings.initializeSDK();

  useEffect(() => {
    if (isNavReady) {
      initializeNewRelic();
      initWonderPush(navigationRef);
      initializeToken();
      startNetworkListener();
      const { onDeepLinkCanceller } = initAppsFlyer();
      return () => {
        onDeepLinkCanceller();
      };
    }
  }, [isNavReady]);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <I18nextProvider i18n={i18next}>
        <ThemeProvider>
          <ReactQueryProvider>
            <AppLoader>
              <BottomSheetModalProvider>
                <NavigationContainer
                  ref={navigationRef}
                  onReady={() => {
                    setIsNavReady(true);
                  }}
                >
                  <RootNavigator />
                  <Toaster />
                  <TeamConfirmation ref={bottomSheetRef} />
                </NavigationContainer>
              </BottomSheetModalProvider>
            </AppLoader>
          </ReactQueryProvider>
        </ThemeProvider>
      </I18nextProvider>
    </GestureHandlerRootView>
  );
}

export default App;
