{"compilerOptions": {"target": "es2020", "moduleResolution": "node", "strict": true, "noEmitOnError": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "allowSyntheticDefaultImports": true, "noImplicitReturns": true, "noStrictGenericChecks": false, "noUnusedLocals": false, "resolveJsonModule": true, "skipLibCheck": true, "noImplicitUseStrict": false, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "noEmit": true, "jsx": "react"}, "include": ["**/*.ts"]}