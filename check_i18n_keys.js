const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const enTranslations = JSON.parse(
  fs.readFileSync('./packages/translations/src/resources/en.json', 'utf8')
);
const roTranslations = JSON.parse(
  fs.readFileSync('./packages/translations/src/resources/ro.json', 'utf8')
);

function extractKeys(obj, prefix = '') {
  let keys = [];
  for (const key in obj) {
    const newPrefix = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys = [...keys, ...extractKeys(obj[key], newPrefix)];
    } else {
      keys.push(newPrefix);
    }
  }
  return keys;
}

const enKeys = extractKeys(enTranslations);
const roKeys = extractKeys(roTranslations);

const missingInRo = enKeys.filter((key) => !roKeys.includes(key));

const extraInRo = roKeys.filter((key) => !enKeys.includes(key));

const findTranslationKeysInCode = () => {
  try {
    const files = execSync(
      'find . -type f -name "*.tsx" -o -name "*.ts" | grep -v "node_modules" | xargs grep -l "i18next.t"'
    )
      .toString()
      .trim()
      .split('\n');

    const keysInCode = new Set();
    const keyRegex = /i18next\.t\(['"]([^'"]+)['"]/g;

    files.forEach((file) => {
      const content = fs.readFileSync(file, 'utf8');
      let match;
      while ((match = keyRegex.exec(content)) !== null) {
        keysInCode.add(match[1]);
      }
    });

    return Array.from(keysInCode);
  } catch (error) {
    console.error('Error extracting keys from code:', error);
    return [];
  }
};

const keysInCode = findTranslationKeysInCode();

const missingInEn = keysInCode.filter((key) => {
  const baseKey = key
    .split('.')
    .filter((part) => !part.includes('{{'))
    .join('.');
  return !enKeys.some((enKey) => enKey.startsWith(baseKey));
});

const missingInBoth = keysInCode.filter((key) => {
  const baseKey = key
    .split('.')
    .filter((part) => !part.includes('{{'))
    .join('.');
  return (
    !enKeys.some((enKey) => enKey.startsWith(baseKey)) &&
    !roKeys.some((roKey) => roKey.startsWith(baseKey))
  );
});

console.log('=== TRANSLATION KEY ANALYSIS ===');
console.log(`\nTotal keys in en.json: ${enKeys.length}`);
console.log(`Total keys in ro.json: ${roKeys.length}`);
console.log(`Total unique keys found in code: ${keysInCode.length}`);

console.log('\n=== MISSING TRANSLATIONS ===');
console.log(`\nKeys in en.json missing from ro.json (${missingInRo.length}):`);
missingInRo.forEach((key) => console.log(`- ${key}`));

console.log(`\nKeys in ro.json missing from en.json (${extraInRo.length}):`);
extraInRo.forEach((key) => console.log(`- ${key}`));

console.log(
  `\nKeys used in code but missing in en.json (${missingInEn.length}):`
);
missingInEn.forEach((key) => console.log(`- ${key}`));

console.log(
  `\nKeys used in code but missing in both translation files (${missingInBoth.length}):`
);
missingInBoth.forEach((key) => console.log(`- ${key}`));
