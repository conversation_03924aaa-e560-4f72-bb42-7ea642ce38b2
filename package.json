{"name": "scorescast", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "check-translation-keys": "node ./check_i18n_keys.js", "prettier": "npx prettier --write .", "lint:fix": "eslint . --fix", "clean:node": "sudo rm -rf node_modules && rm -f yarn.lock && rm -f package-lock.json", "clean:ios": "cd ios && sudo rm -rf Pods Podfile.lock build DerivedData && cd ..", "clean:android": "cd android && sudo rm -rf build app/build .gradle .idea && cd .. && sudo rm -rf android/.gradle", "clean:metro": "rm -rf $TMPDIR/metro-* && rm -rf $TMPDIR/haste-map-* && watchman watch-del-all", "clean:cache": "yarn cache clean", "reset:metro": "npx react-native-clean-project --remove-metro-cache", "nuke": "yarn clean:node && yarn clean:ios && yarn clean:android && yarn clean:metro && yarn clean:cache && yarn install && cd ios && bundle install && RCT_NEW_ARCH_ENABLED=1 bundle exec pod install && cd .. && cd android && ./gradlew clean && cd ..", "install-deps": "yarn install && cd ios && bundle install && RCT_NEW_ARCH_ENABLED=1 bundle exec pod install && cd .."}, "lint-staged": {"*.ts": ["prettier --write", "eslint --fix"], "*.tsx": ["prettier --write", "eslint --fix"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"@bam.tech/react-native-image-resizer": "^3.0.7", "@gorhom/bottom-sheet": "^5.0.6", "@invertase/react-native-apple-authentication": "^2.4.0", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/image-editor": "^2.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-google-signin/google-signin": "13.1.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@sayem314/react-native-keep-awake": "^1.3.1", "@scorescast/analytics": "1.0.0", "@scorescast/http-client": "1.0.0", "@tanstack/react-query": "^5.74.4", "add": "^2.0.6", "i18next": "^24.2.0", "lottie-ios": "4.5.0", "lottie-react-native": "^7.2.2", "newrelic-react-native-agent": "^1.5.2", "react": "19.0.0", "react-i18next": "^15.2.0", "react-native": "0.79.1", "react-native-appsflyer": "^6.16.2", "react-native-camera-kit": "^14.2.0", "react-native-date-picker": "^5.0.12", "react-native-device-info": "^14.0.4", "react-native-fbsdk-next": "^13.0.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "2.25.0", "react-native-image-picker": "5.6.1", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.3.0", "react-native-lottie-splash-screen": "^1.1.2", "react-native-mmkv": "^3.2.0", "react-native-permissions": "^5.3.0", "react-native-popover-view": "^6.1.0", "react-native-reanimated": "3.17.3", "react-native-safe-area-context": "^5.0.0", "react-native-screens": "^4.4.0", "react-native-svg": "^15.10.1", "react-native-wonderpush": "^2.3.0", "react-native-wonderpush-fcm": "^1.0.9", "yarn": "^1.22.22", "zustand": "^5.0.2"}, "workspaces": ["packages/*"], "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@eslint/js": "^9.17.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.1", "@react-native/eslint-config": "0.79.1", "@react-native/metro-config": "0.79.1", "@react-native/typescript-config": "0.79.1", "@scorescast/design-system": "1.0.0", "@scorescast/formatters-constants": "1.0.0", "@scorescast/translations": "1.0.0", "@types/jest": "^29.5.13", "@types/node": "^20.11.30", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "babel-jest": "^29.6.3", "eslint": "^9.0.0-beta.1", "eslint-plugin-react": "^7.37.2", "globals": "^15.13.0", "husky": "^9.1.7", "jest": "^29.6.3", "lint-staged": "^15.2.11", "prettier": "^3.4.2", "prop-types": "^15.8.1", "react-native-clean-project": "^4.0.1", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "19.0.0", "reactotron-react-native": "^5.1.12", "typescript": "5.0.4", "typescript-eslint": "^8.18.0"}, "engines": {"node": ">=18"}, "main": "index.js", "license": "MIT"}