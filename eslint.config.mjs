import globals from 'globals';
import pluginJs from '@eslint/js';
import tseslint from 'typescript-eslint';
import pluginReact from 'eslint-plugin-react';

/** @type {import('eslint').Linter.Config[]} */
export default [
  { files: ['**/*.{mjs,cjs,ts,jsx,tsx}'] },
  {
    ignores: [
      '.node_modules/*',
      'jest.config.js',
      'babel.config.js',
      'metro.config.js',
      'eslint.config.mjs',
      '.prettierrc.js',
    ],
  },
  { languageOptions: { globals: globals.browser } },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  pluginReact.configs.flat.recommended,
  {
    rules: {
      eqeqeq: 'off',
      'no-unused-vars': 'off',
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      'prefer-const': ['error', { ignoreReadBeforeAssign: true }],
      'no-multi-spaces': [
        'error',
        {
          ignoreEOLComments: false, // Optional: set to true to allow multiple spaces before end-of-line comments
          exceptions: {
            VariableDeclarator: true, // Optional: set to true if you want to allow extra spaces around variable declarations
          },
        },
      ],
    },
  },
];
